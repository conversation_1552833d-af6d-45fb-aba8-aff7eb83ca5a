/**
 * Base error class for all custom errors in the application
 * Provides a consistent structure for error handling
 */
export abstract class BaseError extends Error {
  public name: string;
  public httpCode: number;
  public readonly isOperational: boolean;
  public readonly timestamp: Date;
  public details?: any;

  constructor(
    name: string,
    httpCode: number,
    description: string,
    isOperational: boolean = true,
    details?: any
  ) {
    super(description);

    Object.setPrototypeOf(this, new.target.prototype);

    this.name = name;
    this.httpCode = httpCode;
    this.isOperational = isOperational;
    this.timestamp = new Date();
    this.details = details;

    Error.captureStackTrace(this);
  }

  /**
   * Convert error to JSON format for API responses
   */
  toJSON() {
    return {
      success: false,
      error: {
        name: this.name,
        message: this.message,
        code: this.httpCode,
        timestamp: this.timestamp.toISOString(),
        ...(this.details && { details: this.details })
      }
    };
  }

  /**
   * Convert error to Colyseus client error format
   */
  toColyseusError() {
    return {
      code: this.httpCode,
      message: this.message,
      details: this.details
    };
  }
}
