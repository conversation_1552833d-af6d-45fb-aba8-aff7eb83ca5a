"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GardenPlotState = void 0;
const schema_1 = require("@colyseus/schema");
class GardenPlotState extends schema_1.Schema {
    // Future: Could add 'health', 'fertilizer_effect', etc.
    constructor(plotId, plotTemplateId = 'default_plot', seedId = '', plantTimestamp = 0, growthStage = 0, lastWateredTimestamp = 0) {
        super();
        this.plotId = ''; // Unique ID for this plot instance in the garden
        this.plotTemplateId = 'default_plot'; // e.g., 'small_plot', 'large_plot', 'hydroponic'
        this.seedId = ''; // ID of the seed planted, empty if no seed
        this.plantTimestamp = 0; // Unix timestamp when seed was planted
        this.growthStage = 0; // e.g., 0: empty, 1: seeded, 2: sprouting, 3: growing, 4: mature/harvestable
        this.lastWateredTimestamp = 0; // Unix timestamp
        this.plotId = plotId;
        this.plotTemplateId = plotTemplateId;
        this.seedId = seedId;
        this.plantTimestamp = plantTimestamp;
        this.growthStage = growthStage;
        this.lastWateredTimestamp = lastWateredTimestamp;
    }
}
exports.GardenPlotState = GardenPlotState;
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], GardenPlotState.prototype, "plotId", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], GardenPlotState.prototype, "plotTemplateId", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], GardenPlotState.prototype, "seedId", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], GardenPlotState.prototype, "plantTimestamp", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], GardenPlotState.prototype, "growthStage", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], GardenPlotState.prototype, "lastWateredTimestamp", void 0);
