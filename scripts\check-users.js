const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkUsers() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🔍 Checking users in database...');
    
    const [rows] = await connection.query('SELECT id, username FROM users LIMIT 20');
    console.log('📊 Users found:');
    console.table(rows);
    
    console.log('\n🔍 Checking user_currencies...');
    const [currencies] = await connection.query('SELECT user_id, gold_coins, diamonds FROM user_currencies LIMIT 10');
    console.log('💰 User currencies:');
    console.table(currencies);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await connection.end();
  }
}

checkUsers();
