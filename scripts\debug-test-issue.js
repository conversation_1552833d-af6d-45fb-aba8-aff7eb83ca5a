// Debug script to identify the exact issue with the test
const path = require('path');
require('ts-node').register({
  project: path.join(__dirname, '..', 'tsconfig.json')
});

const SpaceService = require('../src/services/SpaceService').default;
const mysql = require('mysql2/promise');
require('dotenv').config();

async function debugTestIssue() {
  const testUserId = '2';
  
  try {
    console.log('🔍 Debugging test issue...');
    console.log(`Test User ID: ${testUserId} (type: ${typeof testUserId})`);
    
    // Test 1: Direct database query
    console.log('\n1️⃣ Direct database query for home items...');
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: Number(process.env.DB_PORT) || 3306,
    });
    
    const [directItems] = await connection.query(
      'SELECT * FROM home_items WHERE owner_user_id = ? ORDER BY placed_at DESC',
      [testUserId]
    );
    console.log(`   Direct query found ${directItems.length} items`);
    
    // Test 2: SpaceService method
    console.log('\n2️⃣ SpaceService.getFullPrivateSpaceData...');
    const spaceData = await SpaceService.getFullPrivateSpaceData(testUserId);
    console.log(`   SpaceService found ${spaceData.homeItems.length} items`);
    console.log(`   SpaceService found ${spaceData.gardenPlots.length} garden plots`);
    
    // Test 3: Compare the results
    console.log('\n3️⃣ Comparing results...');
    console.log(`   Direct query: ${directItems.length} items`);
    console.log(`   SpaceService: ${spaceData.homeItems.length} items`);
    
    if (directItems.length !== spaceData.homeItems.length) {
      console.log('   ❌ MISMATCH DETECTED!');
      
      // Check if there's a user ID type issue
      console.log('\n4️⃣ Checking user ID types...');
      const [numericQuery] = await connection.query(
        'SELECT * FROM home_items WHERE owner_user_id = ? ORDER BY placed_at DESC',
        [parseInt(testUserId, 10)]
      );
      console.log(`   Numeric user ID query found ${numericQuery.length} items`);
      
      const [stringQuery] = await connection.query(
        'SELECT * FROM home_items WHERE owner_user_id = ? ORDER BY placed_at DESC',
        [testUserId.toString()]
      );
      console.log(`   String user ID query found ${stringQuery.length} items`);
      
      // Check the actual data types in the database
      console.log('\n5️⃣ Checking actual data in database...');
      const [allItems] = await connection.query(
        'SELECT owner_user_id, item_instance_id, item_template_id FROM home_items LIMIT 5'
      );
      console.log('   Sample items:');
      allItems.forEach((item, index) => {
        console.log(`     ${index + 1}. owner_user_id: ${item.owner_user_id} (type: ${typeof item.owner_user_id})`);
      });
    } else {
      console.log('   ✅ Results match!');
    }
    
    // Test 4: Place a new item and immediately check
    console.log('\n6️⃣ Testing place and immediate retrieval...');
    const placeResult = await SpaceService.placeHomeItem(testUserId, 'basic_chair', 999, 999, 0, false);
    console.log(`   Place result: ${JSON.stringify(placeResult.success)}`);
    
    if (placeResult.success) {
      const newItemId = placeResult.itemInstance.item_instance_id;
      console.log(`   Placed item ID: ${newItemId}`);
      
      // Immediate retrieval
      const immediateData = await SpaceService.getFullPrivateSpaceData(testUserId);
      const foundItem = immediateData.homeItems.find(item => item.item_instance_id === newItemId);
      
      console.log(`   Immediate retrieval found ${immediateData.homeItems.length} total items`);
      console.log(`   New item found: ${foundItem ? 'YES' : 'NO'}`);
      
      if (foundItem) {
        console.log(`   New item details: ${foundItem.item_template_id} at (${foundItem.pos_x}, ${foundItem.pos_y})`);
      }
    }
    
    await connection.end();
    console.log('\n✅ Debug complete!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

debugTestIssue();
