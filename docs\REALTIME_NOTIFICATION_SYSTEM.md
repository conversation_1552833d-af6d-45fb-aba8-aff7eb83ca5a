# 实时好友通知系统文档

## 概述

实时好友通知系统为 2D 虚拟世界游戏提供了完整的实时通知机制。系统通过专用的 Colyseus 通知房间和全局通知服务，实现好友关系变化、在线状态更新和自定义消息的实时推送。

## 核心特性

### 🎯 主要功能
- **实时好友通知**: 好友请求、接受、拒绝、移除等事件的即时通知
- **在线状态跟踪**: 实时监控用户在线状态和好友状态变化
- **全局通知房间**: 专用的 WebSocket 房间处理所有通知
- **通知持久化**: 数据库存储确保离线用户也能收到通知
- **通知管理**: 已读/未读状态管理和批量操作
- **自定义通知**: 支持房间邀请、系统消息等自定义通知类型

### 🔔 支持的通知类型
- **friend_request_received**: 收到好友请求
- **friend_request_accepted**: 好友请求被接受
- **friend_request_declined**: 好友请求被拒绝
- **friend_removed**: 被移除好友关系
- **user_blocked**: 被用户屏蔽
- **user_online/offline**: 好友上线/下线状态
- **room_invitation**: 房间邀请
- **system_message**: 系统消息

## 系统架构

### 核心组件

#### 1. NotificationService
全局通知服务，负责通知的创建、发送和管理
- 实时通知推送
- 在线状态管理
- 通知持久化
- 好友状态广播

#### 2. NotificationRoom
专用的 Colyseus 房间，处理实时通知连接
- 用户会话管理
- 实时消息广播
- 通知状态同步
- 在线好友列表

#### 3. 数据库集成
- 通知持久化存储
- 过期通知自动清理
- 已读状态管理
- 索引优化查询

## 数据库结构

### notifications 表
```sql
CREATE TABLE `notifications` (
  `id` VARCHAR(255) NOT NULL PRIMARY KEY,
  `type` ENUM(...) NOT NULL,
  `from_user_id` BIGINT NULL,
  `to_user_id` BIGINT NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `message` TEXT NOT NULL,
  `data` JSON NULL,
  `is_read` BOOLEAN NOT NULL DEFAULT FALSE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `expires_at` TIMESTAMP NOT NULL,
  -- 索引优化
  INDEX `idx_to_user_created` (`to_user_id`, `created_at`),
  INDEX `idx_to_user_read` (`to_user_id`, `is_read`)
);
```

## API 接口

### 通知管理

#### 获取用户通知
```http
GET /api/notifications?limit=50&offset=0
Authorization: Bearer TOKEN
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "notif_1234567890_abc123",
        "type": "friend_request_received",
        "fromUserId": "123",
        "title": "New Friend Request",
        "message": "Alice sent you a friend request",
        "data": { "fromUserId": "123", "fromUsername": "Alice" },
        "isRead": false,
        "createdAt": "2024-01-01T12:00:00Z"
      }
    ],
    "hasMore": false,
    "total": 1
  }
}
```

#### 标记通知为已读
```http
PUT /api/notifications/:notificationId/read
Authorization: Bearer TOKEN
```

#### 标记所有通知为已读
```http
PUT /api/notifications/read-all
Authorization: Bearer TOKEN
```

#### 发送自定义通知
```http
POST /api/notifications/send
Authorization: Bearer TOKEN
Content-Type: application/json

{
  "toUserId": "456",
  "type": "room_invitation",
  "title": "Room Invitation",
  "message": "You're invited to my home!",
  "data": { "roomId": "home_123" }
}
```

### 在线状态

#### 获取用户在线状态
```http
GET /api/notifications/online-status/:userId
Authorization: Bearer TOKEN
```

#### 获取所有在线用户
```http
GET /api/notifications/online-users
Authorization: Bearer TOKEN
```

## 客户端使用

### JavaScript 客户端示例

#### 1. 连接通知房间
```javascript
import { Client } from 'colyseus.js';

const client = new Client('ws://localhost:2567');

// 连接到通知房间
const notificationRoom = await client.joinOrCreate('notification', {
  token: userToken,
  uid: userId
});

console.log('Connected to notification room');
```

#### 2. 监听实时通知
```javascript
// 监听通知消息
notificationRoom.onMessage('notification', (notification) => {
  console.log('New notification:', notification);
  
  // 显示通知UI
  showNotificationToUser({
    title: notification.title,
    message: notification.message,
    type: notification.type,
    from: notification.fromUsername
  });
});

// 监听好友在线状态
notificationRoom.onMessage('friend_online', (data) => {
  console.log(`Friend ${data.username} came online`);
  updateFriendStatus(data.userId, true);
});

notificationRoom.onMessage('friend_offline', (data) => {
  console.log(`Friend ${data.username} went offline`);
  updateFriendStatus(data.userId, false);
});
```

#### 3. 通知管理操作
```javascript
// 标记通知为已读
notificationRoom.send('mark_notification_read', {
  notificationId: 'notif_1234567890_abc123'
});

// 标记所有通知为已读
notificationRoom.send('mark_all_notifications_read');

// 获取通知列表
notificationRoom.send('get_notifications', {
  limit: 20,
  offset: 0
});

// 获取在线好友
notificationRoom.send('get_online_friends');
```

#### 4. 发送自定义通知
```javascript
// 发送房间邀请
notificationRoom.send('send_direct_notification', {
  toUserId: 'friend_user_id',
  type: 'room_invitation',
  title: 'Room Invitation',
  message: 'Come visit my home!',
  data: { roomId: 'home_123', roomType: 'home' }
});
```

#### 5. 更新用户状态
```javascript
// 更新当前房间状态
notificationRoom.send('update_status', {
  currentRoom: 'home_123'
});
```

## 好友系统集成

### 自动通知触发

好友系统的所有操作都会自动触发相应的实时通知：

#### 发送好友请求
```javascript
// API调用
const response = await fetch('/api/friends/request', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ targetUserId: 'friend_id' })
});

// 自动触发通知给目标用户
// 类型: 'friend_request_received'
```

#### 响应好友请求
```javascript
// 接受好友请求
const response = await fetch('/api/friends/response', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ 
    senderUserId: 'sender_id',
    response: 'accept'
  })
});

// 自动触发通知给发送者
// 类型: 'friend_request_accepted'
```

## 通知房间状态管理

### 房间状态结构
```javascript
class NotificationRoomState {
  users: Map<string, NotificationUserState>; // 在线用户
  onlineCount: number; // 在线用户数量
}

class NotificationUserState {
  userId: string;
  username: string;
  isOnline: boolean;
  lastSeen: number;
  currentRoom: string; // 当前所在房间
}
```

### 状态同步
- 用户加入/离开自动更新状态
- 实时广播好友状态变化
- 定期清理不活跃用户

## 性能优化

### 缓存策略
- 在线用户状态内存缓存
- 好友关系查询缓存
- 通知模板缓存

### 数据库优化
- 复合索引优化查询
- 自动清理过期通知
- 分页查询减少数据传输

### 网络优化
- 增量状态更新
- 消息批量发送
- 连接池管理

## 监控和维护

### 通知统计
```javascript
// 获取通知发送统计
const stats = await NotificationService.getNotificationStats();
console.log({
  totalSent: stats.totalSent,
  totalRead: stats.totalRead,
  averageReadTime: stats.averageReadTime
});
```

### 清理维护
```javascript
// 清理过期通知
const cleanedCount = await NotificationService.cleanupExpiredNotifications();
console.log(`清理了 ${cleanedCount} 个过期通知`);
```

### 在线用户监控
```javascript
// 获取在线用户统计
const onlineUsers = NotificationService.getOnlineUsers();
console.log(`当前在线用户: ${onlineUsers.length}`);
```

## 错误处理

### 常见错误类型
- **ConnectionError**: 通知房间连接失败
- **AuthenticationError**: 身份验证失败
- **NotificationSendError**: 通知发送失败
- **DatabaseError**: 数据库操作失败

### 错误恢复机制
- 自动重连机制
- 离线通知缓存
- 失败重试策略
- 降级处理方案

## 安全考虑

### 权限控制
- 通知发送权限验证
- 好友关系验证
- 敏感信息过滤

### 防滥用机制
- 通知频率限制
- 内容过滤
- 用户举报系统

## 扩展功能

### 未来增强
- **推送通知**: 移动端推送集成
- **通知模板**: 可配置的通知模板
- **通知分组**: 按类型分组管理
- **通知优先级**: 重要通知优先处理
- **通知统计**: 详细的通知分析
- **多语言支持**: 国际化通知内容

### 集成扩展
- **邮件通知**: 重要事件邮件提醒
- **短信通知**: 关键操作短信验证
- **第三方集成**: Discord、微信等平台集成

## 测试

### 运行测试
```bash
npm run test-notifications
```

### 客户端示例
```bash
npm install axios colyseus.js  # 安装依赖
node examples/realtime-notification-client-example.js
```

实时好友通知系统现在已经完全集成到你的游戏服务端中，提供了完整的实时通知机制，支持好友系统的所有事件，并为未来的功能扩展做好了准备。
