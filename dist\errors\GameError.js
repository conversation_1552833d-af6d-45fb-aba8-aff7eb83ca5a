"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CharacterNameExistsError = exports.CharacterNotFoundError = exports.CharacterError = exports.InvalidItemPlacementError = exports.ItemNotFoundError = exports.ItemError = exports.InvalidPlayerActionError = exports.PlayerNotFoundError = exports.PlayerError = exports.InvalidRoomStateError = exports.RoomAccessDeniedError = exports.RoomFullError = exports.RoomNotFoundError = exports.RoomError = exports.GameError = void 0;
const BaseError_1 = require("./BaseError");
/**
 * Game specific errors
 */
class GameError extends BaseError_1.BaseError {
    constructor(message, httpCode = 400, details) {
        super('GameError', httpCode, message, true, details);
    }
}
exports.GameError = GameError;
/**
 * Room related errors
 */
class RoomError extends GameError {
    constructor(message, roomId) {
        super(message, 400, { roomId });
        this.name = 'RoomError';
    }
}
exports.RoomError = RoomError;
/**
 * Room not found error
 */
class RoomNotFoundError extends RoomError {
    constructor(roomId) {
        super(`Room '${roomId}' not found`, roomId);
        this.name = 'RoomNotFoundError';
        this.httpCode = 404;
    }
}
exports.RoomNotFoundError = RoomNotFoundError;
/**
 * Room full error
 */
class RoomFullError extends RoomError {
    constructor(roomId, maxClients) {
        super(`Room '${roomId}' is full (max ${maxClients} clients)`, roomId);
        this.name = 'RoomFullError';
        this.details = { ...this.details, maxClients };
    }
}
exports.RoomFullError = RoomFullError;
/**
 * Room access denied error
 */
class RoomAccessDeniedError extends RoomError {
    constructor(roomId, reason) {
        let message = `Access denied to room '${roomId}'`;
        if (reason) {
            message += `: ${reason}`;
        }
        super(message, roomId);
        this.name = 'RoomAccessDeniedError';
        this.httpCode = 403;
        if (reason) {
            this.details = { ...this.details, reason };
        }
    }
}
exports.RoomAccessDeniedError = RoomAccessDeniedError;
/**
 * Invalid room state error
 */
class InvalidRoomStateError extends RoomError {
    constructor(roomId, currentState, expectedState) {
        const message = `Invalid room state: current '${currentState}', expected '${expectedState}'`;
        super(message, roomId);
        this.name = 'InvalidRoomStateError';
        this.details = { ...this.details, currentState, expectedState };
    }
}
exports.InvalidRoomStateError = InvalidRoomStateError;
/**
 * Player related errors
 */
class PlayerError extends GameError {
    constructor(message, playerId) {
        super(message, 400, { playerId });
        this.name = 'PlayerError';
    }
}
exports.PlayerError = PlayerError;
/**
 * Player not found error
 */
class PlayerNotFoundError extends PlayerError {
    constructor(playerId) {
        super(`Player '${playerId}' not found`, playerId);
        this.name = 'PlayerNotFoundError';
        this.httpCode = 404;
    }
}
exports.PlayerNotFoundError = PlayerNotFoundError;
/**
 * Invalid player action error
 */
class InvalidPlayerActionError extends PlayerError {
    constructor(action, reason, playerId) {
        const message = `Invalid player action '${action}': ${reason}`;
        super(message, playerId);
        this.name = 'InvalidPlayerActionError';
        this.details = { ...this.details, action, reason };
    }
}
exports.InvalidPlayerActionError = InvalidPlayerActionError;
/**
 * Item related errors
 */
class ItemError extends GameError {
    constructor(message, itemId) {
        super(message, 400, { itemId });
        this.name = 'ItemError';
    }
}
exports.ItemError = ItemError;
/**
 * Item not found error
 */
class ItemNotFoundError extends ItemError {
    constructor(itemId) {
        super(`Item '${itemId}' not found`, itemId);
        this.name = 'ItemNotFoundError';
        this.httpCode = 404;
    }
}
exports.ItemNotFoundError = ItemNotFoundError;
/**
 * Invalid item placement error
 */
class InvalidItemPlacementError extends ItemError {
    constructor(itemId, reason, position) {
        const message = `Cannot place item '${itemId}': ${reason}`;
        super(message, itemId);
        this.name = 'InvalidItemPlacementError';
        if (position) {
            this.details = { ...this.details, position };
        }
    }
}
exports.InvalidItemPlacementError = InvalidItemPlacementError;
/**
 * Character related errors
 */
class CharacterError extends GameError {
    constructor(message, characterId) {
        super(message, 400, { characterId });
        this.name = 'CharacterError';
    }
}
exports.CharacterError = CharacterError;
/**
 * Character not found error
 */
class CharacterNotFoundError extends CharacterError {
    constructor(characterId) {
        super(`Character '${characterId}' not found`, characterId);
        this.name = 'CharacterNotFoundError';
        this.httpCode = 404;
    }
}
exports.CharacterNotFoundError = CharacterNotFoundError;
/**
 * Character name already exists error
 */
class CharacterNameExistsError extends CharacterError {
    constructor(characterName) {
        super(`Character name '${characterName}' already exists`);
        this.name = 'CharacterNameExistsError';
        this.httpCode = 409;
        this.details = { characterName };
    }
}
exports.CharacterNameExistsError = CharacterNameExistsError;
