const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixGardenPlotsTable() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🌱 Fixing garden_plots table structure...');
    
    // Check if plot_instance_id column exists
    try {
      const [columns] = await connection.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'garden_plots' AND COLUMN_NAME = 'plot_instance_id'
      `, [process.env.DB_NAME]);
      
      if (columns.length === 0) {
        console.log('⚠️  plot_instance_id column missing, adding it...');
        
        // Add the missing column
        await connection.query(`
          ALTER TABLE garden_plots 
          ADD COLUMN plot_instance_id VARCHAR(36) NOT NULL DEFAULT '' AFTER id
        `);
        
        // Update existing rows with generated UUIDs
        await connection.query(`
          UPDATE garden_plots 
          SET plot_instance_id = CONCAT('plot_', id, '_', UNIX_TIMESTAMP(), '_', SUBSTRING(MD5(RAND()), 1, 8))
          WHERE plot_instance_id = ''
        `);
        
        console.log('✅ Added plot_instance_id column and populated existing rows');
      } else {
        console.log('✅ plot_instance_id column already exists');
      }
    } catch (error) {
      console.log('❌ Error checking/adding plot_instance_id column:', error.message);
    }
    
    // Check if seed_item_id column exists (should be there from planting system)
    try {
      const [columns] = await connection.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'garden_plots' AND COLUMN_NAME = 'seed_item_id'
      `, [process.env.DB_NAME]);
      
      if (columns.length === 0) {
        console.log('⚠️  seed_item_id column missing, adding it...');
        
        await connection.query(`
          ALTER TABLE garden_plots 
          ADD COLUMN seed_item_id VARCHAR(255) NULL AFTER plot_instance_id,
          ADD COLUMN growth_stage INT NOT NULL DEFAULT 0 AFTER seed_item_id,
          ADD COLUMN plant_timestamp TIMESTAMP NULL AFTER growth_stage,
          ADD COLUMN last_watered_timestamp TIMESTAMP NULL AFTER plant_timestamp
        `);
        
        console.log('✅ Added missing garden plot columns');
      } else {
        console.log('✅ Garden plot columns already exist');
      }
    } catch (error) {
      console.log('❌ Error checking/adding garden plot columns:', error.message);
    }
    
    // Ensure test user has some garden plots
    try {
      const [existingPlots] = await connection.query(
        'SELECT COUNT(*) as count FROM garden_plots WHERE user_id = 2'
      );
      
      if (existingPlots[0].count === 0) {
        console.log('🌱 Creating default garden plots for test user...');
        
        // Create 12 garden plots (first 4 unlocked by default)
        for (let i = 0; i < 12; i++) {
          const plotInstanceId = `plot_user2_${i}_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;
          const isUnlocked = i < 4; // First 4 plots are unlocked by default
          
          await connection.query(`
            INSERT INTO garden_plots (
              user_id, 
              plot_index, 
              plot_instance_id,
              is_unlocked, 
              unlock_level_required, 
              unlock_cost_gold, 
              unlock_cost_diamonds,
              growth_stage,
              created_at,
              updated_at
            ) VALUES (2, ?, ?, ?, ?, ?, ?, 0, NOW(), NOW())
          `, [i, plotInstanceId, isUnlocked, i + 1, i * 100, i * 5]);
        }
        
        console.log('✅ Created 12 garden plots for test user (4 unlocked)');
      } else {
        console.log(`✅ Test user already has ${existingPlots[0].count} garden plots`);
      }
    } catch (error) {
      console.log('❌ Error creating garden plots:', error.message);
    }
    
    console.log('\n🎉 Garden plots table has been fixed!');
    
  } catch (error) {
    console.error('❌ Error fixing garden plots table:', error);
  } finally {
    await connection.end();
  }
}

fixGardenPlotsTable();
