const mysql = require('mysql2/promise');
require('dotenv').config();

async function setupPlantingSystem() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🌱 Setting up Planting System...');
    
    // 1. Create database tables
    console.log('📊 Creating database tables...');
    const schemaSQL = require('fs').readFileSync('sql/planting_system_schema.sql', 'utf8');
    const statements = schemaSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (const statement of statements) {
      try {
        await connection.query(statement);
      } catch (error) {
        if (!error.message.includes('already exists')) {
          console.error('Error executing statement:', error.message);
        }
      }
    }
    console.log('✅ Database tables created');

    // 2. Insert flower seed templates
    console.log('🌸 Adding flower seed templates...');
    
    const seedTemplates = [
      // Common seeds
      {
        seed_id: 'sunflower_seed',
        seed_name: 'Sunflower Seed',
        description: 'A bright and cheerful sunflower that grows quickly.',
        rarity: 'common',
        growth_time_minutes: 30,
        required_level: 1,
        base_yield: 2,
        max_yield: 4,
        water_intervals: 2,
        fertilizer_boost: 1.2,
        rare_drop_chance: 0.05
      },
      {
        seed_id: 'rose_seed',
        seed_name: 'Rose Seed',
        description: 'Classic red roses that symbolize love and beauty.',
        rarity: 'common',
        growth_time_minutes: 45,
        required_level: 1,
        base_yield: 1,
        max_yield: 3,
        water_intervals: 3,
        fertilizer_boost: 1.3,
        rare_drop_chance: 0.08
      },
      {
        seed_id: 'tulip_seed',
        seed_name: 'Tulip Seed',
        description: 'Colorful tulips that bloom in spring.',
        rarity: 'common',
        growth_time_minutes: 25,
        required_level: 1,
        base_yield: 2,
        max_yield: 3,
        water_intervals: 2,
        fertilizer_boost: 1.1,
        rare_drop_chance: 0.03
      },
      
      // Uncommon seeds
      {
        seed_id: 'lavender_seed',
        seed_name: 'Lavender Seed',
        description: 'Fragrant purple lavender with calming properties.',
        rarity: 'uncommon',
        growth_time_minutes: 60,
        required_level: 3,
        base_yield: 2,
        max_yield: 4,
        water_intervals: 2,
        fertilizer_boost: 1.4,
        rare_drop_chance: 0.12
      },
      {
        seed_id: 'orchid_seed',
        seed_name: 'Orchid Seed',
        description: 'Exotic orchids with delicate beauty.',
        rarity: 'uncommon',
        growth_time_minutes: 90,
        required_level: 5,
        base_yield: 1,
        max_yield: 2,
        water_intervals: 4,
        fertilizer_boost: 1.5,
        rare_drop_chance: 0.15
      },
      
      // Rare seeds
      {
        seed_id: 'moonflower_seed',
        seed_name: 'Moonflower Seed',
        description: 'Mystical flowers that bloom under moonlight.',
        rarity: 'rare',
        growth_time_minutes: 120,
        required_level: 8,
        base_yield: 1,
        max_yield: 3,
        water_intervals: 3,
        fertilizer_boost: 1.8,
        rare_drop_chance: 0.25
      },
      {
        seed_id: 'crystal_lily_seed',
        seed_name: 'Crystal Lily Seed',
        description: 'Rare lilies with crystalline petals.',
        rarity: 'rare',
        growth_time_minutes: 150,
        required_level: 10,
        base_yield: 1,
        max_yield: 2,
        water_intervals: 4,
        fertilizer_boost: 2.0,
        rare_drop_chance: 0.30
      },
      
      // Epic seeds
      {
        seed_id: 'phoenix_flower_seed',
        seed_name: 'Phoenix Flower Seed',
        description: 'Legendary flowers that burn with eternal flame.',
        rarity: 'epic',
        growth_time_minutes: 240,
        required_level: 15,
        base_yield: 1,
        max_yield: 2,
        water_intervals: 5,
        fertilizer_boost: 2.5,
        rare_drop_chance: 0.40
      },
      
      // Legendary seeds
      {
        seed_id: 'world_tree_seed',
        seed_name: 'World Tree Seed',
        description: 'The ultimate seed that grows into a magnificent world tree.',
        rarity: 'legendary',
        growth_time_minutes: 480,
        required_level: 20,
        base_yield: 1,
        max_yield: 1,
        water_intervals: 6,
        fertilizer_boost: 3.0,
        rare_drop_chance: 0.50
      }
    ];

    for (const seed of seedTemplates) {
      await connection.query(
        `INSERT IGNORE INTO flower_seeds 
         (seed_id, seed_name, description, rarity, growth_time_minutes, required_level, 
          base_yield, max_yield, water_intervals, fertilizer_boost, rare_drop_chance)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [seed.seed_id, seed.seed_name, seed.description, seed.rarity, seed.growth_time_minutes,
         seed.required_level, seed.base_yield, seed.max_yield, seed.water_intervals,
         seed.fertilizer_boost, seed.rare_drop_chance]
      );
    }
    console.log(`✅ Added ${seedTemplates.length} flower seed templates`);

    // 3. Add seed items to the items table
    console.log('📦 Adding seed items to inventory system...');
    
    for (const seed of seedTemplates) {
      await connection.query(
        `INSERT IGNORE INTO items 
         (item_id, item_name, description, item_type, category, is_stackable, max_stack_size, data)
         VALUES (?, ?, ?, 'seed', 'planting', TRUE, 99, ?)`,
        [seed.seed_id, seed.seed_name, seed.description, JSON.stringify({
          rarity: seed.rarity,
          required_level: seed.required_level,
          growth_time_minutes: seed.growth_time_minutes
        })]
      );
      
      // Add corresponding flower items
      const flowerItemId = `flower_${seed.seed_id}`;
      const flowerName = seed.seed_name.replace(' Seed', '');
      await connection.query(
        `INSERT IGNORE INTO items 
         (item_id, item_name, description, item_type, category, is_stackable, max_stack_size, data)
         VALUES (?, ?, ?, 'flower', 'planting', TRUE, 99, ?)`,
        [flowerItemId, flowerName, `Beautiful ${flowerName.toLowerCase()} harvested from your garden.`, JSON.stringify({
          rarity: seed.rarity,
          base_value: seed.base_yield * 10
        })]
      );
    }
    console.log('✅ Added seed and flower items to inventory system');

    // 4. Add fertilizer and tool items
    console.log('🧪 Adding fertilizers and tools...');
    
    const tools = [
      {
        item_id: 'basic_fertilizer',
        item_name: 'Basic Fertilizer',
        description: 'Improves plant growth and yield.',
        item_type: 'tool',
        category: 'planting',
        data: { fertilizer_type: 'basic', boost_multiplier: 1.2 }
      },
      {
        item_id: 'premium_fertilizer',
        item_name: 'Premium Fertilizer',
        description: 'High-quality fertilizer for better results.',
        item_type: 'tool',
        category: 'planting',
        data: { fertilizer_type: 'premium', boost_multiplier: 1.5 }
      },
      {
        item_id: 'magic_fertilizer',
        item_name: 'Magic Fertilizer',
        description: 'Enchanted fertilizer with powerful effects.',
        item_type: 'tool',
        category: 'planting',
        data: { fertilizer_type: 'magic', boost_multiplier: 2.0 }
      },
      {
        item_id: 'watering_can',
        item_name: 'Watering Can',
        description: 'Essential tool for watering plants.',
        item_type: 'tool',
        category: 'planting',
        data: { tool_type: 'watering_can', durability: 100 }
      },
      {
        item_id: 'plant_fiber',
        item_name: 'Plant Fiber',
        description: 'Natural fiber obtained from harvesting plants.',
        item_type: 'material',
        category: 'planting',
        data: { material_type: 'fiber', base_value: 5 }
      },
      {
        item_id: 'rare_essence',
        item_name: 'Rare Essence',
        description: 'Magical essence extracted from rare flowers.',
        item_type: 'material',
        category: 'planting',
        data: { material_type: 'essence', base_value: 50 }
      },
      {
        item_id: 'magic_pollen',
        item_name: 'Magic Pollen',
        description: 'Enchanted pollen with mystical properties.',
        item_type: 'material',
        category: 'planting',
        data: { material_type: 'pollen', base_value: 75 }
      },
      {
        item_id: 'golden_nectar',
        item_name: 'Golden Nectar',
        description: 'Precious nectar from legendary flowers.',
        item_type: 'material',
        category: 'planting',
        data: { material_type: 'nectar', base_value: 100 }
      }
    ];

    for (const tool of tools) {
      await connection.query(
        `INSERT IGNORE INTO items 
         (item_id, item_name, description, item_type, category, is_stackable, max_stack_size, data)
         VALUES (?, ?, ?, ?, ?, TRUE, 99, ?)`,
        [tool.item_id, tool.item_name, tool.description, tool.item_type, tool.category, JSON.stringify(tool.data)]
      );
    }
    console.log(`✅ Added ${tools.length} planting tools and materials`);

    // 5. Add planting achievements
    console.log('🏆 Adding planting achievements...');
    
    const achievements = [
      {
        achievement_id: 'first_plant',
        achievement_name: 'Green Thumb',
        description: 'Plant your first seed',
        achievement_type: 'plants_grown',
        target_value: 1,
        reward_gold: 100,
        reward_diamonds: 0
      },
      {
        achievement_id: 'plant_master',
        achievement_name: 'Plant Master',
        description: 'Plant 100 seeds',
        achievement_type: 'plants_grown',
        target_value: 100,
        reward_gold: 1000,
        reward_diamonds: 10
      },
      {
        achievement_id: 'harvest_expert',
        achievement_name: 'Harvest Expert',
        description: 'Complete 50 harvests',
        achievement_type: 'harvests_completed',
        target_value: 50,
        reward_gold: 500,
        reward_diamonds: 5
      },
      {
        achievement_id: 'rare_collector',
        achievement_name: 'Rare Collector',
        description: 'Find 10 rare materials',
        achievement_type: 'rare_finds',
        target_value: 10,
        reward_gold: 2000,
        reward_diamonds: 20
      },
      {
        achievement_id: 'level_10',
        achievement_name: 'Experienced Gardener',
        description: 'Reach planting level 10',
        achievement_type: 'level_reached',
        target_value: 10,
        reward_gold: 1500,
        reward_diamonds: 15
      }
    ];

    for (const achievement of achievements) {
      await connection.query(
        `INSERT IGNORE INTO planting_achievements 
         (achievement_id, achievement_name, description, achievement_type, target_value, reward_gold, reward_diamonds)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [achievement.achievement_id, achievement.achievement_name, achievement.description,
         achievement.achievement_type, achievement.target_value, achievement.reward_gold, achievement.reward_diamonds]
      );
    }
    console.log(`✅ Added ${achievements.length} planting achievements`);

    // 6. Give test users some starting seeds
    console.log('🎁 Giving test users starting seeds...');
    
    const testUserIds = [1, 2, 3, 4, 5];
    const startingSeeds = ['sunflower_seed', 'rose_seed', 'tulip_seed'];
    
    for (const userId of testUserIds) {
      for (const seedId of startingSeeds) {
        try {
          await connection.query(
            `INSERT IGNORE INTO user_inventory (user_id, item_id, quantity, acquired_at)
             VALUES (?, ?, 5, CURRENT_TIMESTAMP)`,
            [userId, seedId]
          );
        } catch (error) {
          // Ignore duplicate key errors
          if (!error.message.includes('Duplicate entry')) {
            console.error(`Error adding seed ${seedId} to user ${userId}:`, error.message);
          }
        }
      }
      
      // Add some fertilizers
      try {
        await connection.query(
          `INSERT IGNORE INTO user_inventory (user_id, item_id, quantity, acquired_at)
           VALUES (?, 'basic_fertilizer', 10, CURRENT_TIMESTAMP)`,
          [userId]
        );
      } catch (error) {
        // Ignore duplicate key errors
      }
    }
    console.log('✅ Added starting seeds to test users');

    console.log('🎉 Planting System setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`- Created database tables for planting system`);
    console.log(`- Added ${seedTemplates.length} flower seed templates`);
    console.log(`- Added ${tools.length} tools and materials`);
    console.log(`- Added ${achievements.length} achievements`);
    console.log(`- Gave starting seeds to ${testUserIds.length} test users`);
    
  } catch (error) {
    console.error('❌ Error setting up planting system:', error);
  } finally {
    await connection.end();
  }
}

setupPlantingSystem();
