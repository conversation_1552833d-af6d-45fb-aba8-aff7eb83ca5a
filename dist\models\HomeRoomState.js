"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeRoomState = void 0;
const schema_1 = require("@colyseus/schema");
const PlayerState_1 = require("./PlayerState"); // Assuming PlayerState is in the same directory or adjust path
const HomeItemState_1 = require("./HomeItemState"); // Assuming HomeItemState is in the same directory
class HomeRoomState extends schema_1.Schema {
    constructor(ownerUid, accessLevel = 'private', homeBackgroundId = 'default_background') {
        super();
        this.ownerUid = ''; // UID of the player who owns this home
        this.homeBackgroundId = 'default_background'; // Identifier for the home's background
        this.players = new schema_1.MapSchema();
        this.furniture = new schema_1.MapSchema(); // Furniture and other items placed in the home
        this.accessLevel = 'private'; // Who can access this home
        this.ownerUid = ownerUid;
        this.accessLevel = accessLevel;
        this.homeBackgroundId = homeBackgroundId;
    }
}
exports.HomeRoomState = HomeRoomState;
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], HomeRoomState.prototype, "ownerUid", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], HomeRoomState.prototype, "homeBackgroundId", void 0);
__decorate([
    (0, schema_1.type)({ map: PlayerState_1.PlayerState }),
    __metadata("design:type", Object)
], HomeRoomState.prototype, "players", void 0);
__decorate([
    (0, schema_1.type)({ map: HomeItemState_1.HomeItemState }),
    __metadata("design:type", Object)
], HomeRoomState.prototype, "furniture", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], HomeRoomState.prototype, "accessLevel", void 0);
