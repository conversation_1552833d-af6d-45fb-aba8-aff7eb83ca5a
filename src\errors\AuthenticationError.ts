import { BaseError } from './BaseError';

/**
 * Authentication related errors
 */
export class AuthenticationError extends BaseError {
  constructor(message: string = 'Authentication failed', details?: any) {
    super('AuthenticationError', 401, message, true, details);
  }
}

/**
 * Invalid credentials error
 */
export class InvalidCredentialsError extends AuthenticationError {
  constructor(message: string = 'Invalid username or password') {
    super(message);
    this.name = 'InvalidCredentialsError';
  }
}

/**
 * Token expired error
 */
export class TokenExpiredError extends AuthenticationError {
  constructor(message: string = 'Token has expired') {
    super(message);
    this.name = 'TokenExpiredError';
  }
}

/**
 * Invalid token error
 */
export class InvalidTokenError extends AuthenticationError {
  constructor(message: string = 'Invalid or malformed token') {
    super(message);
    this.name = 'InvalidTokenError';
  }
}

/**
 * Missing token error
 */
export class MissingTokenError extends AuthenticationError {
  constructor(message: string = 'Authentication token is required') {
    super(message);
    this.name = 'MissingTokenError';
  }
}

/**
 * Token revoked error
 */
export class TokenRevokedError extends AuthenticationError {
  constructor(message: string = 'Token has been revoked') {
    super(message);
    this.name = 'TokenRevokedError';
  }
}
