# 物品栏系统文档

## 概述

物品栏系统是 2D 虚拟世界游戏的核心功能之一，为玩家提供完整的物品管理体验。系统支持分类管理、高级筛选、排序、搜索，以及丰富的物品操作功能。

## 核心特性

### 🎯 主要功能
- **分类管理**: 按时装、家具、道具、材料、任务物品分类
- **高级筛选**: 支持多维度筛选和搜索
- **智能排序**: 多种排序方式，支持自定义默认排序
- **物品操作**: 使用、出售、赠送、装备、锁定等操作
- **容量管理**: 可扩展的物品栏容量系统
- **操作记录**: 完整的物品操作历史追踪

### 📦 物品分类

#### 1. 时装 (Fashion)
- **类型**: `wearable`
- **子分类**: 上装、下装、连衣裙、发型、鞋履、翅膀、套装
- **特殊功能**: 装备/卸下、预览搭配

#### 2. 家具 (Furniture)  
- **类型**: `furniture`
- **子分类**: 室内家具、园艺装饰、墙纸地板、主题系列
- **特殊功能**: 放置/收回、家园预览

#### 3. 道具 (Tools)
- **类型**: `tool`, `consumable`
- **子分类**: 种子、成长道具、经验卡、增益道具
- **特殊功能**: 使用消耗、效果应用

#### 4. 材料 (Materials)
- **类型**: `collectible`
- **子分类**: 花朵、露珠、矿石、制作卷轴
- **特殊功能**: 合成制作、任务提交

#### 5. 任务 (Quest)
- **类型**: `quest_item`
- **子分类**: 任务道具、信件、任务凭证
- **特殊功能**: 不可出售、不可赠送

## 数据库结构

### 核心表

#### user_inventory (扩展版)
```sql
CREATE TABLE `user_inventory` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `item_id` VARCHAR(255) NOT NULL,
  `quantity` BIGINT UNSIGNED NOT NULL DEFAULT 1,
  `is_locked` BOOLEAN NOT NULL DEFAULT FALSE,
  `is_new` BOOLEAN NOT NULL DEFAULT TRUE,
  `is_equipped` BOOLEAN NOT NULL DEFAULT FALSE,
  `custom_data` JSON,
  `acquired_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### user_inventory_config
```sql
CREATE TABLE `user_inventory_config` (
  `user_id` BIGINT NOT NULL PRIMARY KEY,
  `max_capacity` INT UNSIGNED NOT NULL DEFAULT 100,
  `default_sort_order` ENUM(...) NOT NULL DEFAULT 'acquired_time_desc',
  `auto_sort_enabled` BOOLEAN NOT NULL DEFAULT FALSE,
  `show_new_items_first` BOOLEAN NOT NULL DEFAULT TRUE
);
```

#### inventory_operations_log
```sql
CREATE TABLE `inventory_operations_log` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `item_id` VARCHAR(255) NOT NULL,
  `operation_type` ENUM('add', 'remove', 'use', 'sell', 'gift_send', 'gift_receive', 'equip', 'unequip', 'lock', 'unlock', 'destroy'),
  `quantity_change` INT NOT NULL,
  `quantity_before` BIGINT UNSIGNED NOT NULL,
  `quantity_after` BIGINT UNSIGNED NOT NULL,
  `operation_source` VARCHAR(255),
  `metadata` JSON
);
```

## API 接口

### 物品栏查询

#### 获取增强物品栏
```http
GET /api/inventory/:userId
```

**查询参数:**
- `category`: 物品分类 (FASHION, FURNITURE, TOOLS, MATERIALS, QUEST)
- `subcategory`: 子分类
- `itemType`: 物品类型
- `isNew`: 是否新物品
- `isEquipped`: 是否已装备
- `isLocked`: 是否已锁定
- `search`: 搜索关键词
- `rarity`: 稀有度
- `sortOrder`: 排序方式
- `page`: 页码
- `pageSize`: 每页数量

**响应示例:**
```json
{
  "success": true,
  "data": {
    "items": [...],
    "totalItems": 45,
    "totalPages": 3,
    "currentPage": 1,
    "hasNewItems": true,
    "capacityUsed": 45,
    "maxCapacity": 100,
    "categories": {
      "fashion": 12,
      "furniture": 8,
      "tools": 15,
      "materials": 10,
      "quest": 0
    }
  }
}
```

#### 搜索物品
```http
GET /api/inventory/:userId/search?q=关键词
```

#### 获取最近获得的物品
```http
GET /api/inventory/:userId/recent?limit=10
```

### 物品栏配置

#### 获取配置
```http
GET /api/inventory/:userId/config
```

#### 更新配置
```http
PUT /api/inventory/:userId/config
```

**请求体:**
```json
{
  "max_capacity": 150,
  "default_sort_order": "name_asc",
  "auto_sort_enabled": true,
  "show_new_items_first": false
}
```

### 物品操作

#### 标记物品为已查看
```http
POST /api/inventory/:userId/mark-seen
```

#### 锁定/解锁物品
```http
POST /api/inventory/:userId/items/:itemId/lock
```

#### 装备/卸下物品
```http
POST /api/inventory/:userId/items/:itemId/equip
```

#### 使用消耗品
```http
POST /api/inventory/:userId/items/:itemId/use
```

#### 出售物品
```http
POST /api/inventory/:userId/items/:itemId/sell
```

#### 销毁物品
```http
POST /api/inventory/:userId/items/:itemId/destroy
```

#### 扩展物品栏容量
```http
POST /api/inventory/:userId/expand
```

### 历史记录

#### 获取操作历史
```http
GET /api/inventory/:userId/history
```

## 排序选项

### 支持的排序方式
- `acquired_time_desc`: 获得时间降序 (最新优先)
- `acquired_time_asc`: 获得时间升序 (最旧优先)
- `name_asc`: 名称升序 (A-Z)
- `name_desc`: 名称降序 (Z-A)
- `quantity_desc`: 数量降序 (多的优先)
- `quantity_asc`: 数量升序 (少的优先)
- `rarity_desc`: 稀有度降序 (稀有优先)

## 物品属性增强

### 增强属性
每个物品都会被增强以下属性：
- `rarity`: 稀有度 (common, uncommon, rare, epic, legendary)
- `can_use`: 是否可使用
- `can_sell`: 是否可出售
- `can_gift`: 是否可赠送
- `can_equip`: 是否可装备

### 物品状态
- `is_locked`: 是否锁定 (防止误操作)
- `is_new`: 是否为新获得 (显示"新"标记)
- `is_equipped`: 是否已装备

## 使用示例

### JavaScript 客户端示例

```javascript
// 获取物品栏
const inventory = await fetch('/api/inventory/player123').then(r => r.json());

// 按分类筛选
const fashionItems = await fetch('/api/inventory/player123?category=FASHION').then(r => r.json());

// 搜索物品
const searchResults = await fetch('/api/inventory/player123/search?q=dress').then(r => r.json());

// 使用物品
await fetch('/api/inventory/player123/items/health_potion/use', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ quantity: 1 })
});

// 装备物品
await fetch('/api/inventory/player123/items/fairy_dress/equip', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ equipped: true })
});

// 锁定物品
await fetch('/api/inventory/player123/items/rare_item/lock', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ locked: true })
});
```

## Schema 模型

### InventorySystemState
用于 Colyseus 房间的实时状态同步：

```typescript
export class InventorySystemState extends Schema {
  @type('string') userId: string;
  @type(InventoryConfigState) config;
  @type({ map: InventoryCategoryState }) categories;
  @type({ map: InventoryItemState }) allItems;
  @type('number') totalItems: number;
  @type('number') capacityUsed: number;
  @type('boolean') hasNewItems: boolean;
}
```

## 性能优化

### 缓存策略
- 物品栏配置缓存
- 分类统计缓存
- 最近操作缓存

### 数据库优化
- 复合索引优化查询
- 分页查询减少数据传输
- 操作日志异步记录

### 前端优化
- 虚拟滚动处理大量物品
- 懒加载物品图标
- 本地缓存减少请求

## 扩展功能

### 未来增强
- **套装管理**: 一键穿戴套装
- **收藏夹**: 标记常用物品
- **自动整理**: 智能物品排序
- **批量操作**: 批量出售/使用
- **物品预设**: 保存装备方案
- **交易系统**: 玩家间物品交易

### 与其他系统集成
- **商店系统**: 购买物品自动添加到物品栏
- **任务系统**: 任务奖励和物品需求
- **家园系统**: 家具放置和收回
- **花园系统**: 种子和道具使用
- **社交系统**: 物品赠送好友

## 测试

### 运行测试
```bash
npm run test-inventory
```

### 客户端示例
```bash
npm install axios  # 如果未安装
node examples/inventory-client-example.js
```

## 错误处理

### 常见错误类型
- **ItemNotFound**: 物品不存在
- **InsufficientQuantity**: 数量不足
- **ItemLocked**: 物品已锁定
- **CapacityExceeded**: 容量不足
- **InvalidOperation**: 无效操作

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "name": "ShopError",
    "message": "Item is locked and cannot be used",
    "code": 400,
    "details": {
      "itemId": "rare_sword",
      "operation": "use"
    }
  }
}
```

物品栏系统现在已经完全集成到你的游戏服务端中，提供了完整的物品管理功能，支持你需求中的所有核心特性，并为未来的功能扩展预留了接口。
