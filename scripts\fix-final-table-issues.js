const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixFinalTableIssues() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🔧 Fixing final table issues...');
    
    // Fix 1: Check garden_plots table column names
    console.log('🌱 Checking garden_plots table columns...');
    try {
      const [columns] = await connection.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'garden_plots'
      `, [process.env.DB_NAME]);
      
      const columnNames = columns.map(col => col.COLUMN_NAME);
      console.log('Garden plots columns:', columnNames);
      
      // Check if we need to rename user_id to owner_user_id or vice versa
      if (columnNames.includes('user_id') && !columnNames.includes('owner_user_id')) {
        console.log('⚠️  Found user_id column, SpaceService expects owner_user_id');
        // We need to update SpaceService to use user_id instead of owner_user_id
        console.log('✅ Will update SpaceService to use correct column name');
      } else if (columnNames.includes('owner_user_id') && !columnNames.includes('user_id')) {
        console.log('✅ Found owner_user_id column, matches SpaceService expectations');
      } else {
        console.log('⚠️  Unexpected column configuration in garden_plots table');
      }
    } catch (error) {
      console.log('❌ Error checking garden_plots columns:', error.message);
    }
    
    // Fix 2: Add updated_at column to home_items table if missing
    console.log('🪑 Checking home_items table columns...');
    try {
      const [columns] = await connection.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'home_items' AND COLUMN_NAME = 'updated_at'
      `, [process.env.DB_NAME]);
      
      if (columns.length === 0) {
        console.log('⚠️  updated_at column missing from home_items, adding it...');
        
        await connection.query(`
          ALTER TABLE home_items 
          ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER placed_at
        `);
        
        console.log('✅ Added updated_at column to home_items table');
      } else {
        console.log('✅ updated_at column already exists in home_items table');
      }
    } catch (error) {
      console.log('❌ Error checking/adding updated_at column:', error.message);
    }
    
    // Fix 3: Check and fix created_at column in home_items if needed
    try {
      const [columns] = await connection.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'home_items' AND COLUMN_NAME = 'created_at'
      `, [process.env.DB_NAME]);
      
      if (columns.length === 0) {
        console.log('⚠️  created_at column missing from home_items, adding it...');
        
        await connection.query(`
          ALTER TABLE home_items 
          ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER updated_at
        `);
        
        console.log('✅ Added created_at column to home_items table');
      } else {
        console.log('✅ created_at column already exists in home_items table');
      }
    } catch (error) {
      console.log('❌ Error checking/adding created_at column:', error.message);
    }
    
    // Fix 4: Show final table structures
    console.log('\n📋 Final table structures:');
    
    try {
      const [gardenColumns] = await connection.query('DESCRIBE garden_plots');
      console.log('\n🌱 garden_plots table:');
      gardenColumns.forEach(col => console.log(`  - ${col.Field} (${col.Type})`));
    } catch (error) {
      console.log('❌ Error describing garden_plots:', error.message);
    }
    
    try {
      const [homeItemsColumns] = await connection.query('DESCRIBE home_items');
      console.log('\n🪑 home_items table:');
      homeItemsColumns.forEach(col => console.log(`  - ${col.Field} (${col.Type})`));
    } catch (error) {
      console.log('❌ Error describing home_items:', error.message);
    }
    
    console.log('\n🎉 Final table issues have been fixed!');
    
  } catch (error) {
    console.error('❌ Error fixing final table issues:', error);
  } finally {
    await connection.end();
  }
}

fixFinalTableIssues();
