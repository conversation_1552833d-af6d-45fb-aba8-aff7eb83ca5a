"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorFactory = exports.QuestError = exports.CharacterNameExistsError = exports.CharacterNotFoundError = exports.CharacterError = exports.InvalidItemPlacementError = exports.ItemNotFoundError = exports.ItemError = exports.InvalidPlayerActionError = exports.PlayerNotFoundError = exports.PlayerError = exports.InvalidRoomStateError = exports.RoomAccessDeniedError = exports.RoomFullError = exports.RoomNotFoundError = exports.RoomError = exports.GameError = exports.DatabaseTimeoutError = exports.DatabaseConstraintError = exports.DatabaseTransactionError = exports.DatabaseQueryError = exports.DatabaseConnectionError = exports.DatabaseError = exports.InvalidStateError = exports.OperationNotAllowedError = exports.InsufficientResourcesError = exports.RateLimitError = exports.PermissionDeniedError = exports.ConflictError = exports.NotFoundError = exports.BusinessLogicError = exports.InvalidFormatError = exports.FieldLengthError = exports.InvalidFieldValueError = exports.InvalidFieldTypeError = exports.MissingFieldError = exports.ValidationError = exports.TokenRevokedError = exports.MissingTokenError = exports.InvalidTokenError = exports.TokenExpiredError = exports.InvalidCredentialsError = exports.AuthenticationError = exports.BaseError = void 0;
// Base error
var BaseError_1 = require("./BaseError");
Object.defineProperty(exports, "BaseError", { enumerable: true, get: function () { return BaseError_1.BaseError; } });
// Authentication errors
var AuthenticationError_1 = require("./AuthenticationError");
Object.defineProperty(exports, "AuthenticationError", { enumerable: true, get: function () { return AuthenticationError_1.AuthenticationError; } });
Object.defineProperty(exports, "InvalidCredentialsError", { enumerable: true, get: function () { return AuthenticationError_1.InvalidCredentialsError; } });
Object.defineProperty(exports, "TokenExpiredError", { enumerable: true, get: function () { return AuthenticationError_1.TokenExpiredError; } });
Object.defineProperty(exports, "InvalidTokenError", { enumerable: true, get: function () { return AuthenticationError_1.InvalidTokenError; } });
Object.defineProperty(exports, "MissingTokenError", { enumerable: true, get: function () { return AuthenticationError_1.MissingTokenError; } });
Object.defineProperty(exports, "TokenRevokedError", { enumerable: true, get: function () { return AuthenticationError_1.TokenRevokedError; } });
// Validation errors
var ValidationError_1 = require("./ValidationError");
Object.defineProperty(exports, "ValidationError", { enumerable: true, get: function () { return ValidationError_1.ValidationError; } });
Object.defineProperty(exports, "MissingFieldError", { enumerable: true, get: function () { return ValidationError_1.MissingFieldError; } });
Object.defineProperty(exports, "InvalidFieldTypeError", { enumerable: true, get: function () { return ValidationError_1.InvalidFieldTypeError; } });
Object.defineProperty(exports, "InvalidFieldValueError", { enumerable: true, get: function () { return ValidationError_1.InvalidFieldValueError; } });
Object.defineProperty(exports, "FieldLengthError", { enumerable: true, get: function () { return ValidationError_1.FieldLengthError; } });
Object.defineProperty(exports, "InvalidFormatError", { enumerable: true, get: function () { return ValidationError_1.InvalidFormatError; } });
// Business logic errors
var BusinessLogicError_1 = require("./BusinessLogicError");
Object.defineProperty(exports, "BusinessLogicError", { enumerable: true, get: function () { return BusinessLogicError_1.BusinessLogicError; } });
Object.defineProperty(exports, "NotFoundError", { enumerable: true, get: function () { return BusinessLogicError_1.NotFoundError; } });
Object.defineProperty(exports, "ConflictError", { enumerable: true, get: function () { return BusinessLogicError_1.ConflictError; } });
Object.defineProperty(exports, "PermissionDeniedError", { enumerable: true, get: function () { return BusinessLogicError_1.PermissionDeniedError; } });
Object.defineProperty(exports, "RateLimitError", { enumerable: true, get: function () { return BusinessLogicError_1.RateLimitError; } });
Object.defineProperty(exports, "InsufficientResourcesError", { enumerable: true, get: function () { return BusinessLogicError_1.InsufficientResourcesError; } });
Object.defineProperty(exports, "OperationNotAllowedError", { enumerable: true, get: function () { return BusinessLogicError_1.OperationNotAllowedError; } });
Object.defineProperty(exports, "InvalidStateError", { enumerable: true, get: function () { return BusinessLogicError_1.InvalidStateError; } });
// Database errors
var DatabaseError_1 = require("./DatabaseError");
Object.defineProperty(exports, "DatabaseError", { enumerable: true, get: function () { return DatabaseError_1.DatabaseError; } });
Object.defineProperty(exports, "DatabaseConnectionError", { enumerable: true, get: function () { return DatabaseError_1.DatabaseConnectionError; } });
Object.defineProperty(exports, "DatabaseQueryError", { enumerable: true, get: function () { return DatabaseError_1.DatabaseQueryError; } });
Object.defineProperty(exports, "DatabaseTransactionError", { enumerable: true, get: function () { return DatabaseError_1.DatabaseTransactionError; } });
Object.defineProperty(exports, "DatabaseConstraintError", { enumerable: true, get: function () { return DatabaseError_1.DatabaseConstraintError; } });
Object.defineProperty(exports, "DatabaseTimeoutError", { enumerable: true, get: function () { return DatabaseError_1.DatabaseTimeoutError; } });
// Game specific errors
var GameError_1 = require("./GameError");
Object.defineProperty(exports, "GameError", { enumerable: true, get: function () { return GameError_1.GameError; } });
Object.defineProperty(exports, "RoomError", { enumerable: true, get: function () { return GameError_1.RoomError; } });
Object.defineProperty(exports, "RoomNotFoundError", { enumerable: true, get: function () { return GameError_1.RoomNotFoundError; } });
Object.defineProperty(exports, "RoomFullError", { enumerable: true, get: function () { return GameError_1.RoomFullError; } });
Object.defineProperty(exports, "RoomAccessDeniedError", { enumerable: true, get: function () { return GameError_1.RoomAccessDeniedError; } });
Object.defineProperty(exports, "InvalidRoomStateError", { enumerable: true, get: function () { return GameError_1.InvalidRoomStateError; } });
Object.defineProperty(exports, "PlayerError", { enumerable: true, get: function () { return GameError_1.PlayerError; } });
Object.defineProperty(exports, "PlayerNotFoundError", { enumerable: true, get: function () { return GameError_1.PlayerNotFoundError; } });
Object.defineProperty(exports, "InvalidPlayerActionError", { enumerable: true, get: function () { return GameError_1.InvalidPlayerActionError; } });
Object.defineProperty(exports, "ItemError", { enumerable: true, get: function () { return GameError_1.ItemError; } });
Object.defineProperty(exports, "ItemNotFoundError", { enumerable: true, get: function () { return GameError_1.ItemNotFoundError; } });
Object.defineProperty(exports, "InvalidItemPlacementError", { enumerable: true, get: function () { return GameError_1.InvalidItemPlacementError; } });
Object.defineProperty(exports, "CharacterError", { enumerable: true, get: function () { return GameError_1.CharacterError; } });
Object.defineProperty(exports, "CharacterNotFoundError", { enumerable: true, get: function () { return GameError_1.CharacterNotFoundError; } });
Object.defineProperty(exports, "CharacterNameExistsError", { enumerable: true, get: function () { return GameError_1.CharacterNameExistsError; } });
// Quest specific errors
var QuestError_1 = require("./QuestError");
Object.defineProperty(exports, "QuestError", { enumerable: true, get: function () { return QuestError_1.QuestError; } });
// Import classes for use in ErrorFactory
const AuthenticationError_2 = require("./AuthenticationError");
const ValidationError_2 = require("./ValidationError");
const BusinessLogicError_2 = require("./BusinessLogicError");
const DatabaseError_2 = require("./DatabaseError");
/**
 * Error factory for creating specific error types
 */
class ErrorFactory {
    /**
     * Create authentication error based on error type
     */
    static createAuthError(type, message) {
        switch (type) {
            case 'invalid_credentials':
                return new AuthenticationError_2.InvalidCredentialsError(message);
            case 'token_expired':
                return new AuthenticationError_2.TokenExpiredError(message);
            case 'invalid_token':
                return new AuthenticationError_2.InvalidTokenError(message);
            case 'missing_token':
                return new AuthenticationError_2.MissingTokenError(message);
            case 'token_revoked':
                return new AuthenticationError_2.TokenRevokedError(message);
            default:
                return new AuthenticationError_2.AuthenticationError(message);
        }
    }
    /**
     * Create validation error for missing field
     */
    static createMissingFieldError(fieldName, message) {
        return new ValidationError_2.MissingFieldError(fieldName, message);
    }
    /**
     * Create validation error for invalid field type
     */
    static createInvalidTypeError(fieldName, expectedType, actualType) {
        return new ValidationError_2.InvalidFieldTypeError(fieldName, expectedType, actualType);
    }
    /**
     * Create not found error
     */
    static createNotFoundError(resource, identifier) {
        return new BusinessLogicError_2.NotFoundError(resource, identifier);
    }
    /**
     * Create conflict error
     */
    static createConflictError(resource, field, value) {
        return new BusinessLogicError_2.ConflictError(resource, field, value);
    }
    /**
     * Create permission denied error
     */
    static createPermissionError(action, resource) {
        return new BusinessLogicError_2.PermissionDeniedError(action, resource);
    }
    /**
     * Create database error
     */
    static createDatabaseError(type, details) {
        switch (type) {
            case 'connection':
                return new DatabaseError_2.DatabaseConnectionError(details?.message);
            case 'query':
                return new DatabaseError_2.DatabaseQueryError(details?.query, details?.originalError);
            case 'transaction':
                return new DatabaseError_2.DatabaseTransactionError(details?.message, details);
            case 'constraint':
                return new DatabaseError_2.DatabaseConstraintError(details?.constraint, details?.table);
            case 'timeout':
                return new DatabaseError_2.DatabaseTimeoutError(details?.timeout);
            default:
                return new DatabaseError_2.DatabaseError(details?.message, details);
        }
    }
}
exports.ErrorFactory = ErrorFactory;
