"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestError = void 0;
const BaseError_1 = require("./BaseError");
/**
 * Quest-related error class
 * Used for quest system specific errors
 */
class QuestError extends BaseError_1.BaseError {
    constructor(message, httpCode = 400, details) {
        super('QuestError', httpCode, message, true, details);
    }
    /**
     * Quest not found error
     */
    static questNotFound(questId) {
        return new QuestError(`Quest with ID ${questId} not found`, 404, { questId });
    }
    /**
     * Quest step not found error
     */
    static questStepNotFound(questId, stepOrder) {
        return new QuestError(`Quest step ${stepOrder} not found for quest ${questId}`, 404, { questId, stepOrder });
    }
    /**
     * Quest already started error
     */
    static questAlreadyStarted(questId) {
        return new QuestError(`Quest ${questId} has already been started`, 409, { questId });
    }
    /**
     * Quest already completed error
     */
    static questAlreadyCompleted(questId) {
        return new QuestError(`Quest ${questId} has already been completed`, 409, { questId });
    }
    /**
     * Quest not started error
     */
    static questNotStarted(questId) {
        return new QuestError(`Quest ${questId} has not been started`, 400, { questId });
    }
    /**
     * Invalid quest step progression error
     */
    static invalidStepProgression(questId, currentStep, targetStep) {
        return new QuestError(`Cannot progress from step ${currentStep} to step ${targetStep} in quest ${questId}`, 400, { questId, currentStep, targetStep });
    }
    /**
     * Quest step not completed error
     */
    static stepNotCompleted(questId, stepOrder) {
        return new QuestError(`Step ${stepOrder} in quest ${questId} is not completed`, 400, { questId, stepOrder });
    }
    /**
     * Invalid quest action error
     */
    static invalidQuestAction(action, questId) {
        return new QuestError(`Invalid quest action '${action}' for quest ${questId}`, 400, { action, questId });
    }
    /**
     * Quest data validation error
     */
    static invalidQuestData(message, details) {
        return new QuestError(`Invalid quest data: ${message}`, 400, details);
    }
    /**
     * NPC response not found error
     */
    static npcResponseNotFound(stepId, npcId, responseType) {
        return new QuestError(`NPC response not found for step ${stepId}, NPC ${npcId}, type ${responseType}`, 404, { stepId, npcId, responseType });
    }
}
exports.QuestError = QuestError;
