// This file is deprecated and replaced by src/index.ts
// The main server configuration is now handled in src/index.ts
// This file is kept for backward compatibility but should not be used

import config from "@colyseus/tools";
import { monitor } from "@colyseus/monitor";
import { playground } from "@colyseus/playground";

/**
 * @deprecated This configuration is deprecated. Use src/index.ts instead.
 * Import your Room files
 */
import { MyRoom } from "./rooms/MyRoom";

export default config({

    initializeGameServer: (gameServer) => {
        /**
         * @deprecated Define your room handlers in src/index.ts instead
         */
        console.warn('WARNING: app.config.ts is deprecated. Room definitions should be in src/index.ts');
        gameServer.define('my_room', MyRoom);

    },

    initializeExpress: (app) => {
        /**
         * @deprecated Express routes should be defined in src/index.ts instead
         */
        console.warn('WARNING: app.config.ts is deprecated. Express configuration should be in src/index.ts');

        app.get("/hello_world", (req, res) => {
            res.send("It's time to kick ass and chew bubblegum!");
        });

        /**
         * Use @colyseus/playground
         * (It is not recommended to expose this route in a production environment)
         */
        if (process.env.NODE_ENV !== "production") {
            app.use("/", playground());
        }

        /**
         * Use @colyseus/monitor
         * It is recommended to protect this route with a password
         * Read more: https://docs.colyseus.io/tools/monitor/#restrict-access-to-the-panel-using-a-password
         */
        app.use("/monitor", monitor());
    },


    beforeListen: () => {
        /**
         * @deprecated Before listen logic should be in src/index.ts instead
         */
        console.warn('WARNING: app.config.ts is deprecated. Before listen logic should be in src/index.ts');
    }
});
