import { describe, it, before, after } from 'mocha';
import { expect } from 'chai';
import InventorySystemService from '../src/services/InventorySystemService';
import InventoryService from '../src/services/InventoryService';
import CurrencyService from '../src/services/CurrencyService';
import { ShopError } from '../src/errors';

describe('Inventory System Tests', () => {
  const testUserId = '1'; // Use numeric user ID that exists in database
  const testItemId = 'test_item_1'; // Use fixed test item ID

  before(async () => {
    // Initialize test user with currency
    try {
      await CurrencyService.initializeUserCurrency(testUserId, 10000, 500);
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        console.log('⚠️  User currency already exists, skipping initialization');
      } else {
        throw error;
      }
    }

    // Try to create default inventory config, skip if already exists
    try {
      await InventorySystemService.createDefaultInventoryConfig(testUserId);
    } catch (error) {
      if (error.message.includes("doesn't exist")) {
        console.log('⚠️  Skipping inventory config creation - table missing');
      } else if (error.code === 'ER_DUP_ENTRY') {
        console.log('⚠️  Inventory config already exists, skipping creation');
      } else {
        throw error;
      }
    }
  });

  describe('Inventory Configuration', () => {
    it('should get inventory configuration', async () => {
      const config = await InventorySystemService.getInventoryConfig(testUserId);
      expect(config).to.exist;
      expect(config.user_id.toString()).to.equal(testUserId);
      expect(config.max_capacity).to.be.a('number');
      expect(config.default_sort_order).to.be.a('string');
    });

    it('should update inventory configuration', async () => {
      const updates = {
        max_capacity: 150,
        default_sort_order: 'name_asc' as const,
        auto_sort_enabled: true
      };

      const updatedConfig = await InventorySystemService.updateInventoryConfig(testUserId, updates);
      expect(updatedConfig.max_capacity).to.equal(150);
      expect(updatedConfig.default_sort_order).to.equal('name_asc');
      expect(updatedConfig.auto_sort_enabled).to.equal(1); // MySQL returns 1 for true
    });

    it('should expand inventory capacity', async () => {
      const initialConfig = await InventorySystemService.getInventoryConfig(testUserId);
      const initialCapacity = initialConfig.max_capacity;

      const expandedConfig = await InventorySystemService.expandInventoryCapacity(
        testUserId,
        20,
        100 // cost in diamonds
      );

      expect(expandedConfig.max_capacity).to.equal(initialCapacity + 20);
    });
  });

  describe('Enhanced Inventory Management', () => {
    it('should get enhanced inventory with default settings', async () => {
      const inventory = await InventorySystemService.getEnhancedInventory(testUserId);

      expect(inventory).to.exist;
      expect(inventory.items).to.be.an('array');
      expect(inventory.totalItems).to.be.a('number');
      expect(inventory.totalPages).to.be.a('number');
      expect(inventory.currentPage).to.equal(1);
      expect(inventory.capacityUsed).to.be.a('number');
      expect(inventory.maxCapacity).to.be.a('number');
      expect(inventory.categories).to.be.an('object');
    });

    it('should get enhanced inventory with filters', async () => {
      const filter = {
        category: 'FASHION' as const,
        isNew: true
      };

      const inventory = await InventorySystemService.getEnhancedInventory(
        testUserId,
        filter
      );

      expect(inventory).to.exist;
      expect(inventory.items).to.be.an('array');
    });

    it('should get enhanced inventory with pagination', async () => {
      const pagination = {
        page: 1,
        pageSize: 10
      };

      const inventory = await InventorySystemService.getEnhancedInventory(
        testUserId,
        {},
        'acquired_time_desc',
        pagination
      );

      expect(inventory).to.exist;
      expect(inventory.currentPage).to.equal(1);
      expect(inventory.items.length).to.be.at.most(10);
    });

    it('should search inventory items', async () => {
      // First add a test item if it doesn't exist
      try {
        await InventoryService.addItem(testUserId, testItemId, 1);
      } catch (error) {
        // Item might not exist in items table, skip this test
        if (error.message.includes('not found')) {
          console.log('Skipping search test - test item not found');
          return;
        }
      }

      const searchResults = await InventorySystemService.searchInventoryItems(
        testUserId,
        'test'
      );

      expect(searchResults).to.be.an('array');
    });

    it('should get recently acquired items', async () => {
      const recentItems = await InventorySystemService.getRecentlyAcquiredItems(testUserId, 5);

      expect(recentItems).to.be.an('array');
      expect(recentItems.length).to.be.at.most(5);
    });
  });

  describe('Item Operations', () => {
    it('should mark items as seen', async () => {
      // This should not throw an error
      await InventorySystemService.markItemsAsSeen(testUserId);

      // Mark specific items as seen
      await InventorySystemService.markItemsAsSeen(testUserId, [testItemId]);
    });

    it('should toggle item lock', async () => {
      // First ensure we have an item to lock
      try {
        await InventoryService.addItem(testUserId, testItemId, 1);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log('⚠️  Item already exists in inventory, continuing with test');
        } else {
          throw error;
        }
      }

      try {
        // Lock the item
        await InventorySystemService.toggleItemLock(testUserId, testItemId, true);

        // Unlock the item
        await InventorySystemService.toggleItemLock(testUserId, testItemId, false);
      } catch (error) {
        if (error.message.includes('not found')) {
          console.log('Skipping lock test - test item not found');
          return;
        }
        throw error;
      }
    });

    it('should handle item equip/unequip for wearable items', async () => {
      // This test would require a wearable item in the items table
      try {
        // Try to equip a non-existent item (should fail)
        await InventorySystemService.toggleItemEquip(testUserId, 'non_existent_item', true);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(ShopError);
      }
    });

    it('should handle item usage for consumable items', async () => {
      // This test would require a consumable item in the items table
      try {
        // Try to use a non-existent item (should fail)
        await InventorySystemService.useItem(testUserId, 'non_existent_item', 1);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(ShopError);
      }
    });

    it('should handle item selling', async () => {
      // This test would require a sellable item in the items table
      try {
        // Try to sell a non-existent item (should fail)
        await InventorySystemService.sellItem(testUserId, 'non_existent_item', 1);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(ShopError);
      }
    });

    it('should handle item destruction', async () => {
      // This test would require an item in the inventory
      try {
        // Try to destroy a non-existent item (should fail)
        await InventorySystemService.destroyItem(testUserId, 'non_existent_item', 1);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(ShopError);
      }
    });
  });

  describe('Category Management', () => {
    it('should get category counts', async () => {
      const categoryCounts = await InventorySystemService.getCategoryCounts(testUserId);

      expect(categoryCounts).to.be.an('object');
      expect(categoryCounts).to.have.property('fashion');
      expect(categoryCounts).to.have.property('furniture');
      expect(categoryCounts).to.have.property('tools');
      expect(categoryCounts).to.have.property('materials');
      expect(categoryCounts).to.have.property('quest');
    });
  });

  describe('Operation History', () => {
    it('should get inventory operation history', async () => {
      const history = await InventorySystemService.getInventoryOperationHistory(testUserId);

      expect(history).to.be.an('array');
      // History might be empty for a new test user
    });

    it('should get filtered operation history', async () => {
      const history = await InventorySystemService.getInventoryOperationHistory(
        testUserId,
        testItemId,
        'add',
        10
      );

      expect(history).to.be.an('array');
      expect(history.length).to.be.at.most(10);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid user ID', async () => {
      try {
        await InventorySystemService.getEnhancedInventory('invalid_user_id');
        // This might not throw an error but return empty inventory
        // The behavior depends on your implementation
      } catch (error) {
        expect(error).to.be.instanceOf(ShopError);
      }
    });

    it('should handle invalid item operations', async () => {
      try {
        await InventorySystemService.toggleItemLock(testUserId, 'non_existent_item', true);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(ShopError);
      }
    });

    it('should handle insufficient currency for expansion', async () => {
      // Try to expand with more diamonds than user has
      try {
        await InventorySystemService.expandInventoryCapacity(testUserId, 10, 10000);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(ShopError);
        expect(error.message).to.include('Insufficient');
      }
    });

    it('should handle locked item operations', async () => {
      // This test would require setting up a locked item first
      // For now, we'll test the error handling logic
      try {
        await InventorySystemService.useItem(testUserId, 'locked_item', 1);
        // This might not fail if the item doesn't exist
      } catch (error) {
        if (error instanceof ShopError) {
          // Expected behavior for locked or non-existent items
          expect(error.message).to.satisfy((msg: string) =>
            msg.includes('locked') || msg.includes('not found')
          );
        }
      }
    });
  });

  describe('Integration with Other Systems', () => {
    it('should work with currency system', async () => {
      const currency = await CurrencyService.getUserCurrency(testUserId);
      expect(currency).to.exist;
      expect(currency.user_id.toString()).to.equal(testUserId);
    });

    it('should work with basic inventory service', async () => {
      const basicInventory = await InventoryService.getUserInventory(testUserId);
      expect(basicInventory).to.be.an('array');
    });
  });
});
