import express, { Request, Response, NextFunction } from 'express';
import friendService, { CategorizedFriends } from '../services/friendService'; // Adjust path as necessary
import authService from '../services/authService'; // For actual token verification
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler';
import {
  MissingTokenError,
  InvalidTokenError,
  TokenExpiredError,
  ValidationError,
  MissingFieldError,
  InvalidFieldValueError
} from '../errors';

const router = express.Router();

// Placeholder/Actual auth middleware
// For actual JWT verification, integrate with authService.verifyToken
const verifyAuthToken = async (req: Request & { user?: { id: string } }, res: Response, next: NextFunction) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader?.split(' ')[1]; // Expecting "Bearer <token>"

        if (!token) {
            throw new MissingTokenError();
        }

        // Use the actual authService to verify the token
        const decodedPayload = await authService.verifyToken(token);

        if (decodedPayload && decodedPayload.userId) {
            req.user = { id: decodedPayload.userId.toString() }; // Attach user info (ensure userId is string)
            return next();
        } else {
            // This case means token was validly signed but might be invalid for other reasons
            // (e.g., not found in user_auth_tokens table, or payload structure wrong)
            throw new InvalidTokenError("Invalid token payload or token revoked");
        }
    } catch (error: any) {
        // Pass the error to the global error handler
        next(error);
    }
};


// POST /api/friends/request
router.post('/request', verifyAuthToken, asyncHandler(async (req: Request & { user?: { id: string } }, res: Response) => {
  const currentUserId = req.user!.id;
  const { targetUserId } = req.body;

  if (!targetUserId || typeof targetUserId !== 'string') {
    throw new MissingFieldError('targetUserId');
  }

  const result = await friendService.sendFriendRequest(currentUserId, targetUserId);
  if (result.success) {
    return res.status(200).json(result);
  } else {
    // Determine appropriate status code based on message if needed, e.g., 404 for not found, 409 for conflict
    return res.status(400).json(result); // Default to 400 for general failures
  }
}));

// POST /api/friends/response
router.post('/response', verifyAuthToken, async (req: Request & { user?: { id: string } }, res: Response) => {
  const currentUserId = req.user!.id;
  const { senderUserId, response } = req.body;

  if (!senderUserId || typeof senderUserId !== 'string' || !response || !['accept', 'decline'].includes(response)) {
    return res.status(400).json({ success: false, message: 'senderUserId (string) and response ("accept" | "decline") are required.' });
  }

  const result = await friendService.respondToFriendRequest(currentUserId, senderUserId, response);
  if (result.success) {
    return res.status(200).json(result);
  } else {
    return res.status(400).json(result);
  }
});

// GET /api/friends/
router.get('/', verifyAuthToken, async (req: Request & { user?: { id: string } }, res: Response) => {
  const currentUserId = req.user!.id;
  const status = req.query.status as 'accepted' | 'pending' | 'blocked' | undefined;

  if (status && !['accepted', 'pending', 'blocked'].includes(status)) {
    return res.status(400).json({ success: false, message: 'Invalid status filter. Allowed values: "accepted", "pending", "blocked".' });
  }

  const result = await friendService.listFriends(currentUserId, status);
  if (result.success) {
    return res.status(200).json(result);
  } else {
    return res.status(500).json(result); // Internal server error for list failure
  }
});

// POST /api/friends/remove
router.post('/remove', verifyAuthToken, async (req: Request & { user?: { id: string } }, res: Response) => {
  const currentUserId = req.user!.id;
  const { targetUserId } = req.body;

  if (!targetUserId || typeof targetUserId !== 'string') {
    return res.status(400).json({ success: false, message: 'targetUserId (string) is required.' });
  }

  const result = await friendService.removeFriend(currentUserId, targetUserId);
  if (result.success) {
    return res.status(200).json(result);
  } else {
    return res.status(400).json(result);
  }
});

// POST /api/friends/block
router.post('/block', verifyAuthToken, async (req: Request & { user?: { id: string } }, res: Response) => {
  const currentUserId = req.user!.id;
  const { targetUserId } = req.body;

  if (!targetUserId || typeof targetUserId !== 'string') {
    return res.status(400).json({ success: false, message: 'targetUserId (string) is required.' });
  }

  const result = await friendService.blockUser(currentUserId, targetUserId);
  if (result.success) {
    return res.status(200).json(result);
  } else {
    return res.status(400).json(result);
  }
});

// POST /api/friends/unblock
router.post('/unblock', verifyAuthToken, async (req: Request & { user?: { id: string } }, res: Response) => {
  const currentUserId = req.user!.id;
  const { targetUserId } = req.body;

  if (!targetUserId || typeof targetUserId !== 'string') {
    return res.status(400).json({ success: false, message: 'targetUserId (string) is required.' });
  }

  const result = await friendService.unblockUser(currentUserId, targetUserId);
  if (result.success) {
    return res.status(200).json(result);
  } else {
    return res.status(400).json(result);
  }
});

// GET /api/friends/status/:userId1/:userId2 (Example for areFriends, not strictly in plan for /api/friends)
// This might be better as an internal service method or a more restricted admin/dev endpoint.
// For now, let's keep it as a demonstration if it were to be exposed.
router.get('/status/:userId1/:userId2', verifyAuthToken, async (req: Request & { user?: { id: string } }, res: Response) => {
    // Note: Access control for this endpoint should be considered.
    // Who is allowed to check friendship status between any two users?
    // For simplicity, any authenticated user can check.
    const { userId1, userId2 } = req.params;

    if (!userId1 || !userId2 || typeof userId1 !== 'string' || typeof userId2 !== 'string') {
        return res.status(400).json({ success: false, message: 'Both userId1 and userId2 (strings) are required as path parameters.' });
    }

    try {
        const areTheyFriends = await friendService.areFriends(userId1, userId2);
        return res.status(200).json({ success: true, areFriends: areTheyFriends });
    } catch (error) {
        console.error("Error checking friendship status via API:", error);
        return res.status(500).json({ success: false, message: "Failed to check friendship status."});
    }
});


export default router;
