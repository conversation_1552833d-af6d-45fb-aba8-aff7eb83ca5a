"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ColyseusErrorHandler = exports.notFoundHandler = exports.asyncHandler = exports.errorHandler = void 0;
const BaseError_1 = require("../errors/BaseError");
/**
 * Global error handler middleware for Express
 */
const errorHandler = (error, req, res, next) => {
    // Log the error
    console.error('Error occurred:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        timestamp: new Date().toISOString()
    });
    // Handle custom errors
    if (error instanceof BaseError_1.BaseError) {
        res.status(error.httpCode).json(error.toJSON());
        return;
    }
    // Handle JWT errors
    if (error.name === 'JsonWebTokenError') {
        res.status(401).json({
            success: false,
            error: {
                name: 'InvalidTokenError',
                message: 'Invalid token format',
                code: 401,
                timestamp: new Date().toISOString()
            }
        });
        return;
    }
    if (error.name === 'TokenExpiredError') {
        res.status(401).json({
            success: false,
            error: {
                name: 'TokenExpiredError',
                message: 'Token has expired',
                code: 401,
                timestamp: new Date().toISOString()
            }
        });
        return;
    }
    // Handle MySQL/Database errors
    if (error.message.includes('ER_DUP_ENTRY')) {
        res.status(409).json({
            success: false,
            error: {
                name: 'ConflictError',
                message: 'Duplicate entry detected',
                code: 409,
                timestamp: new Date().toISOString()
            }
        });
        return;
    }
    if (error.message.includes('ER_NO_SUCH_TABLE')) {
        res.status(500).json({
            success: false,
            error: {
                name: 'DatabaseError',
                message: 'Database table not found',
                code: 500,
                timestamp: new Date().toISOString()
            }
        });
        return;
    }
    // Handle validation errors from express-validator
    if (error.name === 'ValidationError') {
        res.status(400).json({
            success: false,
            error: {
                name: 'ValidationError',
                message: error.message,
                code: 400,
                timestamp: new Date().toISOString()
            }
        });
        return;
    }
    // Default error response
    res.status(500).json({
        success: false,
        error: {
            name: 'InternalServerError',
            message: 'An unexpected error occurred',
            code: 500,
            timestamp: new Date().toISOString()
        }
    });
};
exports.errorHandler = errorHandler;
/**
 * Async error wrapper for route handlers
 */
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
/**
 * 404 Not Found handler
 */
const notFoundHandler = (req, res, next) => {
    res.status(404).json({
        success: false,
        error: {
            name: 'NotFoundError',
            message: `Route ${req.method} ${req.url} not found`,
            code: 404,
            timestamp: new Date().toISOString()
        }
    });
};
exports.notFoundHandler = notFoundHandler;
/**
 * Colyseus error handler utility
 */
class ColyseusErrorHandler {
    /**
     * Handle errors in Colyseus rooms
     */
    static handleRoomError(client, error) {
        console.error('Room error occurred:', {
            sessionId: client.sessionId,
            name: error.name,
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
        });
        if (error instanceof BaseError_1.BaseError) {
            client.error(error.httpCode, error.message, error.details);
            return;
        }
        // Handle specific error types
        if (error.name === 'ValidationError') {
            client.error(400, error.message);
            return;
        }
        if (error.message.includes('permission') || error.message.includes('access')) {
            client.error(403, 'Permission denied');
            return;
        }
        if (error.message.includes('not found')) {
            client.error(404, 'Resource not found');
            return;
        }
        // Default error
        client.error(500, 'An unexpected error occurred');
    }
    /**
     * Safe async operation wrapper for Colyseus
     */
    static async safeAsync(client, operation, errorMessage = 'Operation failed') {
        try {
            return await operation();
        }
        catch (error) {
            console.error(`Safe async operation failed: ${errorMessage}`, error);
            this.handleRoomError(client, error);
            return null;
        }
    }
}
exports.ColyseusErrorHandler = ColyseusErrorHandler;
