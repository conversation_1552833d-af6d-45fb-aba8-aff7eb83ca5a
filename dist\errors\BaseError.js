"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseError = void 0;
/**
 * Base error class for all custom errors in the application
 * Provides a consistent structure for error handling
 */
class BaseError extends Error {
    constructor(name, httpCode, description, isOperational = true, details) {
        super(description);
        Object.setPrototypeOf(this, new.target.prototype);
        this.name = name;
        this.httpCode = httpCode;
        this.isOperational = isOperational;
        this.timestamp = new Date();
        this.details = details;
        Error.captureStackTrace(this);
    }
    /**
     * Convert error to JSON format for API responses
     */
    toJSON() {
        return {
            success: false,
            error: {
                name: this.name,
                message: this.message,
                code: this.httpCode,
                timestamp: this.timestamp.toISOString(),
                ...(this.details && { details: this.details })
            }
        };
    }
    /**
     * Convert error to Colyseus client error format
     */
    toColyseusError() {
        return {
            code: this.httpCode,
            message: this.message,
            details: this.details
        };
    }
}
exports.BaseError = BaseError;
