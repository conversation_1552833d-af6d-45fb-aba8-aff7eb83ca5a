import { 
  ValidationError, 
  MissingFieldError, 
  InvalidFieldTypeError, 
  InvalidFieldValueError, 
  FieldLengthError,
  InvalidFormatError 
} from '../errors';

/**
 * Validation utility functions
 */
export class Validators {
  /**
   * Validate required field exists
   */
  static required(value: any, fieldName: string): void {
    if (value === undefined || value === null || value === '') {
      throw new MissingFieldError(fieldName);
    }
  }

  /**
   * Validate field type
   */
  static type(value: any, expectedType: string, fieldName: string): void {
    const actualType = typeof value;
    if (actualType !== expectedType) {
      throw new InvalidFieldTypeError(fieldName, expectedType, actualType);
    }
  }

  /**
   * Validate string length
   */
  static stringLength(value: string, fieldName: string, minLength?: number, maxLength?: number): void {
    if (typeof value !== 'string') {
      throw new InvalidFieldTypeError(fieldName, 'string', typeof value);
    }

    const length = value.length;
    if (minLength !== undefined && length < minLength) {
      throw new FieldLengthError(fieldName, length, minLength, maxLength);
    }
    if (maxLength !== undefined && length > maxLength) {
      throw new FieldLengthError(fieldName, length, minLength, maxLength);
    }
  }

  /**
   * Validate number range
   */
  static numberRange(value: number, fieldName: string, min?: number, max?: number): void {
    if (typeof value !== 'number' || isNaN(value)) {
      throw new InvalidFieldTypeError(fieldName, 'number', typeof value);
    }

    if (min !== undefined && value < min) {
      throw new InvalidFieldValueError(fieldName, value, [`>= ${min}`]);
    }
    if (max !== undefined && value > max) {
      throw new InvalidFieldValueError(fieldName, value, [`<= ${max}`]);
    }
  }

  /**
   * Validate value is in allowed list
   */
  static oneOf(value: any, allowedValues: any[], fieldName: string): void {
    if (!allowedValues.includes(value)) {
      throw new InvalidFieldValueError(fieldName, value, allowedValues);
    }
  }

  /**
   * Validate email format
   */
  static email(value: string, fieldName: string = 'email'): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      throw new InvalidFormatError(fieldName, 'valid email address', value);
    }
  }

  /**
   * Validate username format
   */
  static username(value: string, fieldName: string = 'username'): void {
    // Username: 3-20 characters, alphanumeric and underscore only
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(value)) {
      throw new InvalidFormatError(fieldName, '3-20 characters, alphanumeric and underscore only', value);
    }
  }

  /**
   * Validate password strength
   */
  static password(value: string, fieldName: string = 'password'): void {
    if (value.length < 6) {
      throw new FieldLengthError(fieldName, value.length, 6);
    }
    // Add more password rules as needed
  }

  /**
   * Validate character name
   */
  static characterName(value: string, fieldName: string = 'characterName'): void {
    // Character name: 2-16 characters, letters, numbers, spaces, and some special chars
    const nameRegex = /^[a-zA-Z0-9\s\-_']{2,16}$/;
    if (!nameRegex.test(value)) {
      throw new InvalidFormatError(fieldName, '2-16 characters, letters, numbers, spaces, hyphens, underscores, and apostrophes only', value);
    }
  }

  /**
   * Validate coordinates
   */
  static coordinates(x: number, y: number, fieldPrefix: string = 'position'): void {
    this.numberRange(x, `${fieldPrefix}.x`, -10000, 10000);
    this.numberRange(y, `${fieldPrefix}.y`, -10000, 10000);
  }

  /**
   * Validate rotation
   */
  static rotation(value: number, fieldName: string = 'rotation'): void {
    this.numberRange(value, fieldName, 0, 359);
  }

  /**
   * Validate boolean
   */
  static boolean(value: any, fieldName: string): void {
    if (typeof value !== 'boolean') {
      throw new InvalidFieldTypeError(fieldName, 'boolean', typeof value);
    }
  }

  /**
   * Validate array
   */
  static array(value: any, fieldName: string, minLength?: number, maxLength?: number): void {
    if (!Array.isArray(value)) {
      throw new InvalidFieldTypeError(fieldName, 'array', typeof value);
    }

    if (minLength !== undefined && value.length < minLength) {
      throw new FieldLengthError(fieldName, value.length, minLength, maxLength);
    }
    if (maxLength !== undefined && value.length > maxLength) {
      throw new FieldLengthError(fieldName, value.length, minLength, maxLength);
    }
  }

  /**
   * Validate object
   */
  static object(value: any, fieldName: string): void {
    if (typeof value !== 'object' || value === null || Array.isArray(value)) {
      throw new InvalidFieldTypeError(fieldName, 'object', typeof value);
    }
  }

  /**
   * Validate JSON string
   */
  static jsonString(value: string, fieldName: string): void {
    try {
      JSON.parse(value);
    } catch (error) {
      throw new InvalidFormatError(fieldName, 'valid JSON string', value);
    }
  }

  /**
   * Validate UUID format
   */
  static uuid(value: string, fieldName: string): void {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(value)) {
      throw new InvalidFormatError(fieldName, 'valid UUID', value);
    }
  }
}
