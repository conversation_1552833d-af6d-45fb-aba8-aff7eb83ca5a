import { Room, Client } from '@colyseus/core';
import authService from '../services/authService';
import RoomMatchingService, { RoomType, AccessLevel } from '../services/RoomMatchingService';

/**
 * Base class for private rooms (Home, Garden, etc.)
 * Provides common functionality for dynamic room management
 */
export abstract class BasePrivateRoom<T extends object> extends Room<T> {
  protected ownerUid!: string;
  protected roomType!: RoomType;
  protected dynamicRoomId!: string;

  public autoDispose = true;

  /**
   * Initialize the room with dynamic ID and owner
   */
  protected async initializeRoom(options: { ownerUid?: string; roomId?: string }, roomType: RoomType): Promise<void> {
    // Handle both direct ownerUid and dynamic roomId
    if (options.roomId) {
      const parsed = RoomMatchingService.parseRoomId(options.roomId);
      if (!parsed || parsed.roomType !== roomType) {
        throw new Error(`Invalid room ID format: ${options.roomId}`);
      }
      this.ownerUid = parsed.ownerUid;
      this.dynamicRoomId = options.roomId;
    } else if (options.ownerUid) {
      this.ownerUid = options.ownerUid;
      this.dynamicRoomId = RoomMatchingService.generateRoomId(roomType, this.ownerUid);
    } else {
      throw new Error('Either ownerUid or roomId must be provided');
    }

    this.roomType = roomType;

    // Set the room ID for Colyseus
    this.roomId = this.dynamicRoomId;

    console.log(`${roomType}Room initialized: ${this.dynamicRoomId} for owner: ${this.ownerUid}`);

    // Register room in the registry
    await RoomMatchingService.registerRoom(
      this.dynamicRoomId,
      this.roomType,
      this.ownerUid
    );
  }

  /**
   * Enhanced authentication with room access checking
   */
  async onAuth(client: Client, options: { token: string; uid: string }, request: any): Promise<any> {
    if (!this.ownerUid) {
      console.error(`${this.roomType}Room.onAuth: Critical - ownerUid not set. Room was not properly initialized.`);
      throw new Error('Room not initialized. Cannot authenticate.');
    }

    if (!options || !options.token || !options.uid || typeof options.token !== 'string' || typeof options.uid !== 'string') {
      console.error(`${this.roomType}Room.onAuth: Auth failed for client ${client.sessionId} - Missing or invalid token/uid in options.`);
      throw new Error('Authentication failed: Token and UID are required and must be strings.');
    }

    // 1. Verify the provided token using AuthService
    const userPayload = await authService.verifyToken(options.token);
    if (!userPayload || userPayload.userId.toString() !== options.uid) {
      console.error(`${this.roomType}Room.onAuth: Auth failed for client ${client.sessionId} - Invalid token or UID mismatch.`);
      throw new Error('Authentication failed: Invalid token or UID does not match.');
    }

    // 2. Check if the authenticated user has access to this room
    const hasAccess = await RoomMatchingService.checkRoomAccess(this.ownerUid, options.uid, this.roomType);
    if (!hasAccess) {
      console.warn(`${this.roomType}Room.onAuth: Access denied for client ${client.sessionId} (UID: ${options.uid}) to ${this.roomType} of ${this.ownerUid}.`);
      throw new Error(`Access denied to ${this.roomType} of user ${this.ownerUid}.`);
    }

    // 3. Update room last accessed time
    await RoomMatchingService.updateRoomLastAccessed(this.dynamicRoomId);

    console.log(`${this.roomType}Room.onAuth: Auth successful for client ${client.sessionId} (UID: ${options.uid}) to ${this.roomType} of ${this.ownerUid}.`);
    return { uid: options.uid };
  }

  /**
   * Handle access level updates
   */
  protected async updateAccessLevel(newAccessLevel: AccessLevel): Promise<void> {
    // Update in room registry
    await RoomMatchingService.updateRoomAccessLevel(this.dynamicRoomId, newAccessLevel);

    // Update room last accessed time
    await RoomMatchingService.updateRoomLastAccessed(this.dynamicRoomId);
  }

  /**
   * Check if a user is the room owner
   */
  protected isOwner(uid: string): boolean {
    return uid === this.ownerUid;
  }

  /**
   * Get room information
   */
  async getRoomInfo() {
    return await RoomMatchingService.getRoomInfo(this.dynamicRoomId);
  }

  /**
   * Cleanup when room is disposed
   */
  onDispose(): void {
    console.log(`${this.roomType}Room.onDispose: Room ${this.dynamicRoomId} for owner ${this.ownerUid} disposed.`);

    // Unregister room from registry
    RoomMatchingService.unregisterRoom(this.dynamicRoomId).catch(error => {
      console.error(`Error unregistering room ${this.dynamicRoomId}:`, error);
    });
  }

  /**
   * Get the dynamic room ID
   */
  getDynamicRoomId(): string {
    return this.dynamicRoomId;
  }

  /**
   * Get the room owner UID
   */
  getOwnerUid(): string {
    return this.ownerUid;
  }

  /**
   * Get the room type
   */
  getRoomType(): RoomType {
    return this.roomType;
  }

  /**
   * Abstract method for room-specific initialization
   * Must be implemented by subclasses
   */
  protected abstract initializeRoomState(): Promise<void>;

  /**
   * Abstract method for registering room-specific message handlers
   * Must be implemented by subclasses
   */
  protected abstract registerMessageHandlers(): void;
}

/**
 * Options for creating/joining private rooms
 */
export interface PrivateRoomOptions {
  ownerUid?: string;
  roomId?: string;
  token: string;
  uid: string;
}

/**
 * Utility functions for room management
 */
export class RoomUtils {
  /**
   * Generate a room ID for a specific user and room type
   */
  static generateRoomId(roomType: RoomType, ownerUid: string): string {
    return RoomMatchingService.generateRoomId(roomType, ownerUid);
  }

  /**
   * Parse a room ID to extract information
   */
  static parseRoomId(roomId: string): { roomType: RoomType; ownerUid: string } | null {
    return RoomMatchingService.parseRoomId(roomId);
  }

  /**
   * Check if a user can access a specific room
   */
  static async canAccessRoom(ownerUid: string, requestingUid: string, roomType: RoomType): Promise<boolean> {
    return await RoomMatchingService.checkRoomAccess(ownerUid, requestingUid, roomType);
  }

  /**
   * Get all accessible rooms for a user
   */
  static async getAccessibleRooms(userId: string, roomType: RoomType): Promise<{
    ownRooms: any[];
    friendRooms: any[];
    publicRooms: any[];
  }> {
    const [ownRooms, friendRooms, publicRooms] = await Promise.all([
      RoomMatchingService.getUserActiveRooms(userId),
      RoomMatchingService.getFriendRooms(userId, roomType),
      RoomMatchingService.getPublicRooms(roomType)
    ]);

    return {
      ownRooms: ownRooms.filter(room => room.room_type === roomType),
      friendRooms,
      publicRooms
    };
  }
}
