import { Schema, type, MapSchema, ArraySchema } from '@colyseus/schema';

/**
 * Represents an inventory item state
 */
export class InventoryItemState extends Schema {
  @type('string') itemId: string = '';
  @type('string') itemName: string = '';
  @type('string') itemType: string = '';
  @type('string') category: string = '';
  @type('string') description: string = '';
  @type('number') quantity: number = 0;
  @type('boolean') isLocked: boolean = false;
  @type('boolean') isNew: boolean = false;
  @type('boolean') isEquipped: boolean = false;
  @type('boolean') isStackable: boolean = false;
  @type('number') maxStackSize: number = 1;
  @type('string') rarity: string = 'common';
  @type('boolean') canUse: boolean = false;
  @type('boolean') canSell: boolean = false;
  @type('boolean') canGift: boolean = false;
  @type('boolean') canEquip: boolean = false;
  @type('number') acquiredAt: number = 0;
  @type('string') customData: string = '{}'; // JSON string

  constructor(
    itemId: string = '',
    itemName: string = '',
    quantity: number = 0
  ) {
    super();
    this.itemId = itemId;
    this.itemName = itemName;
    this.quantity = quantity;
    this.acquiredAt = Date.now();
  }

  /**
   * Get custom data as parsed object
   */
  getCustomData(): any {
    try {
      return JSON.parse(this.customData);
    } catch (error) {
      return {};
    }
  }

  /**
   * Set custom data from object
   */
  setCustomData(data: any): void {
    this.customData = JSON.stringify(data);
  }

  /**
   * Add quantity to this item
   */
  addQuantity(amount: number): void {
    if (this.isStackable) {
      this.quantity = Math.min(this.quantity + amount, this.maxStackSize);
    } else {
      this.quantity += amount;
    }
  }

  /**
   * Remove quantity from this item
   */
  removeQuantity(amount: number): boolean {
    if (this.quantity >= amount) {
      this.quantity -= amount;
      return true;
    }
    return false;
  }

  /**
   * Check if item can be stacked with another item
   */
  canStackWith(otherItem: InventoryItemState): boolean {
    return this.isStackable && 
           this.itemId === otherItem.itemId && 
           this.quantity + otherItem.quantity <= this.maxStackSize;
  }
}

/**
 * Represents inventory category state
 */
export class InventoryCategoryState extends Schema {
  @type('string') categoryKey: string = '';
  @type('string') categoryName: string = '';
  @type('number') itemCount: number = 0;
  @type({ map: InventoryItemState }) items = new MapSchema<InventoryItemState>();

  constructor(categoryKey: string = '', categoryName: string = '') {
    super();
    this.categoryKey = categoryKey;
    this.categoryName = categoryName;
  }

  /**
   * Add item to category
   */
  addItem(item: InventoryItemState): void {
    this.items.set(item.itemId, item);
    this.updateItemCount();
  }

  /**
   * Remove item from category
   */
  removeItem(itemId: string): void {
    this.items.delete(itemId);
    this.updateItemCount();
  }

  /**
   * Update item count
   */
  private updateItemCount(): void {
    this.itemCount = this.items.size;
  }

  /**
   * Get items as array
   */
  getItemsArray(): InventoryItemState[] {
    return Array.from(this.items.values());
  }

  /**
   * Get new items count
   */
  getNewItemsCount(): number {
    let count = 0;
    this.items.forEach(item => {
      if (item.isNew) count++;
    });
    return count;
  }

  /**
   * Get equipped items count
   */
  getEquippedItemsCount(): number {
    let count = 0;
    this.items.forEach(item => {
      if (item.isEquipped) count++;
    });
    return count;
  }
}

/**
 * Represents inventory configuration state
 */
export class InventoryConfigState extends Schema {
  @type('number') maxCapacity: number = 100;
  @type('string') defaultSortOrder: string = 'acquired_time_desc';
  @type('boolean') autoSortEnabled: boolean = false;
  @type('boolean') showNewItemsFirst: boolean = true;

  constructor() {
    super();
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<InventoryConfigState>): void {
    Object.assign(this, config);
  }
}

/**
 * Represents the complete inventory system state
 */
export class InventorySystemState extends Schema {
  @type('string') userId: string = '';
  @type(InventoryConfigState) config = new InventoryConfigState();
  @type({ map: InventoryCategoryState }) categories = new MapSchema<InventoryCategoryState>();
  @type({ map: InventoryItemState }) allItems = new MapSchema<InventoryItemState>();
  @type('number') totalItems: number = 0;
  @type('number') uniqueItems: number = 0;
  @type('number') capacityUsed: number = 0;
  @type('boolean') hasNewItems: boolean = false;
  @type('number') lastUpdated: number = 0;

  constructor(userId: string = '') {
    super();
    this.userId = userId;
    this.lastUpdated = Date.now();
    this.initializeCategories();
  }

  /**
   * Initialize default categories
   */
  private initializeCategories(): void {
    const defaultCategories = [
      { key: 'fashion', name: '时装' },
      { key: 'furniture', name: '家具' },
      { key: 'tools', name: '道具' },
      { key: 'materials', name: '材料' },
      { key: 'quest', name: '任务' }
    ];

    defaultCategories.forEach(cat => {
      this.categories.set(cat.key, new InventoryCategoryState(cat.key, cat.name));
    });
  }

  /**
   * Add item to inventory
   */
  addItem(item: InventoryItemState, categoryKey: string): void {
    // Add to all items
    const existingItem = this.allItems.get(item.itemId);
    if (existingItem && existingItem.isStackable) {
      existingItem.addQuantity(item.quantity);
    } else {
      this.allItems.set(item.itemId, item);
    }

    // Add to category
    const category = this.categories.get(categoryKey);
    if (category) {
      category.addItem(item);
    }

    this.updateStats();
  }

  /**
   * Remove item from inventory
   */
  removeItem(itemId: string, quantity: number = 1): boolean {
    const item = this.allItems.get(itemId);
    if (!item) return false;

    const success = item.removeQuantity(quantity);
    if (success) {
      if (item.quantity === 0) {
        this.allItems.delete(itemId);
        // Remove from all categories
        this.categories.forEach(category => {
          category.removeItem(itemId);
        });
      }
      this.updateStats();
    }

    return success;
  }

  /**
   * Get item by ID
   */
  getItem(itemId: string): InventoryItemState | undefined {
    return this.allItems.get(itemId);
  }

  /**
   * Update item properties
   */
  updateItem(itemId: string, updates: Partial<InventoryItemState>): void {
    const item = this.allItems.get(itemId);
    if (item) {
      Object.assign(item, updates);
      this.updateStats();
    }
  }

  /**
   * Mark items as seen
   */
  markItemsAsSeen(itemIds?: string[]): void {
    if (itemIds && itemIds.length > 0) {
      itemIds.forEach(itemId => {
        const item = this.allItems.get(itemId);
        if (item) {
          item.isNew = false;
        }
      });
    } else {
      // Mark all items as seen
      this.allItems.forEach(item => {
        item.isNew = false;
      });
    }
    this.updateStats();
  }

  /**
   * Get items by category
   */
  getItemsByCategory(categoryKey: string): InventoryItemState[] {
    const category = this.categories.get(categoryKey);
    return category ? category.getItemsArray() : [];
  }

  /**
   * Search items
   */
  searchItems(query: string): InventoryItemState[] {
    const results: InventoryItemState[] = [];
    const searchTerm = query.toLowerCase();

    this.allItems.forEach(item => {
      if (item.itemName.toLowerCase().includes(searchTerm) ||
          item.description.toLowerCase().includes(searchTerm)) {
        results.push(item);
      }
    });

    return results;
  }

  /**
   * Get new items
   */
  getNewItems(): InventoryItemState[] {
    const newItems: InventoryItemState[] = [];
    this.allItems.forEach(item => {
      if (item.isNew) {
        newItems.push(item);
      }
    });
    return newItems;
  }

  /**
   * Get equipped items
   */
  getEquippedItems(): InventoryItemState[] {
    const equippedItems: InventoryItemState[] = [];
    this.allItems.forEach(item => {
      if (item.isEquipped) {
        equippedItems.push(item);
      }
    });
    return equippedItems;
  }

  /**
   * Update inventory statistics
   */
  private updateStats(): void {
    this.uniqueItems = this.allItems.size;
    
    let totalQuantity = 0;
    let hasNew = false;

    this.allItems.forEach(item => {
      totalQuantity += item.quantity;
      if (item.isNew) hasNew = true;
    });

    this.totalItems = totalQuantity;
    this.capacityUsed = this.uniqueItems; // Using unique items for capacity
    this.hasNewItems = hasNew;
    this.lastUpdated = Date.now();
  }

  /**
   * Check if inventory is full
   */
  isFull(): boolean {
    return this.capacityUsed >= this.config.maxCapacity;
  }

  /**
   * Get available capacity
   */
  getAvailableCapacity(): number {
    return Math.max(0, this.config.maxCapacity - this.capacityUsed);
  }

  /**
   * Update configuration
   */
  updateConfig(configUpdates: Partial<InventoryConfigState>): void {
    this.config.updateConfig(configUpdates);
    this.lastUpdated = Date.now();
  }

  /**
   * Clear all items (for testing or reset)
   */
  clearAllItems(): void {
    this.allItems.clear();
    this.categories.forEach(category => {
      category.items.clear();
      category.itemCount = 0;
    });
    this.updateStats();
  }
}
