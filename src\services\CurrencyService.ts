import pool from '../utils/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';
import { ShopError } from '../errors';

/**
 * User currency interface
 */
export interface UserCurrency extends RowDataPacket {
  user_id: string;
  gold_coins: number;
  diamonds: number;
  created_at: Date;
  updated_at: Date;
}

/**
 * Currency transaction interface
 */
export interface CurrencyTransaction {
  userId: string;
  goldChange?: number;
  diamondChange?: number;
  reason: string;
  metadata?: any;
}

/**
 * Currency service for managing player currencies
 */
class CurrencyService {
  private static instance: CurrencyService;

  private constructor() {}

  public static getInstance(): CurrencyService {
    if (!CurrencyService.instance) {
      CurrencyService.instance = new CurrencyService();
    }
    return CurrencyService.instance;
  }

  /**
   * Get user currency balances
   */
  async getUserCurrency(userId: string): Promise<UserCurrency> {
    try {
      const [rows] = await pool.query<UserCurrency[]>(
        'SELECT * FROM user_currencies WHERE user_id = ?',
        [userId]
      );

      if (rows.length === 0) {
        // Create initial currency record for new user
        await this.initializeUserCurrency(userId);
        return await this.getUserCurrency(userId);
      }

      return rows[0];
    } catch (error) {
      console.error('Failed to get user currency:', error);
      throw ShopError.currencyOperationFailed('get_currency', 'Database error');
    }
  }

  /**
   * Initialize currency for a new user
   */
  async initializeUserCurrency(userId: string, initialGold: number = 1000, initialDiamonds: number = 10): Promise<void> {
    try {
      await pool.query<ResultSetHeader>(
        'INSERT INTO user_currencies (user_id, gold_coins, diamonds) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE user_id = user_id',
        [userId, initialGold, initialDiamonds]
      );
    } catch (error) {
      console.error('Failed to initialize user currency:', error);
      throw ShopError.currencyOperationFailed('initialize_currency', 'Database error');
    }
  }

  /**
   * Check if user has sufficient currency
   */
  async hasSufficientCurrency(userId: string, goldRequired: number = 0, diamondsRequired: number = 0): Promise<boolean> {
    try {
      const currency = await this.getUserCurrency(userId);
      return currency.gold_coins >= goldRequired && currency.diamonds >= diamondsRequired;
    } catch (error) {
      console.error('Failed to check currency sufficiency:', error);
      return false;
    }
  }

  /**
   * Deduct currency from user account
   */
  async deductCurrency(userId: string, goldAmount: number = 0, diamondAmount: number = 0, reason: string = 'Purchase'): Promise<UserCurrency> {
    if (goldAmount < 0 || diamondAmount < 0) {
      throw ShopError.invalidShopData('Currency amounts must be non-negative');
    }

    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Check current balance
      const [currentRows] = await connection.query<UserCurrency[]>(
        'SELECT * FROM user_currencies WHERE user_id = ? FOR UPDATE',
        [userId]
      );

      if (currentRows.length === 0) {
        throw ShopError.currencyOperationFailed('deduct_currency', 'User currency record not found');
      }

      const current = currentRows[0];

      // Check if user has sufficient currency
      if (current.gold_coins < goldAmount) {
        throw ShopError.insufficientCurrency('gold coins', goldAmount, current.gold_coins);
      }

      if (current.diamonds < diamondAmount) {
        throw ShopError.insufficientCurrency('diamonds', diamondAmount, current.diamonds);
      }

      // Deduct currency
      const newGold = current.gold_coins - goldAmount;
      const newDiamonds = current.diamonds - diamondAmount;

      await connection.query<ResultSetHeader>(
        'UPDATE user_currencies SET gold_coins = ?, diamonds = ? WHERE user_id = ?',
        [newGold, newDiamonds, userId]
      );

      await connection.commit();

      // Return updated currency
      return {
        ...current,
        gold_coins: newGold,
        diamonds: newDiamonds
      };

    } catch (error) {
      await connection.rollback();
      console.error('Failed to deduct currency:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.currencyOperationFailed('deduct_currency', 'Transaction failed');
    } finally {
      connection.release();
    }
  }

  /**
   * Add currency to user account
   */
  async addCurrency(userId: string, goldAmount: number = 0, diamondAmount: number = 0, reason: string = 'Reward'): Promise<UserCurrency> {
    if (goldAmount < 0 || diamondAmount < 0) {
      throw ShopError.invalidShopData('Currency amounts must be non-negative');
    }

    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Get current balance
      const [currentRows] = await connection.query<UserCurrency[]>(
        'SELECT * FROM user_currencies WHERE user_id = ? FOR UPDATE',
        [userId]
      );

      if (currentRows.length === 0) {
        // Initialize if not exists
        await this.initializeUserCurrency(userId);
        return await this.addCurrency(userId, goldAmount, diamondAmount, reason);
      }

      const current = currentRows[0];

      // Add currency
      const newGold = current.gold_coins + goldAmount;
      const newDiamonds = current.diamonds + diamondAmount;

      await connection.query<ResultSetHeader>(
        'UPDATE user_currencies SET gold_coins = ?, diamonds = ? WHERE user_id = ?',
        [newGold, newDiamonds, userId]
      );

      await connection.commit();

      // Return updated currency
      return {
        ...current,
        gold_coins: newGold,
        diamonds: newDiamonds
      };

    } catch (error) {
      await connection.rollback();
      console.error('Failed to add currency:', error);
      throw ShopError.currencyOperationFailed('add_currency', 'Transaction failed');
    } finally {
      connection.release();
    }
  }

  /**
   * Transfer currency between users (for gifting)
   */
  async transferCurrency(
    fromUserId: string, 
    toUserId: string, 
    goldAmount: number = 0, 
    diamondAmount: number = 0,
    reason: string = 'Transfer'
  ): Promise<{ fromCurrency: UserCurrency; toCurrency: UserCurrency }> {
    if (fromUserId === toUserId) {
      throw ShopError.cannotGiftToSelf();
    }

    if (goldAmount < 0 || diamondAmount < 0) {
      throw ShopError.invalidShopData('Currency amounts must be non-negative');
    }

    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Deduct from sender
      const fromCurrency = await this.deductCurrency(fromUserId, goldAmount, diamondAmount, `${reason} (sent)`);
      
      // Add to recipient
      const toCurrency = await this.addCurrency(toUserId, goldAmount, diamondAmount, `${reason} (received)`);

      await connection.commit();

      return { fromCurrency, toCurrency };

    } catch (error) {
      await connection.rollback();
      console.error('Failed to transfer currency:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.currencyOperationFailed('transfer_currency', 'Transaction failed');
    } finally {
      connection.release();
    }
  }

  /**
   * Get multiple users' currency balances
   */
  async getMultipleUsersCurrency(userIds: string[]): Promise<Map<string, UserCurrency>> {
    if (userIds.length === 0) {
      return new Map();
    }

    try {
      const placeholders = userIds.map(() => '?').join(',');
      const [rows] = await pool.query<UserCurrency[]>(
        `SELECT * FROM user_currencies WHERE user_id IN (${placeholders})`,
        userIds
      );

      const currencyMap = new Map<string, UserCurrency>();
      rows.forEach(currency => {
        currencyMap.set(currency.user_id.toString(), currency);
      });

      return currencyMap;
    } catch (error) {
      console.error('Failed to get multiple users currency:', error);
      throw ShopError.currencyOperationFailed('get_multiple_currency', 'Database error');
    }
  }

  /**
   * Batch currency operations
   */
  async batchCurrencyOperations(transactions: CurrencyTransaction[]): Promise<Map<string, UserCurrency>> {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      const results = new Map<string, UserCurrency>();

      for (const transaction of transactions) {
        const { userId, goldChange = 0, diamondChange = 0, reason } = transaction;

        if (goldChange > 0 || diamondChange > 0) {
          // Add currency
          const result = await this.addCurrency(userId, goldChange, diamondChange, reason);
          results.set(userId, result);
        } else if (goldChange < 0 || diamondChange < 0) {
          // Deduct currency
          const result = await this.deductCurrency(userId, Math.abs(goldChange), Math.abs(diamondChange), reason);
          results.set(userId, result);
        }
      }

      await connection.commit();
      return results;

    } catch (error) {
      await connection.rollback();
      console.error('Failed to execute batch currency operations:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.currencyOperationFailed('batch_operations', 'Transaction failed');
    } finally {
      connection.release();
    }
  }
}

export default CurrencyService.getInstance();
