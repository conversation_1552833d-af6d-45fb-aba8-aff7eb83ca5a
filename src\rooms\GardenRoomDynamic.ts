import { Client } from '@colyseus/core';
import { GardenRoomState } from '../models/GardenRoomState';
import { PlayerState } from '../models/PlayerState';
import { GardenPlotState } from '../models/GardenPlotState';
import { BasePrivateRoom, PrivateRoomOptions } from './BasePrivateRoom';
import SpaceService, { GardenPlotRecord } from '../services/SpaceService';
import ItemService from '../services/ItemService';
import { ColyseusErrorHandler } from '../middleware/errorHandler';
import {
  ValidationError,
  PermissionDeniedError,
  NotFoundError,
  InvalidFieldTypeError,
  MissingFieldError
} from '../errors';

/**
 * Enhanced GardenRoom with dynamic room ID support
 * Extends BasePrivateRoom for automatic room management
 */
export class GardenRoomDynamic extends BasePrivateRoom<GardenRoomState> {
  private spaceServiceInstance!: typeof SpaceService;
  private itemServiceInstance!: typeof ItemService;

  /**
   * Called when the room is created with dynamic room support
   */
  async onCreate(options: PrivateRoomOptions) {
    try {
      // Initialize the room with dynamic ID support
      await this.initializeRoom(options, 'garden');

      // Initialize service instances
      this.spaceServiceInstance = SpaceService;
      this.itemServiceInstance = ItemService;

      // Initialize room state
      await this.initializeRoomState();

      // Register message handlers
      this.registerMessageHandlers();

      console.log(`GardenRoomDynamic created successfully: ${this.getDynamicRoomId()}`);
    } catch (error) {
      console.error('GardenRoomDynamic creation failed:', error);
      throw error;
    }
  }

  /**
   * Initialize the room state with owner's garden data
   */
  protected async initializeRoomState(): Promise<void> {
    // Set the initial state of the room
    this.setState(new GardenRoomState(this.getOwnerUid()));

    try {
      // Load the persisted garden data for the owner
      const spaceData = await this.spaceServiceInstance.getFullPrivateSpaceData(this.getOwnerUid());

      this.state.gardenBackgroundId = spaceData.settings.garden_background_id;
      this.state.accessLevel = spaceData.settings.garden_access_level;

      // Populate garden plots from persisted data
      spaceData.gardenPlots.forEach((plotRecord: GardenPlotRecord) => {
        const gardenPlot = new GardenPlotState(
          plotRecord.plot_id,
          plotRecord.plot_template_id,
          plotRecord.seed_id,
          plotRecord.plant_timestamp ? plotRecord.plant_timestamp.getTime() : 0,
          plotRecord.growth_stage,
          plotRecord.last_watered_timestamp ? plotRecord.last_watered_timestamp.getTime() : 0
        );
        this.state.plots.set(plotRecord.plot_id, gardenPlot);
      });

      console.log(`GardenRoomDynamic state initialized: Background: ${this.state.gardenBackgroundId}, Access: ${this.state.accessLevel}, Plots: ${this.state.plots.size}`);
    } catch (error) {
      console.error('Failed to initialize garden room state:', error);
      throw error;
    }
  }

  /**
   * Register message handlers for garden-specific actions
   */
  protected registerMessageHandlers(): void {
    // Plant seed handler
    this.onMessage("plant_seed", async (client, message: { plotId: string; seedId: string }) => {
      const result = await ColyseusErrorHandler.safeAsync(client, async () => {
        // Permission check
        if (!this.isOwner(client.auth.uid)) {
          throw new PermissionDeniedError("plant seeds", "garden");
        }

        const { plotId, seedId } = message;

        // Validation
        if (!plotId) throw new MissingFieldError('plotId');
        if (!seedId) throw new MissingFieldError('seedId');
        if (typeof plotId !== 'string') throw new InvalidFieldTypeError('plotId', 'string', typeof plotId);
        if (typeof seedId !== 'string') throw new InvalidFieldTypeError('seedId', 'string', typeof seedId);

        // Call SpaceService to handle planting logic
        const serviceResult = await this.spaceServiceInstance.plantSeed(this.getOwnerUid(), plotId, seedId);

        if (serviceResult.success) {
          // Update room state
          const plot = this.state.plots.get(plotId);
          if (plot) {
            plot.seedId = seedId;
            plot.plantTimestamp = Date.now();
            plot.growthStage = 0;
            plot.lastWateredTimestamp = 0;
          }
          return serviceResult;
        } else {
          throw new Error(serviceResult.message || "Failed to plant seed.");
        }
      }, "Failed to plant seed");
    });

    // Water plant handler
    this.onMessage("water_plant", async (client, message: { plotId: string }) => {
      const result = await ColyseusErrorHandler.safeAsync(client, async () => {
        // Permission check
        if (!this.isOwner(client.auth.uid)) {
          throw new PermissionDeniedError("water plants", "garden");
        }

        const { plotId } = message;

        // Validation
        if (!plotId) throw new MissingFieldError('plotId');
        if (typeof plotId !== 'string') throw new InvalidFieldTypeError('plotId', 'string', typeof plotId);

        // Call SpaceService to handle watering logic
        const serviceResult = await this.spaceServiceInstance.waterPlant(this.getOwnerUid(), plotId);

        if (serviceResult.success) {
          // Update room state
          const plot = this.state.plots.get(plotId);
          if (plot) {
            plot.lastWateredTimestamp = Date.now();
          }
          return serviceResult;
        } else {
          throw new Error(serviceResult.message || "Failed to water plant.");
        }
      }, "Failed to water plant");
    });

    // Harvest plant handler
    this.onMessage("harvest_plant", async (client, message: { plotId: string }) => {
      const result = await ColyseusErrorHandler.safeAsync(client, async () => {
        // Permission check
        if (!this.isOwner(client.auth.uid)) {
          throw new PermissionDeniedError("harvest plants", "garden");
        }

        const { plotId } = message;

        // Validation
        if (!plotId) throw new MissingFieldError('plotId');
        if (typeof plotId !== 'string') throw new InvalidFieldTypeError('plotId', 'string', typeof plotId);

        // Call SpaceService to handle harvesting logic
        const serviceResult = await this.spaceServiceInstance.harvestPlant(this.getOwnerUid(), plotId);

        if (serviceResult.success) {
          // Update room state
          const plot = this.state.plots.get(plotId);
          if (plot) {
            plot.seedId = '';
            plot.plantTimestamp = 0;
            plot.growthStage = 0;
            plot.lastWateredTimestamp = 0;
          }

          // Send harvest results to client
          client.send("harvest_completed", {
            plotId,
            harvestedItems: serviceResult.harvestedItems,
            message: "Harvest completed successfully!"
          });

          return serviceResult;
        } else {
          throw new Error(serviceResult.message || "Failed to harvest plant.");
        }
      }, "Failed to harvest plant");
    });

    // Update garden background handler
    this.onMessage("update_background", async (client, message: { backgroundItemId: string }) => {
      if (!this.isOwner(client.auth.uid)) {
        return client.error(403, "Permission denied: Only the room owner can change the background.");
      }

      const { backgroundItemId } = message;
      if (typeof backgroundItemId !== 'string') {
        return client.error(400, "Invalid parameters: 'backgroundItemId' is required.");
      }

      try {
        await this.spaceServiceInstance.updateSpaceBackground(this.getOwnerUid(), 'garden', backgroundItemId);
        this.state.gardenBackgroundId = backgroundItemId;
      } catch (e: any) {
        console.error(`Error updating garden background:`, e);
        client.error(500, e.message || "Server error: Could not update background.");
      }
    });

    // Update access level handler
    this.onMessage("update_access_level", async (client, message: { accessLevel: 'private' | 'friends_only' | 'public' }) => {
      if (!this.isOwner(client.auth.uid)) {
        return client.error(403, "Permission denied: Only the room owner can change access level.");
      }

      const { accessLevel } = message;
      if (!['private', 'friends_only', 'public'].includes(accessLevel)) {
        return client.error(400, "Invalid access level provided.");
      }

      try {
        await this.spaceServiceInstance.updateSpaceAccessLevel(this.getOwnerUid(), 'garden', accessLevel);
        this.state.accessLevel = accessLevel;

        // Update in room registry
        await this.updateAccessLevel(accessLevel);
      } catch (e: any) {
        console.error(`Error updating access level:`, e);
        client.error(500, e.message || "Server error: Could not update access level.");
      }
    });

    // Get plot status handler (for checking growth progress)
    this.onMessage("get_plot_status", async (client, message: { plotId: string }) => {
      try {
        const { plotId } = message;
        if (typeof plotId !== 'string') {
          return client.error(400, "Invalid parameters: 'plotId' is required.");
        }

        const plot = this.state.plots.get(plotId);
        if (!plot) {
          return client.error(404, "Plot not found.");
        }

        // Send current plot status from room state
        client.send("plot_status", {
          plotId,
          status: {
            seedId: plot.seedId,
            plantTimestamp: plot.plantTimestamp,
            growthStage: plot.growthStage,
            lastWateredTimestamp: plot.lastWateredTimestamp
          }
        });
      } catch (e: any) {
        console.error(`Error getting plot status:`, e);
        client.error(500, e.message || "Server error: Could not get plot status.");
      }
    });

    // Player state update handler
    this.onMessage('updatePlayerState', (client, data) => {
      const player = this.state.players.get(client.sessionId);
      if (player) {
        if (data.x !== undefined) player.x = Number(data.x);
        if (data.y !== undefined) player.y = Number(data.y);
        if (data.dir !== undefined) player.dir = String(data.dir);
        if (data.isFlipped !== undefined) player.isFlipped = Boolean(data.isFlipped);
        if (data.isSitting !== undefined) player.isSitting = Boolean(data.isSitting);
        if (data.currentAnimation !== undefined) player.currentAnimation = String(data.currentAnimation);

        this.broadcast('player_state_updated', { sessionId: client.sessionId, ...data }, { except: client });
      }
    });
  }

  /**
   * Handle client joining the room
   */
  onJoin(client: Client, options: any, auth: { uid: string }): void {
    if (!auth || !auth.uid) {
      console.error(`GardenRoomDynamic.onJoin: Join rejected - Missing auth data.`);
      client.leave(1001, "Authentication data missing.");
      return;
    }

    console.log(`Client ${client.sessionId} (UID: ${auth.uid}) joined GardenRoom ${this.getDynamicRoomId()}`);

    // Create player state
    const player = new PlayerState(
      client.sessionId,
      auth.uid,
      Math.floor(Math.random() * 800), // Random spawn X
      Math.floor(Math.random() * 600)  // Random spawn Y
    );

    this.state.players.set(client.sessionId, player);
  }

  /**
   * Handle client leaving the room
   */
  onLeave(client: Client, consented: boolean): void {
    const player = this.state.players.get(client.sessionId);
    const uid = player ? player.uid : 'Unknown';

    if (player) {
      this.state.players.delete(client.sessionId);
      console.log(`Client ${client.sessionId} (UID: ${uid}) left GardenRoom ${this.getDynamicRoomId()}. Consented: ${consented}`);
    }
  }
}
