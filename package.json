{"private": true, "name": "my-app", "version": "1.0.0", "description": "npm init template for bootstrapping an empty Colyseus project", "main": "build/index.js", "engines": {"node": ">= 20.9.0"}, "scripts": {"start": "tsx watch src/index.ts", "loadtest": "tsx loadtest/example.ts --room my_room --numClients 2", "build": "npm run clean && tsc", "clean": "<PERSON><PERSON><PERSON> build", "test": "mocha -r tsx test/**_test.ts --exit --timeout 15000", "setup-quest-system": "node scripts/setup-quest-system.js", "test-quest": "mocha -r tsx test/QuestSystem_test.ts --exit --timeout 15000", "setup-shop-system": "node scripts/setup-shop-system.js", "test-shop": "mocha -r tsx test/ShopSystem_test.ts --exit --timeout 15000", "test-inventory": "mocha -r tsx test/InventorySystem_test.ts --exit --timeout 15000", "setup-test-data": "node scripts/setup-test-data.js", "test-with-setup": "npm run setup-test-data && npm test"}, "author": "", "license": "UNLICENSED", "bugs": {"url": "https://github.com/colyseus/create-colyseus/issues"}, "homepage": "https://github.com/colyseus/create-colyseus#readme", "devDependencies": {"@colyseus/loadtest": "^0.16.0", "@colyseus/testing": "^0.16.0", "@types/chai": "^4.3.11", "@types/cors": "^2.8.18", "@types/express": "^4.17.22", "@types/jsonwebtoken": "^9.0.9", "@types/mocha": "^10.0.1", "@types/node": "^22.15.21", "@types/uuid": "^10.0.0", "chai": "^4.3.10", "mocha": "^10.2.0", "rimraf": "^5.0.0", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "dependencies": {"@colyseus/monitor": "^0.16.7", "@colyseus/playground": "^0.16.0", "@colyseus/tools": "^0.16.0", "@colyseus/ws-transport": "^0.16.5", "colyseus": "^0.16.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "uuid": "^11.1.0"}}