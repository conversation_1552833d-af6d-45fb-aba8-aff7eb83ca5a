/**
 * Real-time Friend Notification System Client Example
 * 
 * This example demonstrates how to use the real-time notification system
 * for friend-related events and general notifications.
 * 
 * To run this example:
 * 1. Make sure the server is running (npm start)
 * 2. Make sure you have registered users in the database
 * 3. Run: node examples/realtime-notification-client-example.js
 */

const axios = require('axios');
const { Client } = require('colyseus.js');

class RealtimeNotificationClientExample {
  constructor() {
    this.baseURL = 'http://localhost:2567';
    this.wsURL = 'ws://localhost:2567';
    this.client = new Client(this.wsURL);
    
    // Test user credentials
    this.testUser1 = {
      username: 'notifuser1',
      password: 'password123',
      token: null,
      uid: null
    };
    
    this.testUser2 = {
      username: 'notifuser2',
      password: 'password123',
      token: null,
      uid: null
    };

    this.notificationRooms = new Map(); // userId -> room
  }

  async runExample() {
    console.log('🔔 Real-time Friend Notification System Example');
    console.log('===============================================\n');

    try {
      // Step 1: Authenticate users
      console.log('1️⃣ Authenticating test users...');
      await this.authenticateUser(this.testUser1);
      await this.authenticateUser(this.testUser2);
      
      console.log(`✅ User 1 authenticated: ${this.testUser1.uid} (${this.testUser1.username})`);
      console.log(`✅ User 2 authenticated: ${this.testUser2.uid} (${this.testUser2.username})`);

      // Step 2: Connect to notification rooms
      console.log('\n2️⃣ Connecting to notification rooms...');
      const user1NotificationRoom = await this.connectToNotificationRoom(this.testUser1);
      const user2NotificationRoom = await this.connectToNotificationRoom(this.testUser2);
      
      console.log(`✅ User 1 connected to notification room`);
      console.log(`✅ User 2 connected to notification room`);

      // Step 3: Set up notification listeners
      console.log('\n3️⃣ Setting up notification listeners...');
      this.setupNotificationListeners(user1NotificationRoom, 'User 1');
      this.setupNotificationListeners(user2NotificationRoom, 'User 2');

      // Step 4: Test friend request flow
      console.log('\n4️⃣ Testing friend request flow...');
      
      // User 1 sends friend request to User 2
      console.log(`Sending friend request from ${this.testUser1.username} to ${this.testUser2.username}...`);
      await this.sendFriendRequest(this.testUser1, this.testUser2.uid);
      
      // Wait for notification
      await this.wait(2000);

      // User 2 accepts friend request
      console.log(`${this.testUser2.username} accepting friend request...`);
      await this.respondToFriendRequest(this.testUser2, this.testUser1.uid, 'accept');
      
      // Wait for notification
      await this.wait(2000);

      // Step 5: Test online status notifications
      console.log('\n5️⃣ Testing online status notifications...');
      
      // Get online friends
      user1NotificationRoom.send('get_online_friends');
      user2NotificationRoom.send('get_online_friends');
      
      await this.wait(1000);

      // Step 6: Test notification management
      console.log('\n6️⃣ Testing notification management...');
      
      // Get notifications via API
      const user2Notifications = await this.getNotifications(this.testUser2);
      console.log(`User 2 has ${user2Notifications.length} notifications`);
      
      if (user2Notifications.length > 0) {
        // Mark first notification as read
        await this.markNotificationAsRead(this.testUser2, user2Notifications[0].id);
        console.log('✅ Marked first notification as read');
      }

      // Step 7: Test custom notifications
      console.log('\n7️⃣ Testing custom notifications...');
      
      // Send custom notification
      await this.sendCustomNotification(this.testUser1, {
        toUserId: this.testUser2.uid,
        type: 'room_invitation',
        title: 'Room Invitation',
        message: `${this.testUser1.username} invited you to their home!`,
        data: { roomId: `home_${this.testUser1.uid}`, roomType: 'home' }
      });

      await this.wait(2000);

      // Step 8: Test friend removal (will trigger notification)
      console.log('\n8️⃣ Testing friend removal...');
      
      console.log(`${this.testUser1.username} removing ${this.testUser2.username} as friend...`);
      await this.removeFriend(this.testUser1, this.testUser2.uid);
      
      await this.wait(2000);

      // Step 9: Test blocking (will trigger notification)
      console.log('\n9️⃣ Testing user blocking...');
      
      console.log(`${this.testUser1.username} blocking ${this.testUser2.username}...`);
      await this.blockUser(this.testUser1, this.testUser2.uid);
      
      await this.wait(2000);

      // Step 10: Get final notification counts
      console.log('\n🔟 Getting final notification status...');
      
      const finalUser1Notifications = await this.getNotifications(this.testUser1);
      const finalUser2Notifications = await this.getNotifications(this.testUser2);
      
      console.log(`Final notification count - User 1: ${finalUser1Notifications.length}, User 2: ${finalUser2Notifications.length}`);

      // Step 11: Test mark all as read
      console.log('\n1️⃣1️⃣ Testing mark all notifications as read...');
      
      const markedCount = await this.markAllNotificationsAsRead(this.testUser2);
      console.log(`✅ Marked ${markedCount} notifications as read for User 2`);

      // Step 12: Test online status
      console.log('\n1️⃣2️⃣ Testing online status check...');
      
      const user1Status = await this.getOnlineStatus(this.testUser1, this.testUser1.uid);
      const user2Status = await this.getOnlineStatus(this.testUser1, this.testUser2.uid);
      
      console.log(`User 1 online status: ${user1Status.isOnline}`);
      console.log(`User 2 online status: ${user2Status.isOnline}`);

      // Wait a bit more to see any final notifications
      await this.wait(3000);

      // Step 13: Cleanup
      console.log('\n1️⃣3️⃣ Cleaning up connections...');
      await user1NotificationRoom.leave();
      await user2NotificationRoom.leave();
      
      console.log('✅ Disconnected from notification rooms');

      console.log('\n🎉 Real-time notification system demo completed successfully!');
      console.log('\nKey features demonstrated:');
      console.log('- Real-time friend request notifications');
      console.log('- Friend request acceptance/decline notifications');
      console.log('- Friend removal notifications');
      console.log('- User blocking notifications');
      console.log('- Online status tracking');
      console.log('- Custom notification sending');
      console.log('- Notification management (read/unread)');
      console.log('- Notification room connectivity');

    } catch (error) {
      console.error('❌ Demo failed:', error.response?.data || error.message);
    }
  }

  async authenticateUser(user) {
    try {
      // Try to login first
      const response = await axios.post(`${this.baseURL}/auth/login`, {
        username: user.username,
        password: user.password
      });
      
      user.token = response.data.token;
      user.uid = response.data.userId.toString();
    } catch (error) {
      if (error.response?.status === 401) {
        // User doesn't exist, try to register
        console.log(`User ${user.username} not found, registering...`);
        const registerResponse = await axios.post(`${this.baseURL}/auth/register`, {
          username: user.username,
          password: user.password
        });
        
        user.token = registerResponse.data.token;
        user.uid = registerResponse.data.userId.toString();
      } else {
        throw error;
      }
    }
  }

  async connectToNotificationRoom(user) {
    const room = await this.client.joinOrCreate('notification', {
      token: user.token,
      uid: user.uid
    });

    this.notificationRooms.set(user.uid, room);

    room.onError((code, message) => {
      console.error(`Notification room error for ${user.username}: ${code} - ${message}`);
    });

    room.onLeave((code) => {
      console.log(`${user.username} left notification room with code: ${code}`);
    });

    return room;
  }

  setupNotificationListeners(room, userLabel) {
    // Real-time notification received
    room.onMessage('notification', (notification) => {
      console.log(`🔔 [${userLabel}] Received notification: ${notification.type}`);
      console.log(`   Title: ${notification.title}`);
      console.log(`   Message: ${notification.message}`);
      console.log(`   From: ${notification.fromUsername || 'System'}`);
      if (notification.data) {
        console.log(`   Data:`, notification.data);
      }
    });

    // Friend online/offline status
    room.onMessage('friend_online', (data) => {
      console.log(`👋 [${userLabel}] Friend ${data.username} came online`);
    });

    room.onMessage('friend_offline', (data) => {
      console.log(`💤 [${userLabel}] Friend ${data.username} went offline`);
    });

    // Online friends list
    room.onMessage('online_friends_list', (data) => {
      console.log(`👥 [${userLabel}] Online friends: ${data.friends.length}`);
      data.friends.forEach(friend => {
        console.log(`   - ${friend.username} (in ${friend.currentRoom || 'lobby'})`);
      });
    });

    // Notification marked as read
    room.onMessage('notification_marked_read', (data) => {
      console.log(`✅ [${userLabel}] Notification ${data.notificationId} marked as read`);
    });

    // All notifications marked as read
    room.onMessage('all_notifications_marked_read', (data) => {
      console.log(`✅ [${userLabel}] ${data.count} notifications marked as read`);
    });

    // Recent notifications
    room.onMessage('recent_notifications', (data) => {
      console.log(`📋 [${userLabel}] Received ${data.notifications.length} recent notifications`);
    });

    // Room joined confirmation
    room.onMessage('notification_room_joined', (data) => {
      console.log(`🏠 [${userLabel}] Joined notification room (${data.onlineCount} users online)`);
    });
  }

  async sendFriendRequest(fromUser, toUserId) {
    try {
      const response = await axios.post(`${this.baseURL}/api/friends/request`, {
        targetUserId: toUserId
      }, {
        headers: {
          'Authorization': `Bearer ${fromUser.token}`
        }
      });
      
      console.log(`✅ Friend request sent: ${response.data.message}`);
    } catch (error) {
      console.error('❌ Failed to send friend request:', error.response?.data?.message || error.message);
    }
  }

  async respondToFriendRequest(user, senderUserId, response) {
    try {
      const apiResponse = await axios.post(`${this.baseURL}/api/friends/response`, {
        senderUserId,
        response
      }, {
        headers: {
          'Authorization': `Bearer ${user.token}`
        }
      });
      
      console.log(`✅ Friend request ${response}: ${apiResponse.data.message}`);
    } catch (error) {
      console.error(`❌ Failed to ${response} friend request:`, error.response?.data?.message || error.message);
    }
  }

  async removeFriend(user, targetUserId) {
    try {
      const response = await axios.post(`${this.baseURL}/api/friends/remove`, {
        targetUserId
      }, {
        headers: {
          'Authorization': `Bearer ${user.token}`
        }
      });
      
      console.log(`✅ Friend removed: ${response.data.message}`);
    } catch (error) {
      console.error('❌ Failed to remove friend:', error.response?.data?.message || error.message);
    }
  }

  async blockUser(user, targetUserId) {
    try {
      const response = await axios.post(`${this.baseURL}/api/friends/block`, {
        targetUserId
      }, {
        headers: {
          'Authorization': `Bearer ${user.token}`
        }
      });
      
      console.log(`✅ User blocked: ${response.data.message}`);
    } catch (error) {
      console.error('❌ Failed to block user:', error.response?.data?.message || error.message);
    }
  }

  async getNotifications(user) {
    try {
      const response = await axios.get(`${this.baseURL}/api/notifications`, {
        headers: {
          'Authorization': `Bearer ${user.token}`
        }
      });
      
      return response.data.data.notifications;
    } catch (error) {
      console.error('❌ Failed to get notifications:', error.response?.data?.message || error.message);
      return [];
    }
  }

  async markNotificationAsRead(user, notificationId) {
    try {
      const response = await axios.put(`${this.baseURL}/api/notifications/${notificationId}/read`, {}, {
        headers: {
          'Authorization': `Bearer ${user.token}`
        }
      });
      
      console.log(`✅ ${response.data.message}`);
    } catch (error) {
      console.error('❌ Failed to mark notification as read:', error.response?.data?.message || error.message);
    }
  }

  async markAllNotificationsAsRead(user) {
    try {
      const response = await axios.put(`${this.baseURL}/api/notifications/read-all`, {}, {
        headers: {
          'Authorization': `Bearer ${user.token}`
        }
      });
      
      return response.data.data.markedCount;
    } catch (error) {
      console.error('❌ Failed to mark all notifications as read:', error.response?.data?.message || error.message);
      return 0;
    }
  }

  async sendCustomNotification(fromUser, notificationData) {
    try {
      const response = await axios.post(`${this.baseURL}/api/notifications/send`, notificationData, {
        headers: {
          'Authorization': `Bearer ${fromUser.token}`
        }
      });
      
      console.log(`✅ Custom notification sent: ${response.data.message}`);
    } catch (error) {
      console.error('❌ Failed to send custom notification:', error.response?.data?.message || error.message);
    }
  }

  async getOnlineStatus(user, targetUserId) {
    try {
      const response = await axios.get(`${this.baseURL}/api/notifications/online-status/${targetUserId}`, {
        headers: {
          'Authorization': `Bearer ${user.token}`
        }
      });
      
      return response.data.data;
    } catch (error) {
      console.error('❌ Failed to get online status:', error.response?.data?.message || error.message);
      return { isOnline: false };
    }
  }

  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Check if required packages are available
try {
  require('axios');
  require('colyseus.js');
} catch (error) {
  console.error('❌ This example requires axios and colyseus.js. Please install them with:');
  console.error('npm install axios colyseus.js');
  process.exit(1);
}

// Run the example
const example = new RealtimeNotificationClientExample();
example.runExample().catch(error => {
  console.error('❌ Example failed:', error.message);
  process.exit(1);
});
