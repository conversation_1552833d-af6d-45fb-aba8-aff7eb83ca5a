"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatMessage = void 0;
const schema_1 = require("@colyseus/schema");
class ChatMessage extends schema_1.Schema {
    constructor(senderSessionId, senderUid, message, channel = 'global') {
        super();
        this.senderSessionId = ''; // Client's sessionId who sent the message
        this.senderUid = ''; // User ID of the sender
        this.message = '';
        this.timestamp = Date.now(); // Unix timestamp
        this.channel = 'global'; // e.g., 'global', 'private', 'trade'
        this.senderSessionId = senderSessionId;
        this.senderUid = senderUid;
        this.message = message;
        this.channel = channel;
        this.timestamp = Date.now();
    }
}
exports.ChatMessage = ChatMessage;
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], ChatMessage.prototype, "senderSessionId", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], ChatMessage.prototype, "senderUid", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], ChatMessage.prototype, "message", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], ChatMessage.prototype, "timestamp", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], ChatMessage.prototype, "channel", void 0);
