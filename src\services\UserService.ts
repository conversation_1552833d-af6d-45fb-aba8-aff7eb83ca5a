import pool from '../utils/db'; // Database connection pool utility
import { RowDataPacket } from 'mysql2';

/**
 * @export
 * @interface UserBasicInfo
 * @description Represents basic public information for a user.
 * @property {string} id - The unique identifier of the user.
 * @property {string} username - The username of the user.
 * // Add other fields here if they are considered basic and public, e.g., avatar_url.
 */
export interface UserBasicInfo extends RowDataPacket {
  id: string; 
  username: string;
}

/**
 * @class UserService
 * @description Provides methods for fetching user-related data.
 *              Implemented as a singleton.
 */
class UserService {
  private static instance: UserService;

  /**
   * Private constructor to enforce singleton pattern.
   * @private
   */
  private constructor() {
    // Initialization logic if needed
  }

  /**
   * Gets the singleton instance of the UserService.
   * @public
   * @static
   * @returns {UserService} The singleton instance.
   */
  public static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  /**
   * Fetches basic public information for a specific user by their ID.
   * @param {string} userId - The ID of the user to fetch.
   * @returns {Promise<UserBasicInfo | null>} A promise that resolves with the user's basic info if found, otherwise null.
   * @throws {Error} If `userId` is not provided or if a database error occurs.
   */
  async getUserBasicInfo(userId: string): Promise<UserBasicInfo | null> {
    if (!userId || typeof userId !== 'string') {
      throw new Error('UserService: getUserBasicInfo - userId must be a non-empty string.');
    }
    try {
      // Select only publicly safe information.
      const [rows] = await pool.query<UserBasicInfo[]>(
        'SELECT id, username FROM users WHERE id = ?',
        [userId]
      );
      if (rows.length > 0) {
        // Ensure 'id' is returned as a string if it's a BIGINT in DB for consistency.
        return { ...rows[0], id: rows[0].id.toString() }; 
      }
      console.log(`UserService: No user found with userId '${userId}'.`);
      return null; // User not found is a valid case.
    } catch (error: any) {
      console.error(`UserService: Error fetching basic info for userId '${userId}': ${error.message}`);
      throw new Error(`Failed to fetch basic info for user '${userId}' due to a server error.`);
    }
  }

  /**
   * Fetches basic public information for multiple users by their IDs.
   * @param {string[]} userIds - An array of user IDs to fetch.
   * @returns {Promise<Map<string, UserBasicInfo>>} A promise that resolves with a map where keys are user IDs and values are user basic info.
   *                                                Users not found will not be included in the map.
   * @throws {Error} If `userIds` is not a valid array or if a database error occurs.
   */
  async getMultipleUserBasicInfo(userIds: string[]): Promise<Map<string, UserBasicInfo>> {
    const userInfoMap = new Map<string, UserBasicInfo>();
    if (!Array.isArray(userIds) || userIds.length === 0) {
      // Return an empty map if no user IDs are provided.
      return userInfoMap; 
    }

    // Filter out any invalid user IDs to prevent SQL errors.
    const validUserIds = userIds.filter(id => id && typeof id === 'string');
    if (validUserIds.length === 0) {
      console.warn('UserService: getMultipleUserBasicInfo - No valid userIds provided after filtering.');
      return userInfoMap;
    }

    try {
      // Create placeholders for the IN clause: e.g., (?, ?, ?)
      const placeholders = validUserIds.map(() => '?').join(',');
      const [rows] = await pool.query<UserBasicInfo[]>(
        `SELECT id, username FROM users WHERE id IN (${placeholders})`,
        validUserIds
      );

      for (const user of rows) {
        // Ensure 'id' is stored as a string in the map.
        userInfoMap.set(user.id.toString(), { ...user, id: user.id.toString() });
      }
      return userInfoMap;
    } catch (error: any) {
      console.error(`UserService: Error fetching basic info for multiple users: ${error.message}`);
      throw new Error('Failed to fetch basic info for multiple users due to a server error.');
    }
  }
}

// Export a singleton instance of UserService.
export default UserService.getInstance();
