const mysql = require('mysql2/promise');
require('dotenv').config();

async function createPlantingTables() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🌱 Creating Planting System Tables...');
    
    // 1. Garden Plots Table
    console.log('📊 Creating garden_plots table...');
    await connection.query(`
      CREATE TABLE IF NOT EXISTS \`garden_plots\` (
        \`id\` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique plot ID',
        \`user_id\` BIGINT NOT NULL COMMENT 'Garden owner user ID',
        \`plot_index\` INT NOT NULL COMMENT 'Plot position index in garden (0-based)',
        \`is_unlocked\` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether this plot is unlocked',
        \`unlock_level_required\` INT NOT NULL DEFAULT 1 COMMENT 'Required planting level to unlock',
        \`unlock_cost_gold\` INT NOT NULL DEFAULT 0 COMMENT 'Gold cost to unlock',
        \`unlock_cost_diamonds\` INT NOT NULL DEFAULT 0 COMMENT 'Diamond cost to unlock',
        \`unlocked_at\` TIMESTAMP NULL COMMENT 'When plot was unlocked',
        \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY \`unique_user_plot\` (\`user_id\`, \`plot_index\`),
        INDEX \`idx_user_unlocked\` (\`user_id\`, \`is_unlocked\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Garden plots for each user'
    `);
    console.log('✅ garden_plots table created');

    // 2. Flower Seeds Table
    console.log('📊 Creating flower_seeds table...');
    await connection.query(`
      CREATE TABLE IF NOT EXISTS \`flower_seeds\` (
        \`seed_id\` VARCHAR(255) PRIMARY KEY COMMENT 'Unique seed template ID',
        \`seed_name\` VARCHAR(255) NOT NULL COMMENT 'Seed display name',
        \`description\` TEXT NULL COMMENT 'Seed description',
        \`rarity\` ENUM('common', 'uncommon', 'rare', 'epic', 'legendary') NOT NULL DEFAULT 'common' COMMENT 'Seed rarity',
        \`growth_time_minutes\` INT NOT NULL COMMENT 'Total growth time in minutes',
        \`required_level\` INT NOT NULL DEFAULT 1 COMMENT 'Required planting level',
        \`base_yield\` INT NOT NULL DEFAULT 1 COMMENT 'Base number of flowers produced',
        \`max_yield\` INT NOT NULL DEFAULT 3 COMMENT 'Maximum possible yield with bonuses',
        \`water_intervals\` INT NOT NULL DEFAULT 2 COMMENT 'Number of times watering is needed',
        \`fertilizer_boost\` DECIMAL(3,2) NOT NULL DEFAULT 1.0 COMMENT 'Fertilizer effectiveness multiplier',
        \`rare_drop_chance\` DECIMAL(5,4) NOT NULL DEFAULT 0.0 COMMENT 'Chance for rare material drops (0-1)',
        \`seed_data\` JSON NULL COMMENT 'Additional seed configuration',
        \`is_available\` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether seed is available for planting',
        \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX \`idx_rarity_level\` (\`rarity\`, \`required_level\`),
        INDEX \`idx_available\` (\`is_available\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Flower seed templates'
    `);
    console.log('✅ flower_seeds table created');

    // 3. Planted Flowers Table
    console.log('📊 Creating planted_flowers table...');
    await connection.query(`
      CREATE TABLE IF NOT EXISTS \`planted_flowers\` (
        \`id\` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique planting record ID',
        \`user_id\` BIGINT NOT NULL COMMENT 'Garden owner user ID',
        \`plot_id\` BIGINT NOT NULL COMMENT 'Garden plot where flower is planted',
        \`seed_id\` VARCHAR(255) NOT NULL COMMENT 'Seed template used',
        \`plant_stage\` ENUM('planted', 'sprouting', 'growing', 'blooming', 'mature', 'withered') NOT NULL DEFAULT 'planted' COMMENT 'Current growth stage',
        \`planted_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When seed was planted',
        \`last_watered\` TIMESTAMP NULL COMMENT 'Last watering time',
        \`last_fertilized\` TIMESTAMP NULL COMMENT 'Last fertilizer application time',
        \`water_count\` INT NOT NULL DEFAULT 0 COMMENT 'Number of times watered',
        \`fertilizer_count\` INT NOT NULL DEFAULT 0 COMMENT 'Number of times fertilized',
        \`growth_boost\` DECIMAL(3,2) NOT NULL DEFAULT 1.0 COMMENT 'Growth speed multiplier from bonuses',
        \`yield_boost\` DECIMAL(3,2) NOT NULL DEFAULT 1.0 COMMENT 'Yield multiplier from bonuses',
        \`expected_harvest_time\` TIMESTAMP NULL COMMENT 'When flower will be ready for harvest',
        \`is_ready_for_harvest\` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether flower is ready to harvest',
        \`needs_water\` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether flower needs watering',
        \`needs_care\` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether flower needs general care',
        \`plant_data\` JSON NULL COMMENT 'Additional plant state data',
        \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX \`idx_user_stage\` (\`user_id\`, \`plant_stage\`),
        INDEX \`idx_harvest_ready\` (\`is_ready_for_harvest\`, \`expected_harvest_time\`),
        INDEX \`idx_needs_care\` (\`user_id\`, \`needs_water\`, \`needs_care\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Currently planted flowers'
    `);
    console.log('✅ planted_flowers table created');

    // 4. User Planting Levels Table
    console.log('📊 Creating user_planting_levels table...');
    await connection.query(`
      CREATE TABLE IF NOT EXISTS \`user_planting_levels\` (
        \`user_id\` BIGINT PRIMARY KEY COMMENT 'User ID',
        \`current_level\` INT NOT NULL DEFAULT 1 COMMENT 'Current planting level',
        \`current_exp\` BIGINT NOT NULL DEFAULT 0 COMMENT 'Current experience points',
        \`total_plants\` BIGINT NOT NULL DEFAULT 0 COMMENT 'Total number of plants grown',
        \`total_harvests\` BIGINT NOT NULL DEFAULT 0 COMMENT 'Total number of successful harvests',
        \`rare_harvests\` BIGINT NOT NULL DEFAULT 0 COMMENT 'Number of rare material harvests',
        \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX \`idx_level\` (\`current_level\`),
        INDEX \`idx_exp\` (\`current_exp\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User planting levels and statistics'
    `);
    console.log('✅ user_planting_levels table created');

    // 5. Planting Operations Log Table
    console.log('📊 Creating planting_operations_log table...');
    await connection.query(`
      CREATE TABLE IF NOT EXISTS \`planting_operations_log\` (
        \`id\` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique log entry ID',
        \`user_id\` BIGINT NOT NULL COMMENT 'User who performed the operation',
        \`operation_type\` ENUM('plant', 'water', 'fertilize', 'harvest', 'clear', 'unlock_plot') NOT NULL COMMENT 'Type of operation',
        \`plot_id\` BIGINT NULL COMMENT 'Garden plot involved (if applicable)',
        \`planted_flower_id\` BIGINT NULL COMMENT 'Planted flower involved (if applicable)',
        \`seed_id\` VARCHAR(255) NULL COMMENT 'Seed involved (if applicable)',
        \`exp_gained\` INT NOT NULL DEFAULT 0 COMMENT 'Experience points gained',
        \`items_received\` JSON NULL COMMENT 'Items received from operation',
        \`operation_data\` JSON NULL COMMENT 'Additional operation metadata',
        \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX \`idx_user_operation\` (\`user_id\`, \`operation_type\`, \`created_at\`),
        INDEX \`idx_created_at\` (\`created_at\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Log of all planting operations'
    `);
    console.log('✅ planting_operations_log table created');

    // 6. Planting Achievements Table
    console.log('📊 Creating planting_achievements table...');
    await connection.query(`
      CREATE TABLE IF NOT EXISTS \`planting_achievements\` (
        \`achievement_id\` VARCHAR(255) PRIMARY KEY COMMENT 'Unique achievement ID',
        \`achievement_name\` VARCHAR(255) NOT NULL COMMENT 'Achievement display name',
        \`description\` TEXT NOT NULL COMMENT 'Achievement description',
        \`achievement_type\` ENUM('plants_grown', 'harvests_completed', 'rare_finds', 'level_reached', 'plots_unlocked', 'consecutive_days') NOT NULL COMMENT 'Type of achievement',
        \`target_value\` BIGINT NOT NULL COMMENT 'Target value to complete achievement',
        \`reward_gold\` INT NOT NULL DEFAULT 0 COMMENT 'Gold reward',
        \`reward_diamonds\` INT NOT NULL DEFAULT 0 COMMENT 'Diamond reward',
        \`reward_items\` JSON NULL COMMENT 'Item rewards',
        \`is_active\` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether achievement is active',
        \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Planting system achievements'
    `);
    console.log('✅ planting_achievements table created');

    // 7. User Planting Achievements Table
    console.log('📊 Creating user_planting_achievements table...');
    await connection.query(`
      CREATE TABLE IF NOT EXISTS \`user_planting_achievements\` (
        \`user_id\` BIGINT NOT NULL COMMENT 'User ID',
        \`achievement_id\` VARCHAR(255) NOT NULL COMMENT 'Achievement ID',
        \`current_progress\` BIGINT NOT NULL DEFAULT 0 COMMENT 'Current progress towards achievement',
        \`is_completed\` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether achievement is completed',
        \`completed_at\` TIMESTAMP NULL COMMENT 'When achievement was completed',
        \`reward_claimed\` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether reward has been claimed',
        \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (\`user_id\`, \`achievement_id\`),
        INDEX \`idx_user_completed\` (\`user_id\`, \`is_completed\`),
        INDEX \`idx_completed_unclaimed\` (\`is_completed\`, \`reward_claimed\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User progress on planting achievements'
    `);
    console.log('✅ user_planting_achievements table created');

    console.log('🎉 All planting system tables created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating planting tables:', error);
  } finally {
    await connection.end();
  }
}

createPlantingTables();
