const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkOutfitSystem() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🔍 Checking Outfit System Status...');
    
    // Check if outfit tables exist
    const [tables] = await connection.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND (TABLE_NAME = 'outfit_templates' OR TABLE_NAME = 'user_outfits')
    `, [process.env.DB_NAME]);
    
    console.log('📋 Found outfit tables:', tables.map(t => t.TABLE_NAME));
    
    let outfitTablesExist = false;
    let templateCount = 0;
    let userOutfitCount = 0;
    
    // Check outfit_templates table
    try {
      const [templateResult] = await connection.query('SELECT COUNT(*) as count FROM outfit_templates');
      templateCount = templateResult[0].count;
      console.log(`👔 outfit_templates table has ${templateCount} templates`);
      outfitTablesExist = true;
    } catch (error) {
      console.log('❌ outfit_templates table does not exist');
    }
    
    // Check user_outfits table
    try {
      const [outfitResult] = await connection.query('SELECT COUNT(*) as count FROM user_outfits');
      userOutfitCount = outfitResult[0].count;
      console.log(`👤 user_outfits table has ${userOutfitCount} user outfits`);
    } catch (error) {
      console.log('❌ user_outfits table does not exist');
    }
    
    // Check if OutfitService exists
    let outfitServiceExists = false;
    try {
      const fs = require('fs');
      if (fs.existsSync('src/services/OutfitService.ts')) {
        console.log('✅ OutfitService.ts exists');
        outfitServiceExists = true;
      } else {
        console.log('❌ OutfitService.ts missing');
      }
    } catch (error) {
      console.log('❌ Error checking OutfitService:', error.message);
    }
    
    // Check if OutfitState models exist
    let outfitModelsExist = false;
    try {
      const fs = require('fs');
      if (fs.existsSync('src/models/OutfitState.ts')) {
        console.log('✅ OutfitState.ts exists');
        outfitModelsExist = true;
      } else {
        console.log('❌ OutfitState.ts missing');
      }
    } catch (error) {
      console.log('❌ Error checking OutfitState:', error.message);
    }
    
    // Check if PlayerState has outfit integration
    let playerStateIntegrated = false;
    try {
      const fs = require('fs');
      if (fs.existsSync('src/models/PlayerState.ts')) {
        const playerStateContent = fs.readFileSync('src/models/PlayerState.ts', 'utf8');
        if (playerStateContent.includes('OutfitState') && playerStateContent.includes('currentOutfit')) {
          console.log('✅ PlayerState has OutfitState integration');
          playerStateIntegrated = true;
        } else {
          console.log('❌ PlayerState missing OutfitState integration');
        }
      } else {
        console.log('❌ PlayerState.ts missing');
      }
    } catch (error) {
      console.log('❌ Error checking PlayerState:', error.message);
    }
    
    // Check if outfit routes exist
    let outfitRoutesExist = false;
    try {
      const fs = require('fs');
      if (fs.existsSync('src/routes/outfitRoutes.ts')) {
        console.log('✅ outfitRoutes.ts exists');
        outfitRoutesExist = true;
      } else {
        console.log('❌ outfitRoutes.ts missing');
      }
    } catch (error) {
      console.log('❌ Error checking outfit routes:', error.message);
    }
    
    // Summary
    console.log('\n📊 Outfit System Completion Status:');
    console.log('=====================================');
    console.log(`Database Tables: ${outfitTablesExist ? '✅' : '❌'}`);
    console.log(`OutfitService: ${outfitServiceExists ? '✅' : '❌'}`);
    console.log(`OutfitState Models: ${outfitModelsExist ? '✅' : '❌'}`);
    console.log(`PlayerState Integration: ${playerStateIntegrated ? '✅' : '❌'}`);
    console.log(`API Routes: ${outfitRoutesExist ? '✅' : '❌'}`);
    
    const completionPercentage = [
      outfitTablesExist,
      outfitServiceExists,
      outfitModelsExist,
      playerStateIntegrated,
      outfitRoutesExist
    ].filter(Boolean).length * 20;
    
    console.log(`\n🎯 Overall Completion: ${completionPercentage}%`);
    
    if (completionPercentage === 100) {
      console.log('🎉 Outfit system is fully implemented!');
    } else if (completionPercentage >= 80) {
      console.log('🚀 Outfit system is mostly complete, minor issues to fix');
    } else if (completionPercentage >= 60) {
      console.log('⚠️  Outfit system is partially complete, needs work');
    } else {
      console.log('❌ Outfit system needs significant development');
    }
    
    // Detailed analysis
    console.log('\n📋 Detailed Analysis:');
    console.log('=====================');
    
    if (outfitTablesExist) {
      console.log(`✅ Database: ${templateCount} outfit templates, ${userOutfitCount} user outfits`);
    } else {
      console.log('❌ Database: Missing outfit tables - need to run schema setup');
    }
    
    if (outfitServiceExists) {
      console.log('✅ Backend: OutfitService provides database operations');
    } else {
      console.log('❌ Backend: Missing OutfitService implementation');
    }
    
    if (outfitModelsExist) {
      console.log('✅ Models: OutfitState schema for real-time synchronization');
    } else {
      console.log('❌ Models: Missing OutfitState schema definitions');
    }
    
    if (playerStateIntegrated) {
      console.log('✅ Integration: PlayerState includes outfit management');
    } else {
      console.log('❌ Integration: PlayerState needs outfit integration');
    }
    
    if (outfitRoutesExist) {
      console.log('✅ API: REST endpoints for outfit management');
    } else {
      console.log('❌ API: Missing outfit management endpoints');
    }
    
  } catch (error) {
    console.error('❌ Error checking outfit system:', error);
  } finally {
    await connection.end();
  }
}

checkOutfitSystem();
