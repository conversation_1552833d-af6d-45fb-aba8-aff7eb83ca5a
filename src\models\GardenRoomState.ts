import { Schema, MapSchema, type } from '@colyseus/schema';
import { PlayerState } from './PlayerState'; // Assuming PlayerState is in the same directory or adjust path
import { GardenPlotState } from './GardenPlotState'; // Assuming GardenPlotState is in the same directory

export class GardenRoomState extends Schema {
  @type('string') ownerUid: string = ''; // UID of the player who owns this garden
  @type('string') gardenBackgroundId: string = 'default_garden_background'; // Identifier for the garden's background
  
  @type({ map: PlayerState })
  players = new MapSchema<PlayerState>();
  
  @type({ map: GardenPlotState })
  plots = new MapSchema<GardenPlotState>(); // Plots in the garden

  @type('string') accessLevel: 'private' | 'friends_only' | 'public' = 'private'; // Who can access this garden

  constructor(ownerUid: string, accessLevel: 'private' | 'friends_only' | 'public' = 'private', gardenBackgroundId: string = 'default_garden_background') {
    super();
    this.ownerUid = ownerUid;
    this.accessLevel = accessLevel;
    this.gardenBackgroundId = gardenBackgroundId;
  }
}
