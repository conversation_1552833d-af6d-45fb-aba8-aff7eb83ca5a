-- Sample quest data based on the XML structure provided
-- This script inserts sample quests, steps, and NPC responses

-- Insert sample quests
INSERT INTO quests (id, title, accept_message_0, accept_message_1, accept_message_2, decline_message_0, reward_message_0_0, reward_message_0_1) VALUES
(1, 'Welcome to Pixie Hollow', 
 'Welcome to Pixie Hollow! I''m so excited to show you around.',
 'There''s so much to see and do here. Let me give you a tour!',
 'Follow me and I''ll introduce you to some of our friends.',
 'Oh, that''s okay! Come back when you''re ready to explore.',
 'Wonderful! You''ve completed your first quest.',
 'Here''s a little something to get you started on your journey.');

-- Insert quest steps for the welcome quest
INSERT INTO quest_steps (quest_id, step_order, goal, summary, intro_0, intro_1, intro_2, intro_3, intro_4) VALUES
(1, 0, 'Talk to <PERSON>mist', 'Meet <PERSON>mist, the water fairy',
 'Let''s start by meeting <PERSON><PERSON>.',
 'She''s our water fairy and loves to help newcomers.',
 'You can find her near the water fountain.',
 'Just walk up to her and say hello!',
 'She''ll be delighted to meet you.'),

(1, 1, 'Visit <PERSON>''s workshop', '<PERSON>rn about tinkering from Tinker Bell',
 'Now let''s visit Tinker Bell''s workshop.',
 'She''s our most talented tinker fairy.',
 'Her workshop is full of amazing inventions.',
 'She can teach you about crafting and fixing things.',
 'Head over to the workshop area to find her.'),

(1, 2, 'Meet Fawn in the garden', 'Get to know Fawn, the animal fairy',
 'Next, let''s meet Fawn in the garden.',
 'She''s our animal fairy and knows all about creatures.',
 'The garden is where she spends most of her time.',
 'She can tell you about the different animals in Pixie Hollow.',
 'Look for her among the flowers and plants.'),

(1, 3, 'Talk to Rosetta about flowers', 'Learn about gardening from Rosetta',
 'Finally, let''s talk to Rosetta about flowers.',
 'She''s our garden fairy and has the most beautiful garden.',
 'She knows everything about plants and flowers.',
 'She can teach you how to grow your own garden.',
 'Find her tending to her precious flowers.');

-- Insert NPC responses for quest steps
-- Silvermist responses (NPC ID: 15503)
INSERT INTO quest_step_responses (step_id, npc_id, response_type, prompt, message_0, message_1, message_2) VALUES
-- Step 0 - Silvermist unfinished
((SELECT id FROM quest_steps WHERE quest_id = 1 AND step_order = 0), 15503, 'unfinished', 
 'Talk to Silvermist',
 'Hello there! You must be new to Pixie Hollow.',
 'I''m Silvermist, and I work with water magic.',
 'Welcome to our magical home!'),

-- Step 0 - Silvermist finished  
((SELECT id FROM quest_steps WHERE quest_id = 1 AND step_order = 0), 15503, 'finished',
 'You''ve already talked to Silvermist',
 'It was so nice meeting you!',
 'I hope you''re enjoying your tour of Pixie Hollow.',
 'Have fun exploring!');

-- Tinker Bell responses (NPC ID: 15504)
INSERT INTO quest_step_responses (step_id, npc_id, response_type, prompt, message_0, message_1, message_2) VALUES
-- Step 1 - Tinker Bell unfinished
((SELECT id FROM quest_steps WHERE quest_id = 1 AND step_order = 1), 15504, 'unfinished',
 'Visit Tinker Bell''s workshop',
 'Oh, a visitor! How wonderful!',
 'Welcome to my workshop. This is where all the magic happens.',
 'I love tinkering and creating new inventions!'),

-- Step 1 - Tinker Bell finished
((SELECT id FROM quest_steps WHERE quest_id = 1 AND step_order = 1), 15504, 'finished',
 'You''ve already visited the workshop',
 'Thanks for stopping by my workshop!',
 'Feel free to come back anytime you need something fixed.',
 'Happy tinkering!');

-- Fawn responses (NPC ID: 15500)
INSERT INTO quest_step_responses (step_id, npc_id, response_type, prompt, message_0, message_1, message_2) VALUES
-- Step 2 - Fawn unfinished
((SELECT id FROM quest_steps WHERE quest_id = 1 AND step_order = 2), 15500, 'unfinished',
 'Meet Fawn in the garden',
 'Hello! I''m Fawn, and I love all the animals here.',
 'The garden is full of wonderful creatures.',
 'Would you like to learn about them?'),

-- Step 2 - Fawn finished
((SELECT id FROM quest_steps WHERE quest_id = 1 AND step_order = 2), 15500, 'finished',
 'You''ve already met Fawn',
 'It was great meeting you!',
 'I hope you''ll visit the animals again soon.',
 'They really enjoyed meeting you too!');

-- Rosetta responses (NPC ID: 15502)
INSERT INTO quest_step_responses (step_id, npc_id, response_type, prompt, message_0, message_1, message_2) VALUES
-- Step 3 - Rosetta unfinished
((SELECT id FROM quest_steps WHERE quest_id = 1 AND step_order = 3), 15502, 'unfinished',
 'Talk to Rosetta about flowers',
 'Oh my! A new fairy! How delightful!',
 'I''m Rosetta, and I simply adore flowers.',
 'Would you like to learn about gardening?'),

-- Step 3 - Rosetta finished
((SELECT id FROM quest_steps WHERE quest_id = 1 AND step_order = 3), 15502, 'finished',
 'You''ve already talked to Rosetta',
 'Thank you for visiting my garden!',
 'I hope you''ll grow beautiful flowers of your own.',
 'Remember, every flower needs love and care!');

-- Insert a second sample quest
INSERT INTO quests (id, title, accept_message_0, accept_message_1, decline_message_0, reward_message_0_0) VALUES
(2, 'Garden Helper',
 'Would you like to help me tend to the garden?',
 'There''s always work to be done with the plants.',
 'That''s alright, come back if you change your mind.',
 'Thank you so much for helping with the garden!');

-- Insert steps for the garden helper quest
INSERT INTO quest_steps (quest_id, step_order, goal, summary, intro_0, intro_1) VALUES
(2, 0, 'Water 3 flower plots', 'Help water the flowers in the garden',
 'The flowers are looking a bit thirsty.',
 'Could you help me water them?'),

(2, 1, 'Plant 2 seeds', 'Plant new seeds in empty plots',
 'We have some empty plots that need new seeds.',
 'Would you help me plant them?'),

(2, 2, 'Harvest ripe vegetables', 'Collect the vegetables that are ready',
 'Some of our vegetables are ready to harvest.',
 'Let''s collect them together!');

-- Insert responses for the garden helper quest
INSERT INTO quest_step_responses (step_id, npc_id, response_type, prompt, message_0, message_1) VALUES
-- Garden helper step responses (using Rosetta as the quest giver)
((SELECT id FROM quest_steps WHERE quest_id = 2 AND step_order = 0), 15502, 'unfinished',
 'Water the flower plots',
 'The flowers need water to grow big and strong.',
 'Just click on each plot to water it.'),

((SELECT id FROM quest_steps WHERE quest_id = 2 AND step_order = 0), 15502, 'finished',
 'You''ve watered the flowers',
 'Perfect! The flowers look much happier now.',
 'Thank you for your help!'),

((SELECT id FROM quest_steps WHERE quest_id = 2 AND step_order = 1), 15502, 'unfinished',
 'Plant the seeds',
 'These seeds will grow into beautiful plants.',
 'Just place them in the empty plots.'),

((SELECT id FROM quest_steps WHERE quest_id = 2 AND step_order = 1), 15502, 'finished',
 'You''ve planted the seeds',
 'Wonderful! Those seeds will grow into lovely plants.',
 'You''re becoming quite the gardener!'),

((SELECT id FROM quest_steps WHERE quest_id = 2 AND step_order = 2), 15502, 'unfinished',
 'Harvest the vegetables',
 'These vegetables are ripe and ready to pick.',
 'Click on them to harvest them.'),

((SELECT id FROM quest_steps WHERE quest_id = 2 AND step_order = 2), 15502, 'finished',
 'You''ve harvested the vegetables',
 'Excellent work! These vegetables will make a delicious meal.',
 'You''re a natural at gardening!');
