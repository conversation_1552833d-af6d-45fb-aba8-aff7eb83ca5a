import { BaseError } from './BaseError';

/**
 * Shop-related error class
 * Used for shop system specific errors
 */
export class ShopError extends BaseError {
  constructor(
    message: string,
    httpCode: number = 400,
    details?: any
  ) {
    super('ShopError', httpCode, message, true, details);
  }

  /**
   * Shop item not found error
   */
  static itemNotFound(itemId: number | string): ShopError {
    return new ShopError(
      `Shop item with ID ${itemId} not found`,
      404,
      { itemId }
    );
  }

  /**
   * Shop category not found error
   */
  static categoryNotFound(categoryId: number | string): ShopError {
    return new ShopError(
      `Shop category with ID ${categoryId} not found`,
      404,
      { categoryId }
    );
  }

  /**
   * Insufficient currency error
   */
  static insufficientCurrency(currencyType: string, required: number, available: number): ShopError {
    return new ShopError(
      `Insufficient ${currencyType}. Required: ${required}, Available: ${available}`,
      400,
      { currencyType, required, available }
    );
  }

  /**
   * Item not available error
   */
  static itemNotAvailable(itemId: number | string, reason?: string): ShopError {
    const message = reason
      ? `Shop item ${itemId} is not available: ${reason}`
      : `Shop item ${itemId} is not available`;
    return new ShopError(
      message,
      400,
      { itemId, reason }
    );
  }

  /**
   * Purchase limit exceeded error
   */
  static purchaseLimitExceeded(itemId: number | string, limit: number): ShopError {
    return new ShopError(
      `Purchase limit exceeded for item ${itemId}. Maximum allowed: ${limit}`,
      400,
      { itemId, limit }
    );
  }

  /**
   * Out of stock error
   */
  static outOfStock(itemId: number | string): ShopError {
    return new ShopError(
      `Shop item ${itemId} is out of stock`,
      400,
      { itemId }
    );
  }

  /**
   * Invalid quantity error
   */
  static invalidQuantity(quantity: number, min: number = 1, max?: number): ShopError {
    let message = `Invalid quantity ${quantity}. Must be at least ${min}`;
    if (max !== undefined) {
      message += ` and at most ${max}`;
    }
    return new ShopError(
      message,
      400,
      { quantity, min, max }
    );
  }

  /**
   * Promotion not found error
   */
  static promotionNotFound(promotionId: number | string): ShopError {
    return new ShopError(
      `Promotion with ID ${promotionId} not found`,
      404,
      { promotionId }
    );
  }

  /**
   * Promotion not applicable error
   */
  static promotionNotApplicable(promotionId: number | string, reason: string): ShopError {
    return new ShopError(
      `Promotion ${promotionId} is not applicable: ${reason}`,
      400,
      { promotionId, reason }
    );
  }

  /**
   * Promotion usage limit exceeded error
   */
  static promotionUsageLimitExceeded(promotionId: number | string, limit: number): ShopError {
    return new ShopError(
      `Promotion ${promotionId} usage limit exceeded. Maximum allowed: ${limit}`,
      400,
      { promotionId, limit }
    );
  }

  /**
   * Invalid gift recipient error
   */
  static invalidGiftRecipient(recipientId: string, reason?: string): ShopError {
    const message = reason
      ? `Invalid gift recipient ${recipientId}: ${reason}`
      : `Invalid gift recipient ${recipientId}`;
    return new ShopError(
      message,
      400,
      { recipientId, reason }
    );
  }

  /**
   * Cannot gift to self error
   */
  static cannotGiftToSelf(): ShopError {
    return new ShopError(
      'Cannot send gift to yourself',
      400
    );
  }

  /**
   * Wishlist item already exists error
   */
  static wishlistItemExists(itemId: number | string): ShopError {
    return new ShopError(
      `Item ${itemId} is already in your wishlist`,
      409,
      { itemId }
    );
  }

  /**
   * Wishlist item not found error
   */
  static wishlistItemNotFound(itemId: number | string): ShopError {
    return new ShopError(
      `Item ${itemId} not found in your wishlist`,
      404,
      { itemId }
    );
  }

  /**
   * Invalid price configuration error
   */
  static invalidPriceConfiguration(itemId: number | string): ShopError {
    return new ShopError(
      `Invalid price configuration for item ${itemId}. Item must have at least one valid price`,
      400,
      { itemId }
    );
  }

  /**
   * Transaction failed error
   */
  static transactionFailed(reason: string, details?: any): ShopError {
    return new ShopError(
      `Transaction failed: ${reason}`,
      500,
      details
    );
  }

  /**
   * Invalid shop data error
   */
  static invalidShopData(message: string, details?: any): ShopError {
    return new ShopError(
      `Invalid shop data: ${message}`,
      400,
      details
    );
  }

  /**
   * Currency operation failed error
   */
  static currencyOperationFailed(operation: string, reason: string): ShopError {
    return new ShopError(
      `Currency operation '${operation}' failed: ${reason}`,
      500,
      { operation, reason }
    );
  }

  /**
   * Inventory operation failed error
   */
  static inventoryOperationFailed(operation: string, reason: string): ShopError {
    return new ShopError(
      `Inventory operation '${operation}' failed: ${reason}`,
      500,
      { operation, reason }
    );
  }

  /**
   * Invalid request error
   */
  static invalidRequest(message: string, details?: any): ShopError {
    return new ShopError(
      message,
      400,
      details
    );
  }

  /**
   * Get status code (alias for httpCode for compatibility)
   */
  get statusCode(): number {
    return this.httpCode;
  }
}
