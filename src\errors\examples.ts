/**
 * 错误处理系统使用示例
 * 这个文件展示了如何在不同场景下使用自定义错误类
 */

import {
  // 基础错误
  BaseError,

  // 认证错误
  AuthenticationError,
  InvalidCredentialsError,
  TokenExpiredError,
  InvalidTokenError,
  MissingTokenError,

  // 验证错误
  ValidationError,
  MissingFieldError,
  InvalidFieldTypeError,
  InvalidFieldValueError,
  FieldLengthError,

  // 业务逻辑错误
  NotFoundError,
  ConflictError,
  PermissionDeniedError,

  // 游戏错误
  RoomNotFoundError,
  PlayerNotFoundError,
  ItemNotFoundError,
  CharacterNameExistsError,

  // 错误工厂
  ErrorFactory
} from './index';

import { Validators } from '../utils/validators';

/**
 * 示例：用户服务中的错误处理
 */
export class ExampleUserService {
  async login(username: string, password: string) {
    try {
      // 验证输入
      if (!username) {
        throw new MissingFieldError('username');
      }

      if (!password) {
        throw new MissingFieldError('password');
      }

      // 验证用户名格式
      Validators.username(username);

      // 模拟数据库查询
      const user = await this.findUserByUsername(username);
      if (!user) {
        throw new InvalidCredentialsError('Invalid username or password');
      }

      // 验证密码
      const isValidPassword = await this.verifyPassword(password, user.hashedPassword);
      if (!isValidPassword) {
        throw new InvalidCredentialsError('Invalid username or password');
      }

      // 生成token
      const token = await this.generateToken(user.id);

      return {
        success: true,
        user: { id: user.id, username: user.username },
        token
      };

    } catch (error) {
      // 错误会被全局错误处理器捕获
      throw error;
    }
  }

  async createUser(userData: any) {
    // 验证必需字段
    Validators.required(userData.username, 'username');
    Validators.required(userData.password, 'password');

    // 验证字段类型
    Validators.type(userData.username, 'string', 'username');
    Validators.type(userData.password, 'string', 'password');

    // 验证字段格式
    Validators.username(userData.username);
    Validators.password(userData.password);

    if (userData.email) {
      Validators.email(userData.email);
    }

    // 检查用户名是否已存在
    const existingUser = await this.findUserByUsername(userData.username);
    if (existingUser) {
      throw new ConflictError('User', 'username', userData.username);
    }

    // 创建用户...
    return { success: true, userId: 'new-user-id' };
  }

  async updateUser(userId: string, updateData: any, currentUserId: string) {
    // 权限检查
    if (userId !== currentUserId) {
      throw new PermissionDeniedError('update user profile');
    }

    // 查找用户
    const user = await this.findUserById(userId);
    if (!user) {
      throw new NotFoundError('User', userId);
    }

    // 验证更新数据
    if (updateData.email) {
      Validators.email(updateData.email);
    }

    // 更新用户...
    return { success: true };
  }

  // 模拟方法
  private async findUserByUsername(username: string): Promise<{ id: string; username: string; hashedPassword: string } | null> {
    // 模拟数据库查询
    return null;
  }

  private async findUserById(id: string): Promise<{ id: string; username: string } | null> {
    // 模拟数据库查询
    return null;
  }

  private async verifyPassword(password: string, hashedPassword: string) {
    // 模拟密码验证
    return true;
  }

  private async generateToken(userId: string) {
    // 模拟token生成
    return 'mock-jwt-token';
  }
}

/**
 * 示例：游戏服务中的错误处理
 */
export class ExampleGameService {
  async joinRoom(roomId: string, playerId: string) {
    // 验证输入
    if (!roomId) {
      throw new MissingFieldError('roomId');
    }

    if (!playerId) {
      throw new MissingFieldError('playerId');
    }

    // 查找房间
    const room = await this.findRoom(roomId);
    if (!room) {
      throw new RoomNotFoundError(roomId);
    }

    // 检查房间是否已满
    if (room.playerCount >= room.maxPlayers) {
      throw ErrorFactory.createNotFoundError('Room', roomId); // 使用工厂方法
    }

    // 查找玩家
    const player = await this.findPlayer(playerId);
    if (!player) {
      throw new PlayerNotFoundError(playerId);
    }

    // 加入房间逻辑...
    return { success: true };
  }

  async placeItem(playerId: string, itemId: string, x: number, y: number) {
    // 验证坐标
    Validators.coordinates(x, y);

    // 查找物品
    const item = await this.findItem(itemId);
    if (!item) {
      throw new ItemNotFoundError(itemId);
    }

    // 检查玩家是否拥有该物品
    const hasItem = await this.playerHasItem(playerId, itemId);
    if (!hasItem) {
      throw new PermissionDeniedError('place item', 'Item');
    }

    // 放置物品逻辑...
    return { success: true };
  }

  async createCharacter(userId: string, characterName: string, appearance: any) {
    // 验证角色名
    Validators.characterName(characterName);

    // 检查角色名是否已存在
    const existingCharacter = await this.findCharacterByName(characterName);
    if (existingCharacter) {
      throw new CharacterNameExistsError(characterName);
    }

    // 验证外观数据
    if (appearance) {
      Validators.object(appearance, 'appearance');
    }

    // 创建角色逻辑...
    return { success: true, characterId: 'new-character-id' };
  }

  // 模拟方法
  private async findRoom(roomId: string) {
    return { id: roomId, playerCount: 0, maxPlayers: 10 };
  }

  private async findPlayer(playerId: string) {
    return { id: playerId };
  }

  private async findItem(itemId: string) {
    return null;
  }

  private async playerHasItem(playerId: string, itemId: string) {
    return false;
  }

  private async findCharacterByName(name: string) {
    return null;
  }
}

/**
 * 示例：Express路由中的错误处理
 */
export function exampleRoutes() {
  const express = require('express');
  const { asyncHandler } = require('../middleware/errorHandler');
  const router = express.Router();

  // 用户登录路由
  router.post('/login', asyncHandler(async (req: any, res: any) => {
    const { username, password } = req.body;

    const userService = new ExampleUserService();
    const result = await userService.login(username, password);

    res.json(result);
  }));

  // 创建用户路由
  router.post('/users', asyncHandler(async (req: any, res: any) => {
    const userService = new ExampleUserService();
    const result = await userService.createUser(req.body);

    res.status(201).json(result);
  }));

  // 加入房间路由
  router.post('/rooms/:roomId/join', asyncHandler(async (req: any, res: any) => {
    const { roomId } = req.params;
    const { playerId } = req.body;

    const gameService = new ExampleGameService();
    const result = await gameService.joinRoom(roomId, playerId);

    res.json(result);
  }));

  return router;
}

/**
 * 示例：错误处理的最佳实践
 */
export class ErrorHandlingBestPractices {
  /**
   * 正确的错误处理方式
   */
  static async goodExample() {
    try {
      // 使用具体的错误类型
      throw new MissingFieldError('username');
    } catch (error) {
      if (error instanceof BaseError) {
        // 处理自定义错误
        console.log('Custom error:', error.toJSON());
      } else {
        // 处理其他错误
        console.log('Unknown error:', error);
      }
    }
  }

  /**
   * 错误的处理方式（避免）
   */
  static async badExample() {
    try {
      // 不要使用通用的Error类
      throw new Error('Something went wrong');
    } catch (error) {
      // 不要忽略错误类型
      console.log('Error:', error);
    }
  }

  /**
   * 使用验证工具的正确方式
   */
  static validateUserInput(userData: any) {
    try {
      // 链式验证
      Validators.required(userData.username, 'username');
      Validators.type(userData.username, 'string', 'username');
      Validators.stringLength(userData.username, 'username', 3, 20);
      Validators.username(userData.username);

      if (userData.email) {
        Validators.email(userData.email);
      }

      return true;
    } catch (error) {
      // 验证错误会自动包含详细信息
      throw error;
    }
  }
}
