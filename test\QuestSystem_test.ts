import { describe, it, before, after } from 'mocha';
import { expect } from 'chai';
import { ColyseusTestServer, boot } from '@colyseus/testing';
import { QuestRoom } from '../src/rooms/QuestRoom';
import QuestService from '../src/services/QuestService';

describe('Quest System Tests', () => {
  let colyseus: ColyseusTestServer;

  before(async () => {
    colyseus = await boot({
      initializeGameServer: (gameServer) => {
        gameServer.define('quest', QuestRoom);
      }
    });
  });

  after(async () => {
    await colyseus.shutdown();
  });

  describe('QuestService', () => {
    it('should load quest cache on initialization', async () => {
      const quests = QuestService.getAllQuests();
      expect(quests).to.be.an('array');
    });

    it('should get quest by ID', () => {
      // Assuming quest ID 1 exists from sample data
      const quest = QuestService.getQuest(1);
      if (quest) {
        expect(quest).to.have.property('id', 1);
        expect(quest).to.have.property('title');
      }
    });

    it('should get quest steps', () => {
      const steps = QuestService.getQuestSteps(1);
      expect(steps).to.be.an('array');
    });

    it('should get quest step by order', () => {
      const step = QuestService.getQuestStep(1, 0);
      if (step) {
        expect(step).to.have.property('step_order', 0);
        expect(step).to.have.property('goal');
      }
    });

    it('should get NPC response', () => {
      const response = QuestService.getNpcResponse(1, 0, 15503, 'unfinished');
      if (response) {
        expect(response).to.have.property('npc_id', 15503);
        expect(response).to.have.property('response_type', 'unfinished');
      }
    });
  });

  describe('QuestRoom', () => {
    beforeEach(async () => {
      // Reset quest data before each test to avoid conflicts
      const mysql = require('mysql2/promise');
      const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        port: Number(process.env.DB_PORT) || 3306,
      });

      try {
        // Clear quest progress for test users
        const testUserIds = ['3', '4', '5', '6', '7', '8', '9', '10'];
        for (const userId of testUserIds) {
          await connection.query('DELETE FROM player_quests WHERE player_id = ?', [userId]);
        }
      } catch (error) {
        console.log('⚠️  Error clearing quest data:', error.message);
      } finally {
        await connection.end();
      }
    });

    it('should allow clients to join with quest system', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '3' // Use numeric user ID that exists in database
      });

      const client = await colyseus.connectTo(room, {
        uid: '3'
      });

      // Wait a bit for the client to be fully connected
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(room.state.players.size).to.equal(1);
      expect(room.state.questSystem).to.exist;
    });

    it('should handle quest actions', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '4'
      });

      const client = await colyseus.connectTo(room, {
        uid: '4'
      });

      // Test quest start action
      client.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      // Wait for response with timeout
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout waiting for quest_started message'));
        }, 5000);

        client.onMessage('quest_started', (message) => {
          clearTimeout(timeout);
          try {
            expect(message.questId).to.equal(1);
            resolve(message);
          } catch (error) {
            reject(error);
          }
        });

        client.onMessage('quest_error', (error) => {
          clearTimeout(timeout);
          reject(new Error(`Quest error: ${error.message}`));
        });
      });
    });

    it('should handle player movement', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '5'
      });

      const client = await colyseus.connectTo(room, {
        uid: '5'
      });

      // Test player movement
      client.send('player_movement', {
        x: 100,
        y: 200,
        dir: 'right',
        animation: 'walk'
      });

      // Wait for the movement to be processed
      await new Promise(resolve => setTimeout(resolve, 100));

      // Check if player position was updated
      const player = room.state.getPlayerByUid('5');
      expect(player?.x).to.equal(100);
      expect(player?.y).to.equal(200);
      expect(player?.dir).to.equal('right');
      expect(player?.currentAnimation).to.equal('walk');
    });

    it('should handle NPC interactions', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '6'
      });

      const client = await colyseus.connectTo(room, {
        uid: '6'
      });

      // First start the quest
      client.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      // Wait for quest to start
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout waiting for quest to start'));
        }, 5000);

        client.onMessage('quest_started', () => {
          clearTimeout(timeout);
          resolve();
        });

        client.onMessage('quest_error', (error) => {
          clearTimeout(timeout);
          reject(new Error(`Quest error: ${error.message}`));
        });
      });

      // Test NPC interaction
      client.send('npc_interaction', {
        npcId: 15503,
        questId: 1,
        stepOrder: 0
      });

      // Wait for response
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout waiting for npc_dialogue message'));
        }, 5000);

        client.onMessage('npc_dialogue', (message) => {
          clearTimeout(timeout);
          try {
            expect(message.npcId).to.equal(15503);
            expect(message.questId).to.equal(1);
            expect(message.stepOrder).to.equal(0);
            // Check if response exists, if not just verify the message structure
            if (message.response) {
              expect(message.response).to.exist;
            } else {
              console.log('⚠️  NPC response is empty, but interaction was processed');
            }
            resolve(message);
          } catch (error) {
            reject(error);
          }
        });

        client.onMessage('npc_interaction_error', (error) => {
          clearTimeout(timeout);
          reject(new Error(`NPC interaction error: ${error.message}`));
        });
      });
    });

    it('should sync quest progress between room state and database', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '7'
      });

      const client = await colyseus.connectTo(room, {
        uid: '7'
      });

      // Start a quest
      client.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      await new Promise(resolve => {
        client.onMessage('quest_started', resolve);
      });

      // Update quest progress
      client.send('quest_action', {
        action: 'update_progress',
        questId: 1,
        stepOrder: 0,
        progressData: { talked_to_silvermist: true }
      });

      await new Promise(resolve => {
        client.onMessage('quest_progress_updated', (message) => {
          expect(message.questId).to.equal(1);
          expect(message.stepOrder).to.equal(0);
          resolve(message);
        });
      });

      // Check room state
      const questState = room.state.questSystem.getPlayerQuest('7', 1);
      expect(questState).to.exist;
      if (questState) {
        const stepProgress = questState.getStepProgress(0);
        expect(stepProgress).to.exist;
      }
    });

    it('should handle quest step completion', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '8'
      });

      const client = await colyseus.connectTo(room, {
        uid: '8'
      });

      // Start a quest
      client.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      await new Promise(resolve => {
        client.onMessage('quest_started', resolve);
      });

      // Complete a quest step
      client.send('quest_action', {
        action: 'complete_step',
        questId: 1,
        stepOrder: 0,
        progressData: { completed: true }
      });

      await new Promise(resolve => {
        client.onMessage('quest_step_completed', (message) => {
          expect(message.questId).to.equal(1);
          expect(message.stepOrder).to.equal(0);
          resolve(message);
        });
      });

      // Check if step is marked as completed
      const questState = room.state.questSystem.getPlayerQuest('8', 1);
      expect(questState).to.exist;
      if (questState) {
        expect(questState.isStepCompleted(0)).to.be.true;
        expect(questState.currentStepOrder).to.equal(1); // Should advance to next step
      }
    });

    it('should handle quest errors gracefully', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '9'
      });

      const client = await colyseus.connectTo(room, {
        uid: '9'
      });

      // Try to start a non-existent quest
      client.send('quest_action', {
        action: 'start_quest',
        questId: 999
      });

      await new Promise(resolve => {
        client.onMessage('quest_error', (error) => {
          expect(error.code).to.equal(404);
          expect(error.message).to.include('not found');
          resolve(error);
        });
      });
    });

    it('should prevent starting the same quest twice', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '10'
      });

      const client = await colyseus.connectTo(room, {
        uid: '10'
      });

      // Start a quest
      client.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      await new Promise(resolve => {
        client.onMessage('quest_started', resolve);
      });

      // Try to start the same quest again
      client.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      await new Promise(resolve => {
        client.onMessage('quest_error', (error) => {
          expect(error.code).to.equal(409);
          expect(error.message).to.include('already been started');
          resolve(error);
        });
      });
    });
  });
});
