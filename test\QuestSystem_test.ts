import { describe, it, before, after } from 'mocha';
import { expect } from 'chai';
import { ColyseusTestServer, boot } from '@colyseus/testing';
import { QuestRoom } from '../src/rooms/QuestRoom';
import QuestService from '../src/services/QuestService';

describe('Quest System Tests', () => {
  let colyseus: ColyseusTestServer;

  before(async () => {
    colyseus = await boot({
      initializeGameServer: (gameServer) => {
        gameServer.define('quest', QuestRoom);
      }
    });
  });

  after(async () => {
    await colyseus.shutdown();
  });

  describe('QuestService', () => {
    it('should load quest cache on initialization', async () => {
      const quests = QuestService.getAllQuests();
      expect(quests).to.be.an('array');
    });

    it('should get quest by ID', () => {
      // Assuming quest ID 1 exists from sample data
      const quest = QuestService.getQuest(1);
      if (quest) {
        expect(quest).to.have.property('id', 1);
        expect(quest).to.have.property('title');
      }
    });

    it('should get quest steps', () => {
      const steps = QuestService.getQuestSteps(1);
      expect(steps).to.be.an('array');
    });

    it('should get quest step by order', () => {
      const step = QuestService.getQuestStep(1, 0);
      if (step) {
        expect(step).to.have.property('step_order', 0);
        expect(step).to.have.property('goal');
      }
    });

    it('should get NPC response', () => {
      const response = QuestService.getNpcResponse(1, 0, 15503, 'unfinished');
      if (response) {
        expect(response).to.have.property('npc_id', 15503);
        expect(response).to.have.property('response_type', 'unfinished');
      }
    });
  });

  describe('QuestRoom', () => {
    it('should allow clients to join with quest system', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '3' // Use numeric user ID that exists in database
      });

      expect(room.state.players.size).to.equal(1);
      expect(room.state.questSystem).to.exist;
    });

    it('should handle quest actions', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '4'
      });

      const client = await colyseus.connectTo(room, {
        uid: '4'
      });

      // Test quest start action
      client.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      // Wait for response
      await new Promise(resolve => {
        client.onMessage('quest_started', (message) => {
          expect(message.questId).to.equal(1);
          resolve(message);
        });
      });
    });

    it('should handle player movement', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '5'
      });

      const client = await colyseus.connectTo(room, {
        uid: '5'
      });

      // Test player movement
      client.send('player_movement', {
        x: 100,
        y: 200,
        dir: 'right',
        animation: 'walk'
      });

      // Check if player position was updated
      const player = room.state.getPlayerByUid('5');
      expect(player?.x).to.equal(100);
      expect(player?.y).to.equal(200);
      expect(player?.dir).to.equal('right');
      expect(player?.currentAnimation).to.equal('walk');
    });

    it('should handle NPC interactions', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '6'
      });

      const client = await colyseus.connectTo(room, {
        uid: '6'
      });

      // Test NPC interaction
      client.send('npc_interaction', {
        npcId: 15503,
        questId: 1,
        stepOrder: 0
      });

      // Wait for response
      await new Promise(resolve => {
        client.onMessage('npc_dialogue', (message) => {
          expect(message.npcId).to.equal(15503);
          expect(message.questId).to.equal(1);
          expect(message.stepOrder).to.equal(0);
          expect(message.response).to.exist;
          resolve(message);
        });
      });
    });

    it('should sync quest progress between room state and database', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '7'
      });

      const client = await colyseus.connectTo(room, {
        uid: '7'
      });

      // Start a quest
      client.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      await new Promise(resolve => {
        client.onMessage('quest_started', resolve);
      });

      // Update quest progress
      client.send('quest_action', {
        action: 'update_progress',
        questId: 1,
        stepOrder: 0,
        progressData: { talked_to_silvermist: true }
      });

      await new Promise(resolve => {
        client.onMessage('quest_progress_updated', (message) => {
          expect(message.questId).to.equal(1);
          expect(message.stepOrder).to.equal(0);
          resolve(message);
        });
      });

      // Check room state
      const questState = room.state.questSystem.getPlayerQuest('7', 1);
      expect(questState).to.exist;
      if (questState) {
        const stepProgress = questState.getStepProgress(0);
        expect(stepProgress).to.exist;
      }
    });

    it('should handle quest step completion', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '8'
      });

      const client = await colyseus.connectTo(room, {
        uid: '8'
      });

      // Start a quest
      client.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      await new Promise(resolve => {
        client.onMessage('quest_started', resolve);
      });

      // Complete a quest step
      client.send('quest_action', {
        action: 'complete_step',
        questId: 1,
        stepOrder: 0,
        progressData: { completed: true }
      });

      await new Promise(resolve => {
        client.onMessage('quest_step_completed', (message) => {
          expect(message.questId).to.equal(1);
          expect(message.stepOrder).to.equal(0);
          resolve(message);
        });
      });

      // Check if step is marked as completed
      const questState = room.state.questSystem.getPlayerQuest('8', 1);
      expect(questState).to.exist;
      if (questState) {
        expect(questState.isStepCompleted(0)).to.be.true;
        expect(questState.currentStepOrder).to.equal(1); // Should advance to next step
      }
    });

    it('should handle quest errors gracefully', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '9'
      });

      const client = await colyseus.connectTo(room, {
        uid: '9'
      });

      // Try to start a non-existent quest
      client.send('quest_action', {
        action: 'start_quest',
        questId: 999
      });

      await new Promise(resolve => {
        client.onMessage('quest_error', (error) => {
          expect(error.code).to.equal(404);
          expect(error.message).to.include('not found');
          resolve(error);
        });
      });
    });

    it('should prevent starting the same quest twice', async () => {
      const room = await colyseus.createRoom('quest', {
        uid: '10'
      });

      const client = await colyseus.connectTo(room, {
        uid: '10'
      });

      // Start a quest
      client.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      await new Promise(resolve => {
        client.onMessage('quest_started', resolve);
      });

      // Try to start the same quest again
      client.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      await new Promise(resolve => {
        client.onMessage('quest_error', (error) => {
          expect(error.code).to.equal(409);
          expect(error.message).to.include('already been started');
          resolve(error);
        });
      });
    });
  });
});
