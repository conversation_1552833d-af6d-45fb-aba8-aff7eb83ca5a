import { BaseError } from './BaseError';

/**
 * Quest-related error class
 * Used for quest system specific errors
 */
export class QuestError extends BaseError {
  constructor(
    message: string,
    httpCode: number = 400,
    details?: any
  ) {
    super('QuestError', httpCode, message, true, details);
  }

  /**
   * Quest not found error
   */
  static questNotFound(questId: number | string): QuestError {
    return new QuestError(
      `Quest with ID ${questId} not found`,
      404,
      { questId }
    );
  }

  /**
   * Quest step not found error
   */
  static questStepNotFound(questId: number | string, stepOrder: number): QuestError {
    return new QuestError(
      `Quest step ${stepOrder} not found for quest ${questId}`,
      404,
      { questId, stepOrder }
    );
  }

  /**
   * Quest already started error
   */
  static questAlreadyStarted(questId: number | string): QuestError {
    return new QuestError(
      `Quest ${questId} has already been started`,
      409,
      { questId }
    );
  }

  /**
   * Quest already completed error
   */
  static questAlreadyCompleted(questId: number | string): QuestError {
    return new QuestError(
      `Quest ${questId} has already been completed`,
      409,
      { questId }
    );
  }

  /**
   * Quest not started error
   */
  static questNotStarted(questId: number | string): QuestError {
    return new QuestError(
      `Quest ${questId} has not been started`,
      400,
      { questId }
    );
  }

  /**
   * Invalid quest step progression error
   */
  static invalidStepProgression(questId: number | string, currentStep: number, targetStep: number): QuestError {
    return new QuestError(
      `Cannot progress from step ${currentStep} to step ${targetStep} in quest ${questId}`,
      400,
      { questId, currentStep, targetStep }
    );
  }

  /**
   * Quest step not completed error
   */
  static stepNotCompleted(questId: number | string, stepOrder: number): QuestError {
    return new QuestError(
      `Step ${stepOrder} in quest ${questId} is not completed`,
      400,
      { questId, stepOrder }
    );
  }

  /**
   * Invalid quest action error
   */
  static invalidQuestAction(action: string, questId: number | string): QuestError {
    return new QuestError(
      `Invalid quest action '${action}' for quest ${questId}`,
      400,
      { action, questId }
    );
  }

  /**
   * Quest data validation error
   */
  static invalidQuestData(message: string, details?: any): QuestError {
    return new QuestError(
      `Invalid quest data: ${message}`,
      400,
      details
    );
  }

  /**
   * NPC response not found error
   */
  static npcResponseNotFound(stepId: number, npcId: number, responseType: string): QuestError {
    return new QuestError(
      `NPC response not found for step ${stepId}, NPC ${npcId}, type ${responseType}`,
      404,
      { stepId, npcId, responseType }
    );
  }
}
