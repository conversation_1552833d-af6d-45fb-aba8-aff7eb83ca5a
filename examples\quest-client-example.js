/**
 * Quest System Client Example
 * 
 * This example demonstrates how to connect to the quest room
 * and interact with the quest system from a client perspective.
 * 
 * To run this example:
 * 1. Make sure the server is running (npm start)
 * 2. Run: node examples/quest-client-example.js
 */

const { Client } = require('colyseus.js');

class QuestClientExample {
  constructor() {
    this.client = new Client('ws://localhost:2567');
    this.room = null;
    this.playerId = 'example_player_' + Date.now();
  }

  async connect() {
    try {
      console.log('🔌 Connecting to quest room...');
      
      // Join or create a quest room
      this.room = await this.client.joinOrCreate('quest', {
        uid: this.playerId,
        x: 100,
        y: 100
      });

      console.log('✅ Connected to quest room!');
      console.log(`Player ID: ${this.playerId}`);
      console.log(`Room ID: ${this.room.roomId}`);

      this.setupEventHandlers();
      
      // Wait a moment for initial data to load
      await this.sleep(2000);
      
      // Start the quest demo
      await this.runQuestDemo();

    } catch (error) {
      console.error('❌ Failed to connect:', error.message);
    }
  }

  setupEventHandlers() {
    // Handle room state changes
    this.room.onStateChange((state) => {
      console.log('📊 Room state updated');
      console.log(`Players in room: ${state.players.size}`);
    });

    // Handle quest data loaded
    this.room.onMessage('quest_data_loaded', (data) => {
      console.log('📋 Quest data loaded:');
      console.log(`Available quests: ${data.availableQuests.length}`);
      console.log(`Player quests: ${data.quests.length}`);
      
      if (data.availableQuests.length > 0) {
        console.log('\n📜 Available Quests:');
        data.availableQuests.forEach(quest => {
          console.log(`  - ${quest.id}: ${quest.title}`);
        });
      }
    });

    // Handle quest started
    this.room.onMessage('quest_started', (data) => {
      console.log(`🎯 Quest ${data.questId} started successfully!`);
    });

    // Handle quest progress updated
    this.room.onMessage('quest_progress_updated', (data) => {
      console.log(`📈 Quest ${data.questId} progress updated (Step ${data.stepOrder})`);
    });

    // Handle quest step completed
    this.room.onMessage('quest_step_completed', (data) => {
      console.log(`✅ Quest ${data.questId} step ${data.stepOrder} completed!`);
    });

    // Handle NPC dialogue
    this.room.onMessage('npc_dialogue', (data) => {
      console.log(`💬 NPC ${data.npcId} says:`);
      if (data.response) {
        console.log(`   "${data.response.message_0}"`);
        if (data.response.message_1) {
          console.log(`   "${data.response.message_1}"`);
        }
      } else {
        console.log(`   "${data.message}"`);
      }
    });

    // Handle NPC response
    this.room.onMessage('npc_response', (response) => {
      if (response) {
        console.log(`📝 NPC Response received:`);
        console.log(`   Type: ${response.response_type}`);
        console.log(`   Message: "${response.message_0}"`);
      }
    });

    // Handle errors
    this.room.onMessage('quest_error', (error) => {
      console.error(`❌ Quest Error: ${error.message} (Code: ${error.code})`);
    });

    this.room.onMessage('npc_interaction_error', (error) => {
      console.error(`❌ NPC Interaction Error: ${error.message}`);
    });

    // Handle room events
    this.room.onMessage('room_joined', (data) => {
      console.log('🏠 Room joined successfully');
      console.log('Room stats:', data.roomStats);
    });
  }

  async runQuestDemo() {
    console.log('\n🎮 Starting Quest Demo...\n');

    try {
      // Step 1: Start a quest
      console.log('1️⃣ Starting quest 1 (Welcome to Pixie Hollow)...');
      this.room.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      await this.sleep(2000);

      // Step 2: Move player around
      console.log('\n2️⃣ Moving player around...');
      this.room.send('player_movement', {
        x: 150,
        y: 150,
        dir: 'right',
        animation: 'walk'
      });

      await this.sleep(1000);

      // Step 3: Interact with Silvermist (NPC 15503)
      console.log('\n3️⃣ Interacting with Silvermist...');
      this.room.send('npc_interaction', {
        npcId: 15503,
        questId: 1,
        stepOrder: 0
      });

      await this.sleep(2000);

      // Step 4: Complete the first quest step
      console.log('\n4️⃣ Completing first quest step...');
      this.room.send('quest_action', {
        action: 'complete_step',
        questId: 1,
        stepOrder: 0,
        progressData: { talked_to_silvermist: true }
      });

      await this.sleep(2000);

      // Step 5: Interact with Tinker Bell (NPC 15504)
      console.log('\n5️⃣ Interacting with Tinker Bell...');
      this.room.send('npc_interaction', {
        npcId: 15504,
        questId: 1,
        stepOrder: 1
      });

      await this.sleep(2000);

      // Step 6: Complete the second quest step
      console.log('\n6️⃣ Completing second quest step...');
      this.room.send('quest_action', {
        action: 'complete_step',
        questId: 1,
        stepOrder: 1,
        progressData: { visited_workshop: true }
      });

      await this.sleep(2000);

      // Step 7: Get NPC response for completed step
      console.log('\n7️⃣ Getting NPC response for completed step...');
      this.room.send('quest_action', {
        action: 'get_npc_response',
        questId: 1,
        stepOrder: 0,
        npcId: 15503,
        responseType: 'finished'
      });

      await this.sleep(2000);

      // Step 8: Try to start the same quest again (should fail)
      console.log('\n8️⃣ Trying to start the same quest again (should fail)...');
      this.room.send('quest_action', {
        action: 'start_quest',
        questId: 1
      });

      await this.sleep(2000);

      // Step 9: Start a different quest
      console.log('\n9️⃣ Starting quest 2 (Garden Helper)...');
      this.room.send('quest_action', {
        action: 'start_quest',
        questId: 2
      });

      await this.sleep(2000);

      console.log('\n🎉 Quest demo completed!');
      console.log('The quest system is working correctly.');

    } catch (error) {
      console.error('❌ Error during quest demo:', error);
    }

    // Keep the connection alive for a bit longer
    setTimeout(() => {
      console.log('\n👋 Disconnecting from server...');
      this.room.leave();
      process.exit(0);
    }, 5000);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the example
console.log('🚀 Quest System Client Example');
console.log('================================\n');

const example = new QuestClientExample();
example.connect().catch(error => {
  console.error('❌ Example failed:', error);
  process.exit(1);
});
