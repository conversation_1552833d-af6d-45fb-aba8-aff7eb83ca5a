const mysql = require('mysql2/promise');
require('dotenv').config();

async function resetQuestData() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🔄 Resetting quest data for tests...');
    
    // Delete all player quest progress for test users
    const testUserIds = ['3', '4', '5', '6', '7', '8', '9', '10'];
    
    for (const userId of testUserIds) {
      await connection.query('DELETE FROM player_quests WHERE player_id = ?', [userId]);
      console.log(`✅ Cleared quest data for user ${userId}`);
    }
    
    console.log('🎉 Quest data reset completed!');
    
  } catch (error) {
    console.error('❌ Error resetting quest data:', error.message);
  } finally {
    await connection.end();
  }
}

resetQuestData();
