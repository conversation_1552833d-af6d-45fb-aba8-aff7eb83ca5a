"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeRoom = void 0;
const core_1 = require("@colyseus/core");
const HomeRoomState_1 = require("../models/HomeRoomState");
const PlayerState_1 = require("../models/PlayerState");
const HomeItemState_1 = require("../models/HomeItemState");
const authService_1 = __importDefault(require("../services/authService")); // Singleton instance
const SpaceService_1 = __importDefault(require("../services/SpaceService")); // Singleton instance
const ItemService_1 = __importDefault(require("../services/ItemService")); // Singleton instance
// Note: uuidv4 is not directly used in this version of HomeRoom, PlayerState handles its own session ID.
const errorHandler_1 = require("../middleware/errorHandler");
const errors_1 = require("../errors");
/**
 * @class HomeRoom
 * @extends Room<HomeRoomState>
 * @description Manages the state and interactions for a user's personal home space.
 * This includes managing placed furniture, background, access levels, and players within the room.
 * The room's lifecycle (onCreate, onAuth, onJoin, onLeave, onDispose) and message handlers
 * are defined here.
 */
class HomeRoom extends core_1.Room {
    constructor() {
        super(...arguments);
        /**
         * @public
         * @description If true, the room will be disposed automatically when the last client leaves.
         *              Set to true for user-specific rooms like homes.
         */
        this.autoDispose = true;
    }
    /**
     * Called when the room is created.
     * Initializes the room state, loads owner's home data, and sets up message handlers.
     * @async
     * @param {object} options - Options passed from the client or matchmaker.
     * @param {string} options.ownerUid - The UID of the user whose home this room represents. Essential for loading correct data.
     * @throws {Error} If `ownerUid` is not provided in options, or if initial data loading fails.
     */
    async onCreate(options) {
        if (!options || !options.ownerUid || typeof options.ownerUid !== 'string') {
            console.error("HomeRoom.onCreate: Critical error - ownerUid is required in options and must be a string.");
            // Immediately attempt to stop further processing if ownerUid is invalid.
            // Throwing an error here is often the best way to signal failure to Colyseus.
            throw new Error("HomeRoom creation failed: Valid ownerUid is required.");
        }
        this.ownerUid = options.ownerUid;
        console.log(`HomeRoom.onCreate: Creating HomeRoom for owner UID: ${this.ownerUid}.`);
        // Initialize service instances (they are singletons)
        this.spaceServiceInstance = SpaceService_1.default;
        this.itemServiceInstance = ItemService_1.default;
        // Set the initial state of the room with the owner's UID.
        this.setState(new HomeRoomState_1.HomeRoomState(this.ownerUid));
        try {
            // Load the persisted home data for the owner.
            const spaceData = await this.spaceServiceInstance.getFullPrivateSpaceData(this.ownerUid);
            // getFullPrivateSpaceData now throws if settings are critically un-fetchable/un-creatable.
            this.state.homeBackgroundId = spaceData.settings.home_background_id;
            this.state.accessLevel = spaceData.settings.home_access_level;
            // Populate furniture from persisted data.
            spaceData.homeItems.forEach((itemRecord) => {
                const homeItem = new HomeItemState_1.HomeItemState(itemRecord.item_instance_id, itemRecord.item_template_id, itemRecord.pos_x, itemRecord.pos_y, itemRecord.rotation, itemRecord.is_flipped);
                this.state.furniture.set(itemRecord.item_instance_id, homeItem);
            });
            console.log(`HomeRoom.onCreate: HomeRoom for ${this.ownerUid} initialized successfully. Background: ${this.state.homeBackgroundId}, Access: ${this.state.accessLevel}, Items: ${this.state.furniture.size}.`);
        }
        catch (error) {
            // If initial data loading fails, the room is in an invalid state.
            console.error(`HomeRoom.onCreate: Critical error initializing HomeRoom for owner ${this.ownerUid}: ${error.message}. Disposing room.`);
            // Disconnect any connected clients and prevent further joins.
            // Throwing the error will also signal to Colyseus that room creation failed.
            throw error; // Re-throw the error to ensure Colyseus handles it.
        }
        // Register handlers for client messages.
        this.registerMessageHandlers();
    }
    /**
     * Registers handlers for messages sent by clients.
     * These handlers manage actions like placing/moving furniture, changing background, etc.
     * @private
     */
    registerMessageHandlers() {
        /**
         * Handles requests from the room owner to place a new item in their home.
         * @param client The client instance sending the message.
         * @param message The message payload.
         * @param {string} message.templateId - The template ID of the item to place.
         * @param {number} message.x - The X position.
         * @param {number} message.y - The Y position.
         * @param {number} [message.rotation] - Optional rotation.
         * @param {boolean} [message.isFlipped] - Optional flip state.
         */
        this.onMessage("place_item", async (client, message) => {
            const result = await errorHandler_1.ColyseusErrorHandler.safeAsync(client, async () => {
                // Permission check
                if (client.auth.uid !== this.state.ownerUid) {
                    throw new errors_1.PermissionDeniedError("place items", "home");
                }
                const { templateId, x, y, rotation, isFlipped } = message;
                // Validation
                if (!templateId)
                    throw new errors_1.MissingFieldError('templateId');
                if (typeof templateId !== 'string')
                    throw new errors_1.InvalidFieldTypeError('templateId', 'string', typeof templateId);
                if (typeof x !== 'number')
                    throw new errors_1.InvalidFieldTypeError('x', 'number', typeof x);
                if (typeof y !== 'number')
                    throw new errors_1.InvalidFieldTypeError('y', 'number', typeof y);
                if (rotation !== undefined && typeof rotation !== 'number')
                    throw new errors_1.InvalidFieldTypeError('rotation', 'number', typeof rotation);
                if (isFlipped !== undefined && typeof isFlipped !== 'boolean')
                    throw new errors_1.InvalidFieldTypeError('isFlipped', 'boolean', typeof isFlipped);
                // Call SpaceService to handle the logic of placing an item (includes inventory check).
                const serviceResult = await this.spaceServiceInstance.placeHomeItem(this.ownerUid, templateId, x, y, rotation, isFlipped);
                if (serviceResult.success) {
                    const newItemState = new HomeItemState_1.HomeItemState(serviceResult.itemInstance.item_instance_id, serviceResult.itemInstance.item_template_id, serviceResult.itemInstance.pos_x, serviceResult.itemInstance.pos_y, serviceResult.itemInstance.rotation, serviceResult.itemInstance.is_flipped);
                    this.state.furniture.set(newItemState.instanceId, newItemState);
                    return newItemState;
                }
                else {
                    throw new Error(serviceResult.message || "Failed to place item.");
                }
            }, "Failed to place item");
            if (result) {
                // Optionally, send a success confirmation to the client.
                // client.send("item_placed_successfully", { instanceId: result.instanceId });
            }
        });
        /**
         * Handles requests from the room owner to update an existing item's details.
         * @param client The client instance sending the message.
         * @param message The message payload.
         * @param {string} message.instanceId - The instance ID of the item to update.
         * @param {number} [message.x] - New X position.
         * @param {number} [message.y] - New Y position.
         * @param {number} [message.rotation] - New rotation.
         * @param {boolean} [message.isFlipped] - New flip state.
         */
        this.onMessage("update_item_details", async (client, message) => {
            if (client.auth.uid !== this.state.ownerUid) {
                return client.error(403, "Permission denied: Only the room owner can update items.");
            }
            const { instanceId, x, y, rotation, isFlipped } = message;
            if (typeof instanceId !== 'string' || (x === undefined && y === undefined && rotation === undefined && isFlipped === undefined)) {
                return client.error(400, "Invalid parameters: 'instanceId' and at least one update field (x, y, rotation, isFlipped) are required.");
            }
            try {
                const updates = {};
                if (x !== undefined)
                    updates.x = x;
                if (y !== undefined)
                    updates.y = y;
                if (rotation !== undefined)
                    updates.rotation = rotation;
                if (isFlipped !== undefined)
                    updates.isFlipped = isFlipped;
                // SpaceService handles database update.
                await this.spaceServiceInstance.updateHomeItemDetails(this.ownerUid, instanceId, updates);
                // Update room state directly upon successful DB update.
                const itemToUpdate = this.state.furniture.get(instanceId);
                if (itemToUpdate) {
                    if (updates.x !== undefined)
                        itemToUpdate.x = updates.x;
                    if (updates.y !== undefined)
                        itemToUpdate.y = updates.y;
                    if (updates.rotation !== undefined)
                        itemToUpdate.rotation = updates.rotation;
                    if (updates.isFlipped !== undefined)
                        itemToUpdate.isFlipped = updates.isFlipped;
                }
                else {
                    // This case should ideally not be reached if service call was successful and item existed.
                    console.warn(`HomeRoom.update_item_details: Item ${instanceId} not found in state after successful DB update.`);
                }
            }
            catch (e) {
                console.error(`HomeRoom.update_item_details: Error for user ${client.auth.uid}, item instance ${instanceId}: ${e.message}`);
                client.error(500, e.message || "Server error: Could not update item details.");
            }
        });
        /**
         * Handles requests from the room owner to remove an item from their home.
         * @param client The client instance sending the message.
         * @param message The message payload.
         * @param {string} message.instanceId - The instance ID of the item to remove.
         */
        this.onMessage("remove_item", async (client, message) => {
            if (client.auth.uid !== this.state.ownerUid) {
                return client.error(403, "Permission denied: Only the room owner can remove items.");
            }
            const { instanceId } = message;
            if (typeof instanceId !== 'string') {
                return client.error(400, "Invalid parameters: 'instanceId' is required.");
            }
            try {
                // SpaceService handles DB removal and simulated inventory return.
                const result = await this.spaceServiceInstance.removeHomeItem(this.ownerUid, instanceId);
                // `removeHomeItem` now directly returns success or throws an error.
                if (result.success) {
                    this.state.furniture.delete(instanceId);
                    // Notify client about the item being "returned" to inventory.
                    client.send("item_returned_to_inventory", {
                        itemId: result.returnedItemId,
                        message: `Item ${result.returnedItemId} (instance ${instanceId}) removed and returned to your inventory.`
                    });
                }
                else {
                    client.error(500, result.message || "Failed to remove item.");
                }
            }
            catch (e) {
                console.error(`HomeRoom.remove_item: Error for user ${client.auth.uid}, item instance ${instanceId}: ${e.message}`);
                client.error(500, e.message || "Server error: Could not remove item.");
            }
        });
        /**
         * Handles requests from the room owner to update the home's background.
         * @param client The client instance sending the message.
         * @param message The message payload.
         * @param {string} message.backgroundItemId - The item ID of the new background.
         */
        this.onMessage("update_background", async (client, message) => {
            if (client.auth.uid !== this.state.ownerUid) {
                return client.error(403, "Permission denied: Only the room owner can change the background.");
            }
            const { backgroundItemId } = message;
            if (typeof backgroundItemId !== 'string') {
                return client.error(400, "Invalid parameters: 'backgroundItemId' (string) is required.");
            }
            try {
                // ItemService validates if the item is a valid background type.
                // This logic is now encapsulated within SpaceService's updateSpaceBackground method.
                await this.spaceServiceInstance.updateSpaceBackground(this.ownerUid, 'home', backgroundItemId);
                this.state.homeBackgroundId = backgroundItemId; // Update room state on success.
            }
            catch (e) {
                console.error(`HomeRoom.update_background: Error for user ${client.auth.uid}, background ${backgroundItemId}: ${e.message}`);
                client.error(500, e.message || "Server error: Could not update background.");
            }
        });
        /**
         * Handles requests from the room owner to update the home's access level.
         * @param client The client instance sending the message.
         * @param message The message payload.
         * @param {'private' | 'friends_only' | 'public'} message.accessLevel - The new access level.
         */
        this.onMessage("update_access_level", async (client, message) => {
            if (client.auth.uid !== this.state.ownerUid) {
                return client.error(403, "Permission denied: Only the room owner can change access level.");
            }
            const { accessLevel } = message;
            if (!['private', 'friends_only', 'public'].includes(accessLevel)) {
                return client.error(400, "Invalid access level provided. Must be 'private', 'friends_only', or 'public'.");
            }
            try {
                await this.spaceServiceInstance.updateSpaceAccessLevel(this.ownerUid, 'home', accessLevel);
                this.state.accessLevel = accessLevel; // Update room state on success.
            }
            catch (e) {
                console.error(`HomeRoom.update_access_level: Error for user ${client.auth.uid}, level ${accessLevel}: ${e.message}`);
                client.error(500, e.message || "Server error: Could not update access level.");
            }
        });
        // Handler for player state updates (position, animation, etc.)
        // This is similar to PublicLobbyRoom's handler.
        this.onMessage('updatePlayerState', (client, data) => {
            const player = this.state.players.get(client.sessionId);
            if (player) {
                if (data.x !== undefined)
                    player.x = Number(data.x);
                if (data.y !== undefined)
                    player.y = Number(data.y);
                if (data.dir !== undefined)
                    player.dir = String(data.dir);
                if (data.isFlipped !== undefined)
                    player.isFlipped = Boolean(data.isFlipped);
                if (data.isSitting !== undefined)
                    player.isSitting = Boolean(data.isSitting);
                if (data.currentAnimation !== undefined)
                    player.currentAnimation = String(data.currentAnimation);
                // Note: Outfit updates are not handled here; they might be part of CharacterService or a separate system.
                // If outfit changes were to be reflected in PlayerState and broadcasted, similar logic would apply.
                // Broadcast updated state to other clients in the room.
                // Consider sending only changed fields (delta updates) for optimization.
                this.broadcast('player_state_updated', { sessionId: client.sessionId, ...data }, { except: client });
            }
        });
    }
    /**
     * Authenticates a client attempting to join the room.
     * Verifies the client's token and UID, then checks if they have access to this specific home.
     * @async
     * @param {Client} client - The Colyseus client instance.
     * @param {object} options - Options provided by the client during the join request.
     * @param {string} options.token - The JWT for authentication.
     * @param {string} options.uid - The UID of the client.
     * @param {any} request - The underlying HTTP request (can be used for IP checks, etc.).
     * @returns {Promise<any>} The authentication data (e.g., user object or UID) if successful.
     * @throws {Error} If authentication or access check fails, preventing the client from joining.
     */
    async onAuth(client, options, request) {
        if (!this.ownerUid) {
            console.error("HomeRoom.onAuth: Critical - ownerUid not set. Room was not properly initialized.");
            throw new Error("Room not initialized. Cannot authenticate.");
        }
        if (!options || !options.token || !options.uid || typeof options.token !== 'string' || typeof options.uid !== 'string') {
            console.error(`HomeRoom.onAuth: Auth failed for client ${client.sessionId} - Missing or invalid token/uid in options.`);
            throw new Error('Authentication failed: Token and UID are required and must be strings.');
        }
        // 1. Verify the provided token using AuthService.
        const userPayload = await authService_1.default.verifyToken(options.token);
        if (!userPayload || userPayload.userId.toString() !== options.uid) { // Ensure userId from payload matches provided uid.
            console.error(`HomeRoom.onAuth: Auth failed for client ${client.sessionId} - Invalid token or UID mismatch.`);
            throw new Error('Authentication failed: Invalid token or UID does not match.');
        }
        // 2. Check if the authenticated user (options.uid) has access to this home (this.ownerUid's home).
        // `checkAccess` will throw if there's an issue with its own logic or DB calls.
        const hasAccess = await this.spaceServiceInstance.checkAccess(this.ownerUid, options.uid, 'home');
        if (!hasAccess) {
            console.warn(`HomeRoom.onAuth: Access denied for client ${client.sessionId} (UID: ${options.uid}) to home of ${this.ownerUid}.`);
            throw new Error(`Access denied to home of user ${this.ownerUid}.`);
        }
        console.log(`HomeRoom.onAuth: Auth successful for client ${client.sessionId} (UID: ${options.uid}) to home of ${this.ownerUid}.`);
        // Return data to be stored in `client.auth`. Here, we store the authenticated UID.
        return { uid: options.uid };
    }
    /**
     * Called when a client successfully joins the room after authentication.
     * Creates a PlayerState for the client and adds them to the room state.
     * @param {Client} client - The Colyseus client instance that joined.
     * @param {any} options - Options passed from the client (these are the same as in onAuth).
     * @param {{ uid: string }} auth - The authentication data returned by `onAuth`.
     */
    onJoin(client, options, auth) {
        // `auth` object contains what was returned from `onAuth`.
        if (!auth || !auth.uid) {
            console.error(`HomeRoom.onJoin: Join rejected for client ${client.sessionId} - Missing auth data (UID). This should not happen if onAuth is correctly implemented.`);
            client.leave(1001, "Authentication data missing post-auth. Please reconnect."); // 1001: Generic error
            return;
        }
        console.log(`HomeRoom.onJoin: Client ${client.sessionId} (UID: ${auth.uid}) joined HomeRoom for owner ${this.ownerUid}.`);
        // Create a new PlayerState for the joined client.
        // Initial position could be a default spawn point for the home.
        const player = new PlayerState_1.PlayerState(client.sessionId, auth.uid, // Use the authenticated UID.
        Math.floor(Math.random() * 800), // Example: Random X spawn point
        Math.floor(Math.random() * 600) // Example: Random Y spawn point
        );
        // TODO: Further player initialization (e.g., fetching character appearance from CharacterService
        // and setting `player.currentOutfit` and `player.currentAnimation`) could be done here.
        // For now, PlayerState uses its defaults.
        this.state.players.set(client.sessionId, player);
        // Note: playerCount is not explicitly in HomeRoomState, but could be added if needed.
        // If added, it would be: this.state.playerCount = this.clients.length;
        // Optionally, send a "welcome" message or initial state snapshot to the joined client.
        // client.send("welcome_home", { owner: this.ownerUid });
    }
    /**
     * Called when a client leaves the room.
     * Removes the player from the room state.
     * @param {Client} client - The Colyseus client instance that left.
     * @param {boolean} consented - True if the client left intentionally (e.g., called `leave()`).
     */
    onLeave(client, consented) {
        const player = this.state.players.get(client.sessionId);
        const uid = player ? player.uid : 'Unknown'; // Get UID for logging before deleting player state.
        if (player) {
            this.state.players.delete(client.sessionId);
            console.log(`HomeRoom.onLeave: Client ${client.sessionId} (UID: ${uid}) left HomeRoom for owner ${this.ownerUid}. Consented: ${consented}.`);
        }
        else {
            // This might happen if onJoin failed or client was disconnected before fully joining.
            console.log(`HomeRoom.onLeave: Client ${client.sessionId} (UID: ${uid}) left, but was not found in state. Consented: ${consented}.`);
        }
        // If playerCount were part of state: this.state.playerCount = this.clients.length;
    }
    /**
     * Called when the room is about to be disposed.
     * Perform any necessary cleanup here.
     */
    onDispose() {
        console.log(`HomeRoom.onDispose: Room for owner ${this.ownerUid} disposed.`);
        // Example: If there were any intervals or timeouts specific to this room instance, clear them here.
        // e.g., if (this.someInterval) this.someInterval.clear();
    }
}
exports.HomeRoom = HomeRoom;
