"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvalidStateError = exports.OperationNotAllowedError = exports.InsufficientResourcesError = exports.RateLimitError = exports.PermissionDeniedError = exports.ConflictError = exports.NotFoundError = exports.BusinessLogicError = void 0;
const BaseError_1 = require("./BaseError");
/**
 * Business logic related errors
 */
class BusinessLogicError extends BaseError_1.BaseError {
    constructor(message, httpCode = 400, details) {
        super('BusinessLogicError', httpCode, message, true, details);
    }
}
exports.BusinessLogicError = BusinessLogicError;
/**
 * Resource not found error
 */
class NotFoundError extends BaseError_1.BaseError {
    constructor(resource, identifier) {
        const message = identifier
            ? `${resource} with identifier '${identifier}' not found`
            : `${resource} not found`;
        super('NotFoundError', 404, message, true, { resource, identifier });
    }
}
exports.NotFoundError = NotFoundError;
/**
 * Resource already exists error
 */
class ConflictError extends BaseError_1.BaseError {
    constructor(resource, field, value) {
        let message = `${resource} already exists`;
        if (field && value) {
            message += ` with ${field}: '${value}'`;
        }
        super('ConflictError', 409, message, true, { resource, field, value });
    }
}
exports.ConflictError = ConflictError;
/**
 * Permission denied error
 */
class PermissionDeniedError extends BaseError_1.BaseError {
    constructor(action, resource) {
        const message = resource
            ? `Permission denied: Cannot ${action} ${resource}`
            : `Permission denied: Cannot ${action}`;
        super('PermissionDeniedError', 403, message, true, { action, resource });
    }
}
exports.PermissionDeniedError = PermissionDeniedError;
/**
 * Rate limit exceeded error
 */
class RateLimitError extends BaseError_1.BaseError {
    constructor(limit, timeWindow) {
        const message = `Rate limit exceeded: ${limit} requests per ${timeWindow}`;
        super('RateLimitError', 429, message, true, { limit, timeWindow });
    }
}
exports.RateLimitError = RateLimitError;
/**
 * Insufficient resources error (e.g., not enough coins, items)
 */
class InsufficientResourcesError extends BusinessLogicError {
    constructor(resource, required, available) {
        const message = `Insufficient ${resource}: required ${required}, available ${available}`;
        super(message, 400, { resource, required, available });
        this.name = 'InsufficientResourcesError';
    }
}
exports.InsufficientResourcesError = InsufficientResourcesError;
/**
 * Operation not allowed error
 */
class OperationNotAllowedError extends BusinessLogicError {
    constructor(operation, reason) {
        const message = `Operation '${operation}' not allowed: ${reason}`;
        super(message, 400, { operation, reason });
        this.name = 'OperationNotAllowedError';
    }
}
exports.OperationNotAllowedError = OperationNotAllowedError;
/**
 * Invalid state error
 */
class InvalidStateError extends BusinessLogicError {
    constructor(currentState, requiredState, resource) {
        let message = `Invalid state: current '${currentState}', required '${requiredState}'`;
        if (resource) {
            message = `${resource} ${message}`;
        }
        super(message, 400, { currentState, requiredState, resource });
        this.name = 'InvalidStateError';
    }
}
exports.InvalidStateError = InvalidStateError;
