const mysql = require('mysql2/promise');
const fs = require('fs');
require('dotenv').config();

async function runSQLFix() {
  let connection;
  
  try {
    console.log('🔗 Connecting to database...');
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: Number(process.env.DB_PORT) || 3306,
    });

    console.log('📄 Reading SQL fix file...');
    const sqlContent = fs.readFileSync('scripts/fix-missing-tables.sql', 'utf8');
    
    // Split SQL statements by semicolon and execute them one by one
    const statements = sqlContent.split(';').filter(stmt => stmt.trim().length > 0);
    
    console.log(`🔧 Executing ${statements.length} SQL statements...`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          await connection.query(statement);
          console.log(`✅ Statement ${i + 1}/${statements.length} executed successfully`);
        } catch (error) {
          console.log(`⚠️  Statement ${i + 1} failed (might already exist):`, error.message);
        }
      }
    }
    
    console.log('🎉 SQL fix completed!');
    
    // Verify tables exist
    console.log('🔍 Verifying tables...');
    const [tables] = await connection.query('SHOW TABLES');
    const tableNames = tables.map(table => Object.values(table)[0]);
    
    const requiredTables = [
      'user_inventory_config',
      'inventory_operations_log',
      'room_registry',
      'notifications',
      'outfit_templates',
      'user_outfits'
    ];
    
    requiredTables.forEach(tableName => {
      if (tableNames.includes(tableName)) {
        console.log(`✅ ${tableName} exists`);
      } else {
        console.log(`❌ ${tableName} missing`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

runSQLFix();
