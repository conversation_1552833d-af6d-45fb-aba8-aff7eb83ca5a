const mysql = require('mysql2/promise');
require('dotenv').config();

async function debugTestState() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🔍 Debugging test state...');
    
    // Check home_items for test user
    console.log('\n🪑 Home items for test user (ID: 2):');
    const [homeItems] = await connection.query(
      'SELECT * FROM home_items WHERE owner_user_id = 2 ORDER BY placed_at DESC'
    );
    
    if (homeItems.length === 0) {
      console.log('  ❌ No home items found for test user');
    } else {
      console.log(`  ✅ Found ${homeItems.length} home items:`);
      homeItems.forEach((item, index) => {
        console.log(`    ${index + 1}. ${item.item_instance_id} - ${item.item_template_id} at (${item.pos_x}, ${item.pos_y})`);
      });
    }
    
    // Check user inventory
    console.log('\n🎒 User inventory for test user (ID: 2):');
    const [inventory] = await connection.query(
      'SELECT item_id, quantity FROM user_inventory WHERE user_id = 2 ORDER BY acquired_at DESC'
    );
    
    if (inventory.length === 0) {
      console.log('  ❌ No inventory items found for test user');
    } else {
      console.log(`  ✅ Found ${inventory.length} inventory items:`);
      inventory.forEach((item, index) => {
        console.log(`    ${index + 1}. ${item.item_id} x${item.quantity}`);
      });
    }
    
    // Check user private spaces
    console.log('\n🏠 User private spaces for test user (ID: 2):');
    const [spaces] = await connection.query(
      'SELECT * FROM user_private_spaces WHERE owner_user_id = 2'
    );
    
    if (spaces.length === 0) {
      console.log('  ❌ No private space settings found for test user');
    } else {
      console.log(`  ✅ Found private space settings:`);
      const space = spaces[0];
      console.log(`    Home background: ${space.home_background_id}`);
      console.log(`    Home access: ${space.home_access_level}`);
      console.log(`    Garden background: ${space.garden_background_id}`);
      console.log(`    Garden access: ${space.garden_access_level}`);
    }
    
    // Check garden plots
    console.log('\n🌱 Garden plots for test user (ID: 2):');
    const [plots] = await connection.query(
      'SELECT plot_instance_id, plot_index, is_unlocked, seed_item_id, growth_stage FROM garden_plots WHERE user_id = 2 ORDER BY plot_index'
    );
    
    if (plots.length === 0) {
      console.log('  ❌ No garden plots found for test user');
    } else {
      console.log(`  ✅ Found ${plots.length} garden plots:`);
      plots.forEach((plot, index) => {
        const status = plot.is_unlocked ? 'unlocked' : 'locked';
        const planted = plot.seed_item_id ? `planted with ${plot.seed_item_id} (stage ${plot.growth_stage})` : 'empty';
        console.log(`    Plot ${plot.plot_index}: ${status}, ${planted}`);
      });
    }
    
    // Check room registry
    console.log('\n🏘️  Room registry for test user (ID: 2):');
    const [rooms] = await connection.query(
      'SELECT room_id, room_type, access_level, is_active FROM room_registry WHERE owner_uid = 2'
    );
    
    if (rooms.length === 0) {
      console.log('  ❌ No rooms found for test user');
    } else {
      console.log(`  ✅ Found ${rooms.length} rooms:`);
      rooms.forEach((room, index) => {
        const status = room.is_active ? 'active' : 'inactive';
        console.log(`    ${room.room_id} (${room.room_type}): ${room.access_level}, ${status}`);
      });
    }
    
    // Check available furniture items
    console.log('\n🪑 Available furniture items:');
    const [furnitureItems] = await connection.query(
      "SELECT item_id, item_name, item_type, category FROM items WHERE item_type = 'furniture' LIMIT 10"
    );
    
    if (furnitureItems.length === 0) {
      console.log('  ❌ No furniture items found');
    } else {
      console.log(`  ✅ Found ${furnitureItems.length} furniture items:`);
      furnitureItems.forEach((item, index) => {
        console.log(`    ${index + 1}. ${item.item_id} - ${item.item_name} (${item.category})`);
      });
    }
    
    console.log('\n🎉 Debug complete!');
    
  } catch (error) {
    console.error('❌ Error debugging test state:', error);
  } finally {
    await connection.end();
  }
}

debugTestState();
