import { describe, it, before, after } from 'mocha';
import { expect } from 'chai';
import ShopService from '../src/services/ShopService';
import CurrencyService from '../src/services/CurrencyService';
import InventoryService from '../src/services/InventoryService';
import WishlistService from '../src/services/WishlistService';
import { ShopError } from '../src/errors';

describe('Shop System Tests', () => {
  const testUserId = '2'; // Use numeric user ID that exists in database
  const testUserId2 = '3'; // Use another numeric user ID that exists

  before(async () => {
    // Initialize test users with currency
    try {
      await CurrencyService.initializeUserCurrency(testUserId, 10000, 500);
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        console.log('⚠️  User currency already exists for user', testUserId);
      } else if (error.code === 'ER_NO_REFERENCED_ROW_2') {
        console.log('⚠️  User', testUserId, 'does not exist, skipping currency initialization');
      } else {
        throw error;
      }
    }

    try {
      await CurrencyService.initializeUserCurrency(testUserId2, 5000, 100);
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        console.log('⚠️  User currency already exists for user', testUserId2);
      } else if (error.code === 'ER_NO_REFERENCED_ROW_2') {
        console.log('⚠️  User', testUserId2, 'does not exist, skipping currency initialization');
      } else {
        throw error;
      }
    }
  });

  describe('CurrencyService', () => {
    it('should get user currency', async () => {
      const currency = await CurrencyService.getUserCurrency(testUserId);
      expect(currency).to.exist;
      expect(currency.user_id.toString()).to.equal(testUserId);
      expect(currency.gold_coins).to.be.a('number');
      expect(currency.diamonds).to.be.a('number');
    });

    it('should check sufficient currency', async () => {
      const hasSufficient = await CurrencyService.hasSufficientCurrency(testUserId, 100, 10);
      expect(hasSufficient).to.be.true;

      const hasInsufficient = await CurrencyService.hasSufficientCurrency(testUserId, 100000, 1000);
      expect(hasInsufficient).to.be.false;
    });

    it('should deduct currency', async () => {
      const initialCurrency = await CurrencyService.getUserCurrency(testUserId);
      const deductAmount = 100;

      const updatedCurrency = await CurrencyService.deductCurrency(testUserId, deductAmount, 0, 'Test deduction');

      expect(updatedCurrency.gold_coins).to.equal(initialCurrency.gold_coins - deductAmount);
    });

    it('should add currency', async () => {
      const initialCurrency = await CurrencyService.getUserCurrency(testUserId);
      const addAmount = 50;

      const updatedCurrency = await CurrencyService.addCurrency(testUserId, addAmount, 0, 'Test addition');

      expect(updatedCurrency.gold_coins).to.equal(initialCurrency.gold_coins + addAmount);
    });

    it('should fail to deduct insufficient currency', async () => {
      try {
        await CurrencyService.deductCurrency(testUserId, 1000000, 0, 'Test insufficient');
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(ShopError);
        expect(error.message).to.include('Insufficient');
      }
    });
  });

  describe('InventoryService', () => {
    const testItemId = 'test_item_' + Date.now();

    it('should add item to inventory', async () => {
      // First, we need to ensure the item exists in the items table
      // For testing purposes, we'll assume it exists or skip this test
      try {
        const result = await InventoryService.addItem(testUserId, testItemId, 5);
        expect(result).to.exist;
        expect(result.item_id).to.equal(testItemId);
        expect(result.quantity).to.equal(5);
      } catch (error) {
        // If item doesn't exist, skip this test
        if (error.message.includes('not found')) {
          console.log('Skipping inventory test - test item not found');
          return;
        }
        throw error;
      }
    });

    it('should get item quantity', async () => {
      try {
        const quantity = await InventoryService.getItemQuantity(testUserId, testItemId);
        expect(quantity).to.be.a('number');
        expect(quantity).to.be.at.least(0);
      } catch (error) {
        // Skip if item doesn't exist
        if (error.message.includes('not found')) {
          console.log('Skipping inventory quantity test - test item not found');
          return;
        }
        throw error;
      }
    });

    it('should check if user has item', async () => {
      try {
        const hasItem = await InventoryService.hasItem(testUserId, testItemId, 1);
        expect(hasItem).to.be.a('boolean');
      } catch (error) {
        // Skip if item doesn't exist
        console.log('Skipping has item test - test item not found');
      }
    });

    it('should get user inventory', async () => {
      const inventory = await InventoryService.getUserInventory(testUserId);
      expect(inventory).to.be.an('array');
    });

    it('should get inventory statistics', async () => {
      const stats = await InventoryService.getInventoryStats(testUserId);
      expect(stats).to.exist;
      expect(stats.totalItems).to.be.a('number');
      expect(stats.totalUniqueItems).to.be.a('number');
      expect(stats.itemsByType).to.be.an('object');
      expect(stats.itemsByCategory).to.be.an('object');
    });
  });

  describe('ShopService', () => {
    it('should get shop categories', async () => {
      const categories = await ShopService.getCategories();
      expect(categories).to.be.an('array');
    });

    it('should get featured items', async () => {
      const featuredItems = await ShopService.getFeaturedItems();
      expect(featuredItems).to.be.an('array');
    });

    it('should get limited time items', async () => {
      const limitedItems = await ShopService.getLimitedTimeItems();
      expect(limitedItems).to.be.an('array');
    });

    it('should search items', async () => {
      const searchResults = await ShopService.searchItems('dress');
      expect(searchResults).to.be.an('array');
    });

    it('should get items by category', async () => {
      // Get first category
      const categories = await ShopService.getCategories();
      if (categories.length > 0) {
        const items = await ShopService.getItemsByCategory(categories[0].id);
        expect(items).to.be.an('array');
      }
    });

    it('should check purchase eligibility', async () => {
      // Get a shop item to test with
      const featuredItems = await ShopService.getFeaturedItems();
      if (featuredItems.length > 0) {
        const eligibility = await ShopService.canPurchaseItem(testUserId, featuredItems[0].id, 1);
        expect(eligibility).to.exist;
        expect(eligibility.canPurchase).to.be.a('boolean');
      }
    });

    it('should purchase item successfully', async () => {
      // Get an affordable item
      const featuredItems = await ShopService.getFeaturedItems();
      const affordableItem = featuredItems.find(item =>
        (item.price_gold || 0) <= 1000 && (item.price_diamonds || 0) <= 50
      );

      if (affordableItem) {
        try {
          const result = await ShopService.purchaseItem({
            userId: testUserId,
            shopItemId: affordableItem.id,
            quantity: 1
          });

          expect(result).to.exist;
          expect(result.success).to.be.true;
          expect(result.transactionId).to.be.a('number');
          expect(result.itemsAdded).to.be.an('array');
          expect(result.currencySpent).to.exist;
          expect(result.remainingCurrency).to.exist;
        } catch (error) {
          // If purchase fails due to item not being available or other reasons, that's okay
          if (error instanceof ShopError) {
            console.log('Purchase failed as expected:', error.message);
          } else {
            throw error;
          }
        }
      } else {
        console.log('No affordable items found for purchase test');
      }
    });

    it('should get purchase history', async () => {
      const history = await ShopService.getPurchaseHistory(testUserId, 10);
      expect(history).to.be.an('array');
    });
  });

  describe('WishlistService', () => {
    let testShopItemId: number;

    before(async () => {
      // Get a shop item to use for wishlist tests
      const featuredItems = await ShopService.getFeaturedItems();
      if (featuredItems.length > 0) {
        testShopItemId = featuredItems[0].id;
      }
    });

    it('should add item to wishlist', async () => {
      if (testShopItemId) {
        try {
          const result = await WishlistService.addToWishlist(testUserId, testShopItemId);
          expect(result).to.exist;
          expect(result.shop_item_id).to.equal(testShopItemId);
        } catch (error) {
          // If item already in wishlist, that's okay
          if (error instanceof ShopError && error.message.includes('already in')) {
            console.log('Item already in wishlist, which is expected');
          } else {
            throw error;
          }
        }
      } else {
        console.log('No shop items available for wishlist test');
      }
    });

    it('should check if item is in wishlist', async () => {
      if (testShopItemId) {
        const isInWishlist = await WishlistService.isInWishlist(testUserId, testShopItemId);
        expect(isInWishlist).to.be.a('boolean');
      }
    });

    it('should get user wishlist', async () => {
      const wishlist = await WishlistService.getUserWishlist(testUserId);
      expect(wishlist).to.be.an('array');
    });

    it('should get available wishlist items', async () => {
      const availableItems = await WishlistService.getAvailableWishlistItems(testUserId);
      expect(availableItems).to.be.an('array');
    });

    it('should get wishlist statistics', async () => {
      const stats = await WishlistService.getWishlistStats(testUserId);
      expect(stats).to.exist;
      expect(stats.totalItems).to.be.a('number');
      expect(stats.availableItems).to.be.a('number');
      expect(stats.limitedTimeItems).to.be.a('number');
      expect(stats.itemsByCategory).to.be.an('object');
    });

    it('should get expiring wishlist items', async () => {
      const expiringItems = await WishlistService.getExpiringWishlistItems(testUserId, 24);
      expect(expiringItems).to.be.an('array');
    });

    it('should remove item from wishlist', async () => {
      if (testShopItemId) {
        try {
          await WishlistService.removeFromWishlist(testUserId, testShopItemId);
          // If successful, verify it's removed
          const isInWishlist = await WishlistService.isInWishlist(testUserId, testShopItemId);
          expect(isInWishlist).to.be.false;
        } catch (error) {
          // If item not in wishlist, that's okay
          if (error instanceof ShopError && error.message.includes('not found')) {
            console.log('Item not in wishlist, which is expected');
          } else {
            throw error;
          }
        }
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid shop item ID', async () => {
      try {
        await ShopService.getShopItem(999999);
        expect.fail('Should have thrown an error');
      } catch (error) {
        if (error.message === 'Should have thrown an error') {
          // This means the method didn't throw an error, which is unexpected
          throw error;
        }
        expect(error).to.be.instanceOf(ShopError);
      }
    });

    it('should handle invalid category ID', async () => {
      try {
        await ShopService.getItemsByCategory(999999);
        // This might return empty array instead of error
        const items = await ShopService.getItemsByCategory(999999);
        expect(items).to.be.an('array');
      } catch (error) {
        expect(error).to.be.instanceOf(ShopError);
      }
    });

    it('should handle insufficient currency for purchase', async () => {
      // Try to purchase an expensive item
      const featuredItems = await ShopService.getFeaturedItems();
      const expensiveItem = featuredItems.find(item =>
        (item.price_gold || 0) > 50000 || (item.price_diamonds || 0) > 1000
      );

      if (expensiveItem) {
        try {
          await ShopService.purchaseItem({
            userId: testUserId2, // User with less currency
            shopItemId: expensiveItem.id,
            quantity: 1
          });
          expect.fail('Should have thrown an error');
        } catch (error) {
          expect(error).to.be.instanceOf(ShopError);
          expect(error.message).to.include('Insufficient');
        }
      } else {
        console.log('No expensive items found for insufficient currency test');
      }
    });

    it('should handle adding duplicate item to wishlist', async () => {
      const featuredItems = await ShopService.getFeaturedItems();
      if (featuredItems.length > 0) {
        const itemId = featuredItems[0].id;

        // Add item to wishlist first
        try {
          await WishlistService.addToWishlist(testUserId2, itemId);
        } catch (error) {
          // Ignore if already exists
        }

        // Try to add the same item again
        try {
          await WishlistService.addToWishlist(testUserId2, itemId);
          expect.fail('Should have thrown an error');
        } catch (error) {
          expect(error).to.be.instanceOf(ShopError);
          expect(error.message).to.include('already in');
        }
      }
    });
  });
});
