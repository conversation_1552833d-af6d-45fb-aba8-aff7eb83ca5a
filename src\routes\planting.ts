import { Router, Request, Response } from 'express';
import PlantingService from '../services/PlantingService';
import PlantingOperationsService from '../services/PlantingOperationsService';
import { ShopError } from '../errors';

const router = Router();

/**
 * Get user's garden plots
 * GET /api/planting/garden
 */
router.get('/garden', async (req: Request, res: Response) => {
  try {
    const userId = req.headers['user-id'] as string;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    const garden = await PlantingService.getUserGarden(userId);
    
    res.json({
      success: true,
      data: {
        plots: garden
      }
    });
  } catch (error) {
    console.error('Failed to get user garden:', error);
    res.status(500).json({
      success: false,
      message: error instanceof ShopError ? error.message : 'Internal server error'
    });
  }
});

/**
 * Get user's planted flowers
 * GET /api/planting/flowers
 */
router.get('/flowers', async (req: Request, res: Response) => {
  try {
    const userId = req.headers['user-id'] as string;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    // Update flower states before returning
    await PlantingOperationsService.updateFlowerStates(userId);
    
    const flowers = await PlantingService.getUserPlantedFlowers(userId);
    
    res.json({
      success: true,
      data: {
        flowers
      }
    });
  } catch (error) {
    console.error('Failed to get planted flowers:', error);
    res.status(500).json({
      success: false,
      message: error instanceof ShopError ? error.message : 'Internal server error'
    });
  }
});

/**
 * Get available seeds
 * GET /api/planting/seeds
 */
router.get('/seeds', async (req: Request, res: Response) => {
  try {
    const userId = req.headers['user-id'] as string;
    
    let userLevel: number | undefined;
    if (userId) {
      const levelData = await PlantingService.getUserPlantingLevel(userId);
      userLevel = levelData.current_level;
    }
    
    const seeds = await PlantingService.getAvailableSeeds(userLevel);
    
    res.json({
      success: true,
      data: {
        seeds,
        user_level: userLevel
      }
    });
  } catch (error) {
    console.error('Failed to get available seeds:', error);
    res.status(500).json({
      success: false,
      message: error instanceof ShopError ? error.message : 'Internal server error'
    });
  }
});

/**
 * Get user's planting level and stats
 * GET /api/planting/level
 */
router.get('/level', async (req: Request, res: Response) => {
  try {
    const userId = req.headers['user-id'] as string;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    const levelData = await PlantingService.getUserPlantingLevel(userId);
    const expForNextLevel = PlantingService.calculateExpForLevel(levelData.current_level);
    const expForCurrentLevel = levelData.current_level > 1 ? 
      PlantingService.calculateExpForLevel(levelData.current_level - 1) : 0;
    
    res.json({
      success: true,
      data: {
        ...levelData,
        exp_for_next_level: expForNextLevel,
        exp_for_current_level: expForCurrentLevel,
        exp_progress: levelData.current_exp - expForCurrentLevel,
        exp_needed: expForNextLevel - levelData.current_exp
      }
    });
  } catch (error) {
    console.error('Failed to get planting level:', error);
    res.status(500).json({
      success: false,
      message: error instanceof ShopError ? error.message : 'Internal server error'
    });
  }
});

/**
 * Unlock a garden plot
 * POST /api/planting/unlock-plot
 */
router.post('/unlock-plot', async (req: Request, res: Response) => {
  try {
    const userId = req.headers['user-id'] as string;
    const { plot_index } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    if (typeof plot_index !== 'number') {
      return res.status(400).json({
        success: false,
        message: 'Plot index is required and must be a number'
      });
    }
    
    const result = await PlantingService.unlockPlot(userId, plot_index);
    
    res.json(result);
  } catch (error) {
    console.error('Failed to unlock plot:', error);
    res.status(500).json({
      success: false,
      message: error instanceof ShopError ? error.message : 'Internal server error'
    });
  }
});

/**
 * Plant a seed
 * POST /api/planting/plant
 */
router.post('/plant', async (req: Request, res: Response) => {
  try {
    const userId = req.headers['user-id'] as string;
    const { seed_id, plot_index } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    if (!seed_id || typeof plot_index !== 'number') {
      return res.status(400).json({
        success: false,
        message: 'Seed ID and plot index are required'
      });
    }
    
    const result = await PlantingOperationsService.plantSeed(userId, seed_id, plot_index);
    
    res.json(result);
  } catch (error) {
    console.error('Failed to plant seed:', error);
    res.status(500).json({
      success: false,
      message: error instanceof ShopError ? error.message : 'Internal server error'
    });
  }
});

/**
 * Water a flower
 * POST /api/planting/water
 */
router.post('/water', async (req: Request, res: Response) => {
  try {
    const userId = req.headers['user-id'] as string;
    const { planted_flower_id } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    if (typeof planted_flower_id !== 'number') {
      return res.status(400).json({
        success: false,
        message: 'Planted flower ID is required and must be a number'
      });
    }
    
    const result = await PlantingOperationsService.waterFlower(userId, planted_flower_id);
    
    res.json(result);
  } catch (error) {
    console.error('Failed to water flower:', error);
    res.status(500).json({
      success: false,
      message: error instanceof ShopError ? error.message : 'Internal server error'
    });
  }
});

/**
 * Apply fertilizer to a flower
 * POST /api/planting/fertilize
 */
router.post('/fertilize', async (req: Request, res: Response) => {
  try {
    const userId = req.headers['user-id'] as string;
    const { planted_flower_id, fertilizer_id } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    if (typeof planted_flower_id !== 'number' || !fertilizer_id) {
      return res.status(400).json({
        success: false,
        message: 'Planted flower ID and fertilizer ID are required'
      });
    }
    
    const result = await PlantingOperationsService.fertilizeFlower(userId, planted_flower_id, fertilizer_id);
    
    res.json(result);
  } catch (error) {
    console.error('Failed to fertilize flower:', error);
    res.status(500).json({
      success: false,
      message: error instanceof ShopError ? error.message : 'Internal server error'
    });
  }
});

/**
 * Harvest a flower
 * POST /api/planting/harvest
 */
router.post('/harvest', async (req: Request, res: Response) => {
  try {
    const userId = req.headers['user-id'] as string;
    const { planted_flower_id } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    if (typeof planted_flower_id !== 'number') {
      return res.status(400).json({
        success: false,
        message: 'Planted flower ID is required and must be a number'
      });
    }
    
    const result = await PlantingOperationsService.harvestFlower(userId, planted_flower_id);
    
    res.json(result);
  } catch (error) {
    console.error('Failed to harvest flower:', error);
    res.status(500).json({
      success: false,
      message: error instanceof ShopError ? error.message : 'Internal server error'
    });
  }
});

/**
 * Clear a plot
 * POST /api/planting/clear-plot
 */
router.post('/clear-plot', async (req: Request, res: Response) => {
  try {
    const userId = req.headers['user-id'] as string;
    const { plot_index } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    if (typeof plot_index !== 'number') {
      return res.status(400).json({
        success: false,
        message: 'Plot index is required and must be a number'
      });
    }
    
    const result = await PlantingOperationsService.clearPlot(userId, plot_index);
    
    res.json(result);
  } catch (error) {
    console.error('Failed to clear plot:', error);
    res.status(500).json({
      success: false,
      message: error instanceof ShopError ? error.message : 'Internal server error'
    });
  }
});

export default router;
