const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function setupShopSystem() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: Number(process.env.DB_PORT) || 3306,
    });

    console.log('Connected to database successfully');

    // Read and execute the main schema file (if not already done)
    const schemaPath = path.join(__dirname, '../sql/virtualwrld_strict_schema_final.sql');
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    
    // Split by semicolon and execute each statement
    const statements = schemaSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    console.log('Ensuring database tables exist...');
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement);
        } catch (error) {
          // Ignore "table already exists" errors
          if (!error.message.includes('already exists')) {
            console.warn('Warning executing statement:', error.message);
          }
        }
      }
    }

    // Read and execute the shop sample data file
    const shopDataPath = path.join(__dirname, '../sql/shop_sample_data.sql');
    const shopDataSQL = fs.readFileSync(shopDataPath, 'utf8');
    
    // Split by semicolon and execute each statement
    const dataStatements = shopDataSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    console.log('Inserting sample shop data...');
    for (const statement of dataStatements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement);
        } catch (error) {
          // Ignore duplicate entry errors
          if (!error.message.includes('Duplicate entry') && !error.message.includes('already exists')) {
            console.warn('Warning executing shop data statement:', error.message);
          }
        }
      }
    }

    // Verify the setup
    console.log('Verifying shop system setup...');
    
    const [categories] = await connection.execute('SELECT COUNT(*) as count FROM shop_categories');
    console.log(`✓ Shop categories table: ${categories[0].count} categories loaded`);
    
    const [shopItems] = await connection.execute('SELECT COUNT(*) as count FROM shop_items');
    console.log(`✓ Shop items table: ${shopItems[0].count} items loaded`);
    
    const [currencies] = await connection.execute('SELECT COUNT(*) as count FROM user_currencies');
    console.log(`✓ User currencies table: ${currencies[0].count} user currencies`);
    
    const [inventory] = await connection.execute('SELECT COUNT(*) as count FROM user_inventory');
    console.log(`✓ User inventory table: ${inventory[0].count} inventory items`);
    
    const [purchaseHistory] = await connection.execute('SELECT COUNT(*) as count FROM purchase_history');
    console.log(`✓ Purchase history table: ${purchaseHistory[0].count} transactions`);
    
    const [wishlist] = await connection.execute('SELECT COUNT(*) as count FROM wishlist');
    console.log(`✓ Wishlist table: ${wishlist[0].count} wishlist items`);

    // Show sample data summary
    console.log('\n📊 Sample Data Summary:');
    
    const [categoryBreakdown] = await connection.execute(`
      SELECT sc.display_name, COUNT(si.id) as item_count
      FROM shop_categories sc
      LEFT JOIN shop_items si ON sc.id = si.category_id
      WHERE sc.parent_category_id IS NULL
      GROUP BY sc.id, sc.display_name
      ORDER BY sc.sort_order
    `);
    
    categoryBreakdown.forEach(row => {
      console.log(`  - ${row.display_name}: ${row.item_count} items`);
    });

    const [featuredItems] = await connection.execute('SELECT COUNT(*) as count FROM shop_items WHERE is_featured = TRUE');
    console.log(`  - Featured items: ${featuredItems[0].count}`);
    
    const [limitedItems] = await connection.execute('SELECT COUNT(*) as count FROM shop_items WHERE is_limited_time = TRUE');
    console.log(`  - Limited time items: ${limitedItems[0].count}`);

    console.log('\n🎉 Shop system setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Start the server: npm start');
    console.log('2. Test the shop API endpoints');
    console.log('3. Connect to a room with shop functionality');
    console.log('\nShop API endpoints available at:');
    console.log('- GET /api/shop/categories - List all shop categories');
    console.log('- GET /api/shop/featured - Get featured items');
    console.log('- GET /api/shop/limited-time - Get limited time items');
    console.log('- GET /api/shop/users/{userId}/currency - Get user currency');
    console.log('- GET /api/shop/users/{userId}/inventory - Get user inventory');
    console.log('- POST /api/shop/purchase - Purchase an item');
    console.log('- GET /api/shop/users/{userId}/wishlist - Get user wishlist');
    console.log('- POST /api/shop/users/{userId}/wishlist - Add item to wishlist');
    
    console.log('\nSample test commands:');
    console.log('curl http://localhost:2567/api/shop/categories');
    console.log('curl http://localhost:2567/api/shop/featured');
    console.log('curl http://localhost:2567/api/shop/users/1/currency');
    console.log('curl http://localhost:2567/api/shop/users/1/inventory');

  } catch (error) {
    console.error('Error setting up shop system:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Check if required environment variables are set
const requiredEnvVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingEnvVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  console.error('\nPlease create a .env file with the required database configuration.');
  console.error('Example .env file:');
  console.error('DB_HOST=localhost');
  console.error('DB_USER=your_username');
  console.error('DB_PASSWORD=your_password');
  console.error('DB_NAME=your_database');
  console.error('DB_PORT=3306');
  process.exit(1);
}

// Run the setup
setupShopSystem();
