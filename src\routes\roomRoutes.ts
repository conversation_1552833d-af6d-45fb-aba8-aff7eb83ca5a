import { Router, Request, Response } from 'express';
import RoomMatchingService, { RoomType, AccessLevel } from '../services/RoomMatchingService';
import { RoomUtils } from '../rooms/BasePrivateRoom';
import authService from '../services/authService';

const router = Router();

/**
 * Get available rooms for a user
 * GET /api/rooms/:roomType/available
 */
router.get('/:roomType/available', async (req: Request, res: Response) => {
  try {
    const { roomType } = req.params;
    const { token, uid } = req.query;

    // Validate room type
    if (!['home', 'garden', 'quest'].includes(roomType)) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Invalid room type. Must be home, garden, or quest.'
        }
      });
    }

    // Validate authentication
    if (!token || !uid) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'To<PERSON> and UID are required.'
        }
      });
    }

    // Verify token
    const userPayload = await authService.verifyToken(token as string);
    if (!userPayload || userPayload.userId.toString() !== uid) {
      return res.status(401).json({
        success: false,
        error: {
          name: 'AuthenticationError',
          message: 'Invalid token or UID mismatch.'
        }
      });
    }

    // Get accessible rooms
    const accessibleRooms = await RoomUtils.getAccessibleRooms(uid as string, roomType as RoomType);

    res.json({
      success: true,
      data: accessibleRooms
    });

  } catch (error) {
    console.error('Get available rooms error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to get available rooms'
      }
    });
  }
});

/**
 * Generate room ID for a specific user and room type
 * GET /api/rooms/:roomType/generate-id/:ownerUid
 */
router.get('/:roomType/generate-id/:ownerUid', async (req: Request, res: Response) => {
  try {
    const { roomType, ownerUid } = req.params;
    const { token, uid } = req.query;

    // Validate room type
    if (!['home', 'garden', 'quest'].includes(roomType)) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Invalid room type. Must be home, garden, or quest.'
        }
      });
    }

    // Validate authentication
    if (!token || !uid) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Token and UID are required.'
        }
      });
    }

    // Verify token
    const userPayload = await authService.verifyToken(token as string);
    if (!userPayload || userPayload.userId.toString() !== uid) {
      return res.status(401).json({
        success: false,
        error: {
          name: 'AuthenticationError',
          message: 'Invalid token or UID mismatch.'
        }
      });
    }

    // Check if user can access the target room
    const canAccess = await RoomUtils.canAccessRoom(ownerUid, uid as string, roomType as RoomType);
    if (!canAccess) {
      return res.status(403).json({
        success: false,
        error: {
          name: 'AccessDeniedError',
          message: `Access denied to ${roomType} of user ${ownerUid}.`
        }
      });
    }

    // Generate room ID
    const roomId = RoomUtils.generateRoomId(roomType as RoomType, ownerUid);

    res.json({
      success: true,
      data: {
        roomId,
        roomType,
        ownerUid,
        canAccess: true
      }
    });

  } catch (error) {
    console.error('Generate room ID error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to generate room ID'
      }
    });
  }
});

/**
 * Parse room ID to extract information
 * GET /api/rooms/parse/:roomId
 */
router.get('/parse/:roomId', async (req: Request, res: Response) => {
  try {
    const { roomId } = req.params;
    const { token, uid } = req.query;

    // Validate authentication
    if (!token || !uid) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Token and UID are required.'
        }
      });
    }

    // Verify token
    const userPayload = await authService.verifyToken(token as string);
    if (!userPayload || userPayload.userId.toString() !== uid) {
      return res.status(401).json({
        success: false,
        error: {
          name: 'AuthenticationError',
          message: 'Invalid token or UID mismatch.'
        }
      });
    }

    // Parse room ID
    const parsed = RoomUtils.parseRoomId(roomId);
    if (!parsed) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Invalid room ID format.'
        }
      });
    }

    // Check if user can access the room
    const canAccess = await RoomUtils.canAccessRoom(parsed.ownerUid, uid as string, parsed.roomType);

    // Get room info if it exists
    const roomInfo = await RoomMatchingService.getRoomInfo(roomId);

    res.json({
      success: true,
      data: {
        ...parsed,
        canAccess,
        roomInfo,
        isActive: roomInfo?.is_active || false
      }
    });

  } catch (error) {
    console.error('Parse room ID error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to parse room ID'
      }
    });
  }
});

/**
 * Get room information
 * GET /api/rooms/info/:roomId
 */
router.get('/info/:roomId', async (req: Request, res: Response) => {
  try {
    const { roomId } = req.params;
    const { token, uid } = req.query;

    // Validate authentication
    if (!token || !uid) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Token and UID are required.'
        }
      });
    }

    // Verify token
    const userPayload = await authService.verifyToken(token as string);
    if (!userPayload || userPayload.userId.toString() !== uid) {
      return res.status(401).json({
        success: false,
        error: {
          name: 'AuthenticationError',
          message: 'Invalid token or UID mismatch.'
        }
      });
    }

    // Get room info
    const roomInfo = await RoomMatchingService.getRoomInfo(roomId);
    if (!roomInfo) {
      return res.status(404).json({
        success: false,
        error: {
          name: 'NotFoundError',
          message: 'Room not found.'
        }
      });
    }

    // Check if user can access the room
    const canAccess = await RoomUtils.canAccessRoom(roomInfo.owner_uid, uid as string, roomInfo.room_type);

    res.json({
      success: true,
      data: {
        ...roomInfo,
        canAccess
      }
    });

  } catch (error) {
    console.error('Get room info error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to get room info'
      }
    });
  }
});

/**
 * Get public rooms of a specific type
 * GET /api/rooms/:roomType/public
 */
router.get('/:roomType/public', async (req: Request, res: Response) => {
  try {
    const { roomType } = req.params;
    const { limit = 20 } = req.query;

    // Validate room type
    if (!['home', 'garden', 'quest'].includes(roomType)) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Invalid room type. Must be home, garden, or quest.'
        }
      });
    }

    const publicRooms = await RoomMatchingService.getPublicRooms(
      roomType as RoomType,
      parseInt(limit as string)
    );

    res.json({
      success: true,
      data: publicRooms
    });

  } catch (error) {
    console.error('Get public rooms error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to get public rooms'
      }
    });
  }
});

/**
 * Get room statistics (admin endpoint)
 * GET /api/rooms/admin/statistics
 */
router.get('/admin/statistics', async (req: Request, res: Response) => {
  try {
    const { token, uid } = req.query;

    // Validate authentication
    if (!token || !uid) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Token and UID are required.'
        }
      });
    }

    // Verify token
    const userPayload = await authService.verifyToken(token as string);
    if (!userPayload || userPayload.userId.toString() !== uid) {
      return res.status(401).json({
        success: false,
        error: {
          name: 'AuthenticationError',
          message: 'Invalid token or UID mismatch.'
        }
      });
    }

    // TODO: Add admin role check here
    // For now, any authenticated user can access statistics

    const statistics = await RoomMatchingService.getRoomStatistics();

    res.json({
      success: true,
      data: statistics
    });

  } catch (error) {
    console.error('Get room statistics error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to get room statistics'
      }
    });
  }
});

/**
 * Cleanup inactive rooms (admin endpoint)
 * POST /api/rooms/admin/cleanup
 */
router.post('/admin/cleanup', async (req: Request, res: Response) => {
  try {
    const { token, uid } = req.query;
    const { olderThanHours = 24 } = req.body;

    // Validate authentication
    if (!token || !uid) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Token and UID are required.'
        }
      });
    }

    // Verify token
    const userPayload = await authService.verifyToken(token as string);
    if (!userPayload || userPayload.userId.toString() !== uid) {
      return res.status(401).json({
        success: false,
        error: {
          name: 'AuthenticationError',
          message: 'Invalid token or UID mismatch.'
        }
      });
    }

    // TODO: Add admin role check here

    const cleanedCount = await RoomMatchingService.cleanupInactiveRooms(olderThanHours);

    res.json({
      success: true,
      data: {
        cleanedRooms: cleanedCount,
        olderThanHours
      }
    });

  } catch (error) {
    console.error('Cleanup rooms error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to cleanup rooms'
      }
    });
  }
});

export default router;
