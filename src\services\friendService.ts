import pool from '../utils/db';
import { ResultSetHeader, RowDataPacket } from 'mysql2';
import UserService from './UserService'; // Assuming UserService is in the same directory or adjust path
import NotificationService from './NotificationService'; // Import notification service

/**
 * @interface FriendRelationship
 * @description Represents the structure of a record in the `friend_relationships` table.
 * @property {number} id - The unique identifier for the relationship.
 * @property {string} user_one_id - The ID of the first user in the relationship (typically the smaller ID).
 * @property {string} user_two_id - The ID of the second user in the relationship (typically the larger ID).
 * @property {'pending' | 'accepted' | 'declined' | 'blocked' | 'deleted'} status - The current status of the relationship.
 * @property {string} action_user_id - The ID of the user who performed the last action on this relationship.
 * @property {Date} [created_at] - Timestamp of when the relationship was created.
 * @property {Date} [updated_at] - Timestamp of when the relationship was last updated.
 */
interface FriendRelationship extends RowDataPacket {
  id: number;
  user_one_id: string;
  user_two_id: string;
  status: 'pending' | 'accepted' | 'declined' | 'blocked' | 'deleted';
  action_user_id: string;
  created_at?: Date;
  updated_at?: Date;
}

/**
 * @interface CategorizedFriends
 * @description Defines the structure for returning categorized lists of friends and relationship statuses.
 * @property {Array<{ userId: string, username?: string }>} acceptedFriends - List of users who are friends.
 * @property {Array<{ userId: string, username?: string }>} sentRequests - List of users to whom the current user has sent friend requests.
 * @property {Array<{ userId: string, username?: string }>} receivedRequests - List of users from whom the current user has received friend requests.
 * @property {Array<{ userId: string, username?: string }>} blockedByYou - List of users blocked by the current user.
 * @property {Array<{ userId: string, username?: string }>} blockedYou - List of users who have blocked the current user.
 */
export interface CategorizedFriends {
  acceptedFriends: { userId: string, username?: string }[];
  sentRequests: { userId: string, username?: string }[];
  receivedRequests: { userId: string, username?: string }[];
  blockedByYou: { userId: string, username?: string }[];
  blockedYou: { userId: string, username?: string }[];
}

/**
 * @class FriendService
 * @description Manages friend relationships, including sending requests, responding to requests,
 * listing friends, removing friends, and blocking/unblocking users.
 */
class FriendService {

  /**
   * Normalizes two user IDs to ensure that `user_one_id` is always the smaller one.
   * This consistency helps in uniquely identifying a relationship pair in the database,
   * regardless of which user initiated an action.
   * @private
   * @param {string} userId1 - The ID of the first user.
   * @param {string} userId2 - The ID of the second user.
   * @returns {{ user_one_id: string, user_two_id: string }} An object containing the normalized user IDs.
   */
  private normalizeUserIds(userId1: string, userId2: string): { user_one_id: string, user_two_id: string } {
    // Ensures user_one_id is always the smaller ID to maintain a canonical representation for the pair.
    if (userId1 < userId2) {
      return { user_one_id: userId1, user_two_id: userId2 };
    } else {
      return { user_one_id: userId2, user_two_id: userId1 };
    }
  }

  /**
   * Fetches basic user information (ID and username) for a given user ID.
   * This method is a placeholder and should ideally be replaced by a call to a dedicated UserService
   * or a direct query to the `users` table if UserService is not available.
   * @private
   * @param {string} userId - The ID of the user whose information is to be fetched.
   * @returns {Promise<{ id: string, username: string } | null>} User info if found, otherwise null.
   * @throws {Error} If there's an unexpected database error during the query.
   */
  private async fetchUserInfo(userId: string): Promise<{ id: string, username: string } | null> {
    // In a production environment, this should call a UserService.getUserBasicInfo(userId)
    // For now, directly querying the users table.
    try {
        // Ensure that the user ID is valid before querying.
        if (!userId || typeof userId !== 'string') { // Basic validation
            console.warn(`FriendService: fetchUserInfo called with invalid userId: ${userId}`);
            return null;
        }
        const [users] = await pool.query<RowDataPacket[]>("SELECT id, username FROM users WHERE id = ?", [userId]);
        if (users.length > 0) {
            // Assuming 'id' from DB is compatible with string type expected.
            return { id: users[0].id.toString(), username: users[0].username || `User_${users[0].id}` };
        }
        return null; // User not found.
    } catch (error: any) {
        console.error(`FriendService: Error fetching user info for userId ${userId}: ${error.message}`);
        // Re-throw a more specific error or handle as appropriate for the application.
        throw new Error(`Database error while fetching user information for user ${userId}.`);
    }
  }

  /**
   * Sends a friend request from the current user to a target user.
   * @param {string} currentUserId - The ID of the user sending the request.
   * @param {string} targetUserId - The ID of the user to whom the request is being sent.
   * @returns {Promise<{ success: boolean; message: string }>} An object indicating success or failure and a message.
   * @throws {Error} If there's an unexpected database error.
   */
  async sendFriendRequest(currentUserId: string, targetUserId: string): Promise<{ success: boolean; message: string }> {
    if (currentUserId === targetUserId) {
      return { success: false, message: 'Cannot send a friend request to yourself.' };
    }

    const targetUserExists = await this.fetchUserInfo(targetUserId);
    if (!targetUserExists) {
      // More specific error to be handled by API layer for 404.
      throw new Error('Target user does not exist.');
    }

    const { user_one_id, user_two_id } = this.normalizeUserIds(currentUserId, targetUserId);

    try {
      const [existingRelationships] = await pool.query<FriendRelationship[]>(
        'SELECT * FROM friend_relationships WHERE user_one_id = ? AND user_two_id = ?',
        [user_one_id, user_two_id]
      );

      if (existingRelationships.length > 0) {
        const rel = existingRelationships[0];
        if (rel.status === 'accepted') {
          return { success: false, message: 'You are already friends with this user.' };
        }
        if (rel.status === 'pending') {
          if (rel.action_user_id === currentUserId) {
            return { success: false, message: 'Friend request already sent and is pending.' };
          } else {
            // A request is pending from the target user.
            // Current design: ask user to respond. Future: could auto-accept here.
            return { success: false, message: 'This user has already sent you a friend request. Please respond to it.' };
          }
        }
        if (rel.status === 'blocked') {
            // Check who initiated the block.
            if (rel.action_user_id === currentUserId) {
                 return { success: false, message: 'You have blocked this user. Unblock them to send a friend request.' };
            } else { // Blocked by the target user.
                 return { success: false, message: 'This user has blocked you. Cannot send a friend request.' };
            }
        }
        // If status is 'declined' or 'deleted', allow sending a new request by updating the existing record.
        // This effectively "re-sends" the request.
        const [updateResult] = await pool.query<ResultSetHeader>(
          'UPDATE friend_relationships SET status = ?, action_user_id = ?, updated_at = NOW() WHERE id = ?',
          ['pending', currentUserId, rel.id]
        );
        if (updateResult.affectedRows === 0) throw new Error('Failed to update existing relationship to pending.');

        // Send real-time notification
        await NotificationService.sendFriendRequestNotification(currentUserId, targetUserId);

        return { success: true, message: 'Friend request sent successfully (previous relationship updated).' };
      }

      // No existing relationship, insert a new one.
      const [insertResult] = await pool.query<ResultSetHeader>(
        'INSERT INTO friend_relationships (user_one_id, user_two_id, status, action_user_id) VALUES (?, ?, ?, ?)',
        [user_one_id, user_two_id, 'pending', currentUserId]
      );
       if (insertResult.affectedRows === 0) throw new Error('Failed to insert new friend request.');

      // Send real-time notification
      await NotificationService.sendFriendRequestNotification(currentUserId, targetUserId);

      return { success: true, message: 'Friend request sent successfully.' };

    } catch (error: any) {
      console.error(`FriendService: Error sending friend request from ${currentUserId} to ${targetUserId}: ${error.message}`);
      // Re-throw for the route handler to catch and convert to appropriate HTTP response.
      throw new Error(`Failed to send friend request due to a server error: ${error.message}`);
    }
  }

  /**
   * Responds to a pending friend request.
   * @param {string} currentUserId - The ID of the user responding to the request (the recipient).
   * @param {string} senderUserId - The ID of the user who sent the request.
   * @param {'accept' | 'decline'} response - The response to the friend request.
   * @returns {Promise<{ success: boolean; message: string }>} An object indicating success or failure and a message.
   * @throws {Error} If there's an unexpected database error or the request is not found.
   */
  async respondToFriendRequest(currentUserId: string, senderUserId: string, response: 'accept' | 'decline'): Promise<{ success: boolean; message: string }> {
    if (currentUserId === senderUserId) {
        // This should ideally be caught by validation before calling the service.
        return { success: false, message: 'Cannot respond to a friend request from yourself.' };
    }
    const { user_one_id, user_two_id } = this.normalizeUserIds(currentUserId, senderUserId);

    try {
      // Ensure the request is pending and was sent by `senderUserId`.
      // `currentUserId` is the one taking action now.
      const [relationships] = await pool.query<FriendRelationship[]>(
        'SELECT id FROM friend_relationships WHERE user_one_id = ? AND user_two_id = ? AND status = ? AND action_user_id = ?',
        [user_one_id, user_two_id, 'pending', senderUserId]
      );

      if (relationships.length === 0) {
        throw new Error('No pending friend request found from this user, or you were not the intended recipient.');
      }

      const relationshipId = relationships[0].id;
      const newStatus = response === 'accept' ? 'accepted' : 'declined';

      const [updateResult] = await pool.query<ResultSetHeader>(
        'UPDATE friend_relationships SET status = ?, action_user_id = ?, updated_at = NOW() WHERE id = ?',
        [newStatus, currentUserId, relationshipId] // `currentUserId` is now the action_user_id
      );

      if (updateResult.affectedRows === 0) {
        // This might happen if the record was changed/deleted between the SELECT and UPDATE.
        throw new Error('Failed to update friend request status. The request may have been modified or deleted.');
      }

      // Send real-time notification based on response
      if (response === 'accept') {
        await NotificationService.sendFriendRequestAcceptedNotification(currentUserId, senderUserId);
      } else {
        await NotificationService.sendFriendRequestDeclinedNotification(currentUserId, senderUserId);
      }

      return { success: true, message: `Friend request ${newStatus}.` };

    } catch (error: any) {
      console.error(`FriendService: Error responding to friend request from ${senderUserId} by ${currentUserId}: ${error.message}`);
      throw new Error(`Failed to respond to friend request: ${error.message}`);
    }
  }

  /**
   * Lists all friend relationships for the current user, categorized by status.
   * Can be filtered by a specific status.
   * @param {string} currentUserId - The ID of the user whose friends are being listed.
   * @param {'accepted' | 'pending' | 'blocked'} [filterStatus] - Optional filter for the relationship status.
   * @returns {Promise<{ success: boolean; data?: CategorizedFriends; message?: string }>} Result object with categorized friends or an error message.
   * @throws {Error} If there's an unexpected database error.
   */
  async listFriends(currentUserId: string, filterStatus?: 'accepted' | 'pending' | 'blocked'): Promise<{ success: true; data: CategorizedFriends } | { success: false; message: string }> {
    try {
      // Fetch all relationships involving the current user to categorize them.
      const [relationships] = await pool.query<FriendRelationship[]>(
        'SELECT * FROM friend_relationships WHERE (user_one_id = ? OR user_two_id = ?) AND status != ?', // Exclude 'deleted' and 'declined' from direct listing unless specified
        [currentUserId, currentUserId, 'deleted'] // Also excluding 'declined' as they are usually not actively shown.
      );

      const categorized: CategorizedFriends = {
        acceptedFriends: [],
        sentRequests: [],
        receivedRequests: [],
        blockedByYou: [],
        blockedYou: []
      };

      // Iterate through relationships and categorize them based on status and action_user_id.
      // Also, fetch usernames for better display.
      const otherUserIds = new Set<string>();
      relationships.forEach(rel => {
          otherUserIds.add(rel.user_one_id === currentUserId ? rel.user_two_id : rel.user_one_id);
      });

      const userInfos = await UserService.getMultipleUserBasicInfo(Array.from(otherUserIds));


      for (const rel of relationships) {
        const otherUserId = rel.user_one_id === currentUserId ? rel.user_two_id : rel.user_one_id;
        const userInfo = userInfos.get(otherUserId.toString()) || { id: otherUserId, username: `User_${otherUserId}` };
        const friendEntry = { userId: otherUserId, username: userInfo.username };

        switch (rel.status) {
            case 'accepted':
                if (!filterStatus || filterStatus === 'accepted') categorized.acceptedFriends.push(friendEntry);
                break;
            case 'pending':
                if (!filterStatus || filterStatus === 'pending') {
                    if (rel.action_user_id === currentUserId) { // Current user sent the request
                        categorized.sentRequests.push(friendEntry);
                    } else { // Current user received the request
                        categorized.receivedRequests.push(friendEntry);
                    }
                }
                break;
            case 'blocked':
                if (!filterStatus || filterStatus === 'blocked') {
                    if (rel.action_user_id === currentUserId) { // Current user initiated the block
                        categorized.blockedByYou.push(friendEntry);
                    } else { // Current user was blocked by the other user
                        categorized.blockedYou.push(friendEntry);
                    }
                }
                break;
            // 'declined' and 'deleted' statuses are typically not displayed in these lists.
        }
      }
      return { success: true, data: categorized };

    } catch (error: any) {
      console.error(`FriendService: Error listing friends for user ${currentUserId}: ${error.message}`);
      throw new Error(`Failed to list friends due to a server error: ${error.message}`);
    }
  }

  /**
   * Removes a friend (changes relationship status to 'deleted').
   * @param {string} currentUserId - The ID of the user initiating the removal.
   * @param {string} targetUserId - The ID of the friend to be removed.
   * @returns {Promise<{ success: boolean; message: string }>} An object indicating success or failure and a message.
   * @throws {Error} If there's an unexpected database error or users are not friends.
   */
  async removeFriend(currentUserId: string, targetUserId: string): Promise<{ success: boolean; message: string }> {
    if (currentUserId === targetUserId) {
      return { success: false, message: 'Cannot remove yourself as a friend.' };
    }
    const { user_one_id, user_two_id } = this.normalizeUserIds(currentUserId, targetUserId);

    try {
      // Ensure they are currently friends before attempting to remove.
      const [updateResult] = await pool.query<ResultSetHeader>(
        "UPDATE friend_relationships SET status = 'deleted', action_user_id = ?, updated_at = NOW() WHERE user_one_id = ? AND user_two_id = ? AND status = 'accepted'",
        [currentUserId, user_one_id, user_two_id]
      );

      if (updateResult.affectedRows === 0) {
        // This means they were not 'accepted' friends, or the record doesn't exist.
        throw new Error('You are not friends with this user, or the user could not be found.');
      }

      // Send real-time notification
      await NotificationService.sendFriendRemovedNotification(currentUserId, targetUserId);

      return { success: true, message: 'Friend removed successfully.' };

    } catch (error: any) {
      console.error(`FriendService: Error removing friend ${targetUserId} for user ${currentUserId}: ${error.message}`);
      throw new Error(`Failed to remove friend: ${error.message}`);
    }
  }

  /**
   * Blocks a target user. If a relationship exists, it's updated to 'blocked'.
   * If no relationship exists, a new 'blocked' relationship is created.
   * @param {string} currentUserId - The ID of the user initiating the block.
   * @param {string} targetUserId - The ID of the user to be blocked.
   * @returns {Promise<{ success: boolean; message: string }>} An object indicating success or failure and a message.
   * @throws {Error} If there's an unexpected database error or the target user doesn't exist.
   */
  async blockUser(currentUserId: string, targetUserId: string): Promise<{ success: boolean; message: string }> {
    if (currentUserId === targetUserId) {
      return { success: false, message: 'Cannot block yourself.' };
    }

    const targetUserExists = await this.fetchUserInfo(targetUserId);
    if (!targetUserExists) {
      throw new Error('Target user to block does not exist.');
    }

    const { user_one_id, user_two_id } = this.normalizeUserIds(currentUserId, targetUserId);

    try {
      const [existingRelationships] = await pool.query<FriendRelationship[]>(
        'SELECT id, status, action_user_id FROM friend_relationships WHERE user_one_id = ? AND user_two_id = ?',
        [user_one_id, user_two_id]
      );

      if (existingRelationships.length > 0) {
        const rel = existingRelationships[0];
        // If already blocked by the current user, no action needed.
        if (rel.status === 'blocked' && rel.action_user_id === currentUserId) {
            return { success: false, message: 'User is already blocked by you.'};
        }
        // Update existing relationship to 'blocked', with currentUserId as the one performing the action.
        const [updateResult] = await pool.query<ResultSetHeader>(
          "UPDATE friend_relationships SET status = 'blocked', action_user_id = ?, updated_at = NOW() WHERE id = ?",
          [currentUserId, rel.id]
        );
         if (updateResult.affectedRows === 0) throw new Error('Failed to update existing relationship to blocked.');
      } else {
        // No existing relationship, insert a new 'blocked' relationship.
        const [insertResult] = await pool.query<ResultSetHeader>(
          "INSERT INTO friend_relationships (user_one_id, user_two_id, status, action_user_id) VALUES (?, ?, 'blocked', ?)",
          [user_one_id, user_two_id, currentUserId]
        );
        if (insertResult.affectedRows === 0) throw new Error('Failed to insert new blocked relationship.');
      }

      // Send real-time notification
      await NotificationService.sendUserBlockedNotification(currentUserId, targetUserId);

      return { success: true, message: 'User blocked successfully.' };

    } catch (error: any) {
      console.error(`FriendService: Error blocking user ${targetUserId} by ${currentUserId}: ${error.message}`);
      throw new Error(`Failed to block user: ${error.message}`);
    }
  }

  /**
   * Unblocks a target user. The relationship status is set to 'deleted',
   * effectively removing the block from the current user's perspective.
   * @param {string} currentUserId - The ID of the user initiating the unblock.
   * @param {string} targetUserId - The ID of the user to be unblocked.
   * @returns {Promise<{ success: boolean; message: string }>} An object indicating success or failure and a message.
   * @throws {Error} If there's an unexpected database error or the user was not blocked by the current user.
   */
  async unblockUser(currentUserId: string, targetUserId: string): Promise<{ success: boolean; message: string }> {
     if (currentUserId === targetUserId) {
      return { success: false, message: 'Invalid operation: Cannot unblock yourself.' };
    }
    const { user_one_id, user_two_id } = this.normalizeUserIds(currentUserId, targetUserId);

    try {
      // Only unblock if currentUserId was the one who initiated the block.
      // The status is changed to 'deleted' to signify the end of this specific block interaction.
      // If the other user also blocked, their 'blocked' status with their action_user_id would remain.
      const [result] = await pool.query<ResultSetHeader>(
        "UPDATE friend_relationships SET status = 'deleted', action_user_id = ?, updated_at = NOW() WHERE user_one_id = ? AND user_two_id = ? AND status = 'blocked' AND action_user_id = ?",
        [currentUserId, user_one_id, user_two_id, currentUserId]
      );

      if (result.affectedRows === 0) {
        throw new Error('User was not blocked by you, or no such relationship found to unblock.');
      }
      return { success: true, message: 'User unblocked successfully.' };

    } catch (error: any) {
      console.error(`FriendService: Error unblocking user ${targetUserId} by ${currentUserId}: ${error.message}`);
      throw new Error(`Failed to unblock user: ${error.message}`);
    }
  }

  /**
   * Checks if two users are currently friends (relationship status is 'accepted').
   * @param {string} userId1 - The ID of the first user.
   * @param {string} userId2 - The ID of the second user.
   * @returns {Promise<boolean>} True if users are friends, false otherwise.
   * @throws {Error} If there's an unexpected database error.
   */
  async areFriends(userId1: string, userId2: string): Promise<boolean> {
    if (userId1 === userId2) return false; // Users cannot be friends with themselves.

    const { user_one_id, user_two_id } = this.normalizeUserIds(userId1, userId2);

    try {
      const [relationships] = await pool.query<FriendRelationship[]>(
        "SELECT status FROM friend_relationships WHERE user_one_id = ? AND user_two_id = ? AND status = 'accepted'",
        [user_one_id, user_two_id]
      );
      return relationships.length > 0;
    } catch (error: any) {
      console.error(`FriendService: Error checking friendship status between ${userId1} and ${userId2}: ${error.message}`);
      // Depending on application requirements, this might return false or throw.
      // Throwing allows higher-level handlers to decide on the response.
      throw new Error(`Database error while checking friendship status: ${error.message}`);
    }
  }
}

// Export a singleton instance of the FriendService.
// This ensures that the same instance is used throughout the application,
// which can be beneficial for managing state or connections if the service becomes more complex.
export default new FriendService();
