# Quest System Documentation

## Overview

The Quest System is a comprehensive task management system for the 2D Virtual World game server. It supports multi-step quests with NPC interactions, progress tracking, and real-time synchronization between clients and the server.

## Features

- **Multi-step Quests**: Each quest can have multiple sequential steps
- **NPC Interactions**: NPCs provide different responses based on quest progress
- **Real-time Sync**: Quest progress is synchronized in real-time using Colyseus
- **Persistent Storage**: Quest progress is saved to MySQL database
- **Error Handling**: Comprehensive error handling with custom error types
- **RESTful API**: HTTP endpoints for quest management
- **Caching**: Quest templates are cached for performance

## Database Schema

### Tables

1. **quests**: Quest templates and definitions
2. **quest_steps**: Individual steps within quests
3. **quest_step_responses**: NPC responses for different quest states
4. **player_quests**: Player progress tracking

### Setup

1. Run the main schema:
```sql
-- Execute the quest tables from virtualwrld_strict_schema_final.sql
```

2. Insert sample data:
```sql
-- Execute quest_sample_data.sql for example quests
```

## API Endpoints

### Quest Information

- `GET /api/quests/quests` - Get all available quests
- `GET /api/quests/quests/:questId` - Get specific quest details
- `GET /api/quests/quests/:questId/steps` - Get quest steps
- `GET /api/quests/quests/:questId/steps/:stepOrder/npc/:npcId/response/:responseType` - Get NPC response

### Player Progress

- `GET /api/quests/players/:playerId/quests` - Get player's quest progress
- `GET /api/quests/players/:playerId/quests/:questId` - Get specific quest progress
- `POST /api/quests/players/:playerId/quests/:questId/start` - Start a quest
- `PUT /api/quests/players/:playerId/quests/:questId/progress` - Update quest progress

### Admin

- `POST /api/quests/admin/quests/refresh-cache` - Refresh quest cache

## Colyseus Integration

### Room Type

Use the `QuestRoom` for quest-enabled gameplay:

```typescript
// Client connection
const room = await client.joinOrCreate('quest', {
  uid: 'player_user_id',
  x: 100,
  y: 200
});
```

### Message Types

#### Quest Actions

```typescript
// Start a quest
room.send('quest_action', {
  action: 'start_quest',
  questId: 1
});

// Update quest progress
room.send('quest_action', {
  action: 'update_progress',
  questId: 1,
  stepOrder: 0,
  progressData: { items_collected: 3 }
});

// Complete a quest step
room.send('quest_action', {
  action: 'complete_step',
  questId: 1,
  stepOrder: 0,
  progressData: { completed: true }
});

// Get NPC response
room.send('quest_action', {
  action: 'get_npc_response',
  questId: 1,
  stepOrder: 0,
  npcId: 15503,
  responseType: 'unfinished'
});
```

#### Player Movement

```typescript
room.send('player_movement', {
  x: 150,
  y: 250,
  dir: 'right',
  animation: 'walk',
  isFlipped: false,
  isSitting: false
});
```

#### NPC Interaction

```typescript
room.send('npc_interaction', {
  npcId: 15503,
  questId: 1,
  stepOrder: 0
});
```

### Server Responses

#### Quest Events

```typescript
// Quest started successfully
room.onMessage('quest_started', (data) => {
  console.log('Quest started:', data.questId);
});

// Quest progress updated
room.onMessage('quest_progress_updated', (data) => {
  console.log('Progress updated:', data.questId, data.stepOrder);
});

// Quest step completed
room.onMessage('quest_step_completed', (data) => {
  console.log('Step completed:', data.questId, data.stepOrder);
});

// Quest data loaded on join
room.onMessage('quest_data_loaded', (data) => {
  console.log('Player quests:', data.quests);
  console.log('Available quests:', data.availableQuests);
});
```

#### NPC Interactions

```typescript
// NPC dialogue response
room.onMessage('npc_dialogue', (data) => {
  console.log('NPC says:', data.response.message_0);
});

// NPC response for quest
room.onMessage('npc_response', (response) => {
  console.log('NPC response:', response);
});
```

#### Error Handling

```typescript
// Quest-related errors
room.onMessage('quest_error', (error) => {
  console.error('Quest error:', error.message);
});

// NPC interaction errors
room.onMessage('npc_interaction_error', (error) => {
  console.error('NPC interaction error:', error.message);
});
```

## Usage Examples

### Starting a Quest

```typescript
// 1. Join quest room
const room = await client.joinOrCreate('quest', { uid: 'player123' });

// 2. Listen for quest data
room.onMessage('quest_data_loaded', (data) => {
  const availableQuests = data.availableQuests;
  // Show available quests to player
});

// 3. Start a quest
room.send('quest_action', {
  action: 'start_quest',
  questId: 1
});

// 4. Handle quest started
room.onMessage('quest_started', (data) => {
  console.log(`Started quest ${data.questId}`);
});
```

### Completing Quest Steps

```typescript
// 1. Update progress (e.g., collecting items)
room.send('quest_action', {
  action: 'update_progress',
  questId: 1,
  stepOrder: 0,
  progressData: { items_collected: 1 }
});

// 2. Complete the step when objective is met
room.send('quest_action', {
  action: 'complete_step',
  questId: 1,
  stepOrder: 0,
  progressData: { objective_completed: true }
});

// 3. Handle step completion
room.onMessage('quest_step_completed', (data) => {
  console.log(`Completed step ${data.stepOrder} of quest ${data.questId}`);
  // Quest automatically advances to next step
});
```

### NPC Interactions

```typescript
// 1. Interact with NPC
room.send('npc_interaction', {
  npcId: 15503, // Silvermist
  questId: 1,
  stepOrder: 0
});

// 2. Handle NPC dialogue
room.onMessage('npc_dialogue', (data) => {
  if (data.response) {
    // Display quest-related dialogue
    console.log(data.response.message_0);
    console.log(data.response.message_1);
  } else {
    // Display general dialogue
    console.log(data.message);
  }
});
```

## Error Handling

The quest system includes comprehensive error handling:

### Common Error Types

- **QuestNotFound**: Quest ID doesn't exist
- **QuestAlreadyStarted**: Attempting to start an already started quest
- **QuestNotStarted**: Trying to update progress for a quest not started
- **InvalidStepProgression**: Invalid step order progression
- **NpcResponseNotFound**: No response found for NPC/quest combination

### Error Response Format

```typescript
{
  code: 404,
  message: "Quest with ID 999 not found",
  details: { questId: 999 }
}
```

## Testing

Run the quest system tests:

```bash
npm test -- test/QuestSystem_test.ts
```

The test suite covers:
- Quest service functionality
- Room integration
- Message handling
- Error scenarios
- Progress synchronization

## Performance Considerations

1. **Quest Cache**: Quest templates are cached in memory for fast access
2. **Database Optimization**: Indexes on frequently queried fields
3. **Real-time Sync**: Only essential data is synchronized via Colyseus
4. **Batch Operations**: Multiple quest updates can be batched

## Future Enhancements

- Quest prerequisites and dependencies
- Quest rewards system integration
- Quest chains and branching narratives
- Time-limited quests
- Repeatable daily/weekly quests
- Quest sharing between players
- Achievement system integration
