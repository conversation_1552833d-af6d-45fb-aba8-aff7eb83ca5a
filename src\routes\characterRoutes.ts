import express, { Request, Response, NextFunction } from 'express';
import characterService from '../services/characterService'; // Adjust path as necessary
import authService from '../services/authService'; // For actual token verification

const router = express.Router();

// Re-use or define verifyAuthToken middleware (ensure it's consistent with friendRoutes)
const verifyAuthToken = async (req: Request & { user?: { id: string } }, res: Response, next: NextFunction) => {
    const authHeader = req.headers.authorization;
    const token = authHeader?.split(' ')[1]; // Expecting "Bearer <token>"

    if (!token) {
        return res.status(401).json({ success: false, message: "Unauthorized: Missing token." });
    }

    try {
        const decodedPayload = await authService.verifyToken(token);
        
        if (decodedPayload && decodedPayload.userId) {
            req.user = { id: decodedPayload.userId.toString() };
            return next();
        } else {
             return res.status(401).json({ success: false, message: "Unauthorized: Invalid token payload or token revoked." });
        }
    } catch (error: any) {
        console.error("Token verification error:", error.message);
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({ success: false, message: "Unauthorized: Token expired." });
        }
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({ success: false, message: "Unauthorized: Malformed token." });
        }
        return res.status(401).json({ success: false, message: "Unauthorized: Invalid token." });
    }
};

// POST /api/characters/ - Create a new character
router.post('/', verifyAuthToken, async (req: Request & { user?: { id: string } }, res: Response) => {
  const userId = req.user!.id;
  const { characterName, initialAppearanceDetails } = req.body;

  if (!characterName || typeof characterName !== 'string' || !initialAppearanceDetails || typeof initialAppearanceDetails !== 'object') {
    return res.status(400).json({ success: false, message: 'characterName (string) and initialAppearanceDetails (object) are required.' });
  }

  const result = await characterService.createCharacter(userId, characterName, initialAppearanceDetails);
  if (result.success) {
    return res.status(201).json(result); // 201 Created
  } else {
    // Determine appropriate status code (e.g., 400 for validation, 409 for conflict, 500 for server error)
    if (result.message && (result.message.includes('taken') || result.message.includes('limit'))) {
        return res.status(409).json(result); // Conflict
    }
    return res.status(400).json(result); // Bad Request for other validation issues
  }
});

// GET /api/characters/ - Get all characters for the authenticated user
router.get('/', verifyAuthToken, async (req: Request & { user?: { id: string } }, res: Response) => {
  const userId = req.user!.id;
  const result = await characterService.getUserCharacters(userId);

  if (result.success) {
    return res.status(200).json(result);
  } else {
    return res.status(500).json({ success: false, message: result.message || 'Failed to retrieve characters.' });
  }
});

// GET /api/characters/:characterId - Get specific character data
router.get('/:characterId', verifyAuthToken, async (req: Request & { user?: { id: string } }, res: Response) => {
  const userId = req.user!.id;
  const { characterId } = req.params;

  if (!characterId || typeof characterId !== 'string') {
    return res.status(400).json({ success: false, message: 'characterId (string) path parameter is required.' });
  }

  const result = await characterService.getCharacterData(userId, characterId);
  if (result.success) {
    return res.status(200).json(result);
  } else {
    if (result.message && (result.message.includes('not found') || result.message.includes('Access denied'))) {
        return res.status(404).json(result); // Not Found or Forbidden (masquerading as Not Found for security)
    }
    return res.status(500).json({ success: false, message: result.message || 'Failed to retrieve character data.' });
  }
});

// PUT /api/characters/:characterId - Update character data
router.put('/:characterId', verifyAuthToken, async (req: Request & { user?: { id: string } }, res: Response) => {
  const userId = req.user!.id;
  const { characterId } = req.params;
  const updates = req.body;

  if (!characterId || typeof characterId !== 'string') {
    return res.status(400).json({ success: false, message: 'characterId (string) path parameter is required.' });
  }
  if (!updates || typeof updates !== 'object' || Object.keys(updates).length === 0) {
    return res.status(400).json({ success: false, message: 'Request body with updates is required.' });
  }

  const result = await characterService.updateCharacterData(userId, characterId, updates);
  if (result.success) {
    return res.status(200).json(result);
  } else {
     if (result.message && (result.message.includes('not found') || result.message.includes('Access denied'))) {
        return res.status(404).json(result);
    }
    if (result.message && result.message.startsWith('Invalid') || result.message.includes('must be')) {
        return res.status(400).json(result); // Bad request for validation errors during update
    }
    return res.status(500).json({ success: false, message: result.message || 'Failed to update character data.' });
  }
});

export default router;
