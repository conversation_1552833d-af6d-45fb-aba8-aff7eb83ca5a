"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Validators = void 0;
const errors_1 = require("../errors");
/**
 * Validation utility functions
 */
class Validators {
    /**
     * Validate required field exists
     */
    static required(value, fieldName) {
        if (value === undefined || value === null || value === '') {
            throw new errors_1.MissingFieldError(fieldName);
        }
    }
    /**
     * Validate field type
     */
    static type(value, expectedType, fieldName) {
        const actualType = typeof value;
        if (actualType !== expectedType) {
            throw new errors_1.InvalidFieldTypeError(fieldName, expectedType, actualType);
        }
    }
    /**
     * Validate string length
     */
    static stringLength(value, fieldName, minLength, maxLength) {
        if (typeof value !== 'string') {
            throw new errors_1.InvalidFieldTypeError(fieldName, 'string', typeof value);
        }
        const length = value.length;
        if (minLength !== undefined && length < minLength) {
            throw new errors_1.FieldLengthError(fieldName, length, minLength, maxLength);
        }
        if (maxLength !== undefined && length > maxLength) {
            throw new errors_1.FieldLengthError(fieldName, length, minLength, maxLength);
        }
    }
    /**
     * Validate number range
     */
    static numberRange(value, fieldName, min, max) {
        if (typeof value !== 'number' || isNaN(value)) {
            throw new errors_1.InvalidFieldTypeError(fieldName, 'number', typeof value);
        }
        if (min !== undefined && value < min) {
            throw new errors_1.InvalidFieldValueError(fieldName, value, [`>= ${min}`]);
        }
        if (max !== undefined && value > max) {
            throw new errors_1.InvalidFieldValueError(fieldName, value, [`<= ${max}`]);
        }
    }
    /**
     * Validate value is in allowed list
     */
    static oneOf(value, allowedValues, fieldName) {
        if (!allowedValues.includes(value)) {
            throw new errors_1.InvalidFieldValueError(fieldName, value, allowedValues);
        }
    }
    /**
     * Validate email format
     */
    static email(value, fieldName = 'email') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            throw new errors_1.InvalidFormatError(fieldName, 'valid email address', value);
        }
    }
    /**
     * Validate username format
     */
    static username(value, fieldName = 'username') {
        // Username: 3-20 characters, alphanumeric and underscore only
        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
        if (!usernameRegex.test(value)) {
            throw new errors_1.InvalidFormatError(fieldName, '3-20 characters, alphanumeric and underscore only', value);
        }
    }
    /**
     * Validate password strength
     */
    static password(value, fieldName = 'password') {
        if (value.length < 6) {
            throw new errors_1.FieldLengthError(fieldName, value.length, 6);
        }
        // Add more password rules as needed
    }
    /**
     * Validate character name
     */
    static characterName(value, fieldName = 'characterName') {
        // Character name: 2-16 characters, letters, numbers, spaces, and some special chars
        const nameRegex = /^[a-zA-Z0-9\s\-_']{2,16}$/;
        if (!nameRegex.test(value)) {
            throw new errors_1.InvalidFormatError(fieldName, '2-16 characters, letters, numbers, spaces, hyphens, underscores, and apostrophes only', value);
        }
    }
    /**
     * Validate coordinates
     */
    static coordinates(x, y, fieldPrefix = 'position') {
        this.numberRange(x, `${fieldPrefix}.x`, -10000, 10000);
        this.numberRange(y, `${fieldPrefix}.y`, -10000, 10000);
    }
    /**
     * Validate rotation
     */
    static rotation(value, fieldName = 'rotation') {
        this.numberRange(value, fieldName, 0, 359);
    }
    /**
     * Validate boolean
     */
    static boolean(value, fieldName) {
        if (typeof value !== 'boolean') {
            throw new errors_1.InvalidFieldTypeError(fieldName, 'boolean', typeof value);
        }
    }
    /**
     * Validate array
     */
    static array(value, fieldName, minLength, maxLength) {
        if (!Array.isArray(value)) {
            throw new errors_1.InvalidFieldTypeError(fieldName, 'array', typeof value);
        }
        if (minLength !== undefined && value.length < minLength) {
            throw new errors_1.FieldLengthError(fieldName, value.length, minLength, maxLength);
        }
        if (maxLength !== undefined && value.length > maxLength) {
            throw new errors_1.FieldLengthError(fieldName, value.length, minLength, maxLength);
        }
    }
    /**
     * Validate object
     */
    static object(value, fieldName) {
        if (typeof value !== 'object' || value === null || Array.isArray(value)) {
            throw new errors_1.InvalidFieldTypeError(fieldName, 'object', typeof value);
        }
    }
    /**
     * Validate JSON string
     */
    static jsonString(value, fieldName) {
        try {
            JSON.parse(value);
        }
        catch (error) {
            throw new errors_1.InvalidFormatError(fieldName, 'valid JSON string', value);
        }
    }
    /**
     * Validate UUID format
     */
    static uuid(value, fieldName) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(value)) {
            throw new errors_1.InvalidFormatError(fieldName, 'valid UUID', value);
        }
    }
}
exports.Validators = Validators;
