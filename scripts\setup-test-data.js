const mysql = require('mysql2/promise');
const crypto = require('crypto');
require('dotenv').config();

/**
 * Setup test data for the virtual world game server
 * This script creates necessary test users, items, quests, and shop data
 */

async function setupTestData() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🚀 Setting up test data...');

    // Create test users
    console.log('📝 Creating test users...');

    // Create users with both fixed IDs and dynamic patterns that tests might use
    const baseUsers = [
      { id: 'test_inventory_user_1748262809534', username: 'test_inventory_user' },
      { id: 'test_shop_user_1748262810005', username: 'test_shop_user' },
      { id: 'test_user_1', username: 'test_user_1' },
      { id: 'test_user_2', username: 'test_user_2' },
      { id: 'test_user_3', username: 'test_user_3' },
      { id: 'test_user_4', username: 'test_user_4' },
      { id: 'test_user_5', username: 'test_user_5' },
      { id: 'test_user_6', username: 'test_user_6' },
      { id: 'test_user_7', username: 'test_user_7' },
      { id: 'test_user_8', username: 'test_user_8' },
    ];

    // Add dynamic users that might be created by tests
    const dynamicUsers = [];
    const currentTime = Date.now();
    for (let i = 0; i < 10; i++) {
      const timestamp = currentTime + i * 1000; // Different timestamps
      dynamicUsers.push({
        id: `test_inventory_user_${timestamp}`,
        username: `test_inventory_user_${timestamp}`
      });
      dynamicUsers.push({
        id: `test_shop_user_${timestamp}`,
        username: `test_shop_user_${timestamp}`
      });
    }

    const testUsers = [...baseUsers, ...dynamicUsers];

    for (const user of testUsers) {
      const passwordHash = crypto.createHash('sha256').update('test_password').digest('hex');
      await connection.query(
        'INSERT IGNORE INTO users (id, username, password_hash) VALUES (?, ?, ?)',
        [user.id, user.username, passwordHash]
      );
    }

    // Create test items
    console.log('🎒 Creating test items...');
    const testItems = [
      {
        item_id: 'test_item_1',
        item_name: 'Test Sword',
        description: 'A basic test sword',
        item_type: 'wearable',
        category: 'melee',
        is_stackable: false,
        max_stack_size: 1,
        data: JSON.stringify({ attack: 10, durability: 100 })
      },
      {
        item_id: 'test_item_2',
        item_name: 'Health Potion',
        description: 'Restores health',
        item_type: 'consumable',
        category: 'potion',
        is_stackable: true,
        max_stack_size: 99,
        data: JSON.stringify({ heal_amount: 50 })
      },
      {
        item_id: 'test_item_3',
        item_name: 'Magic Hat',
        description: 'A stylish magic hat',
        item_type: 'wearable',
        category: 'hat',
        is_stackable: false,
        max_stack_size: 1,
        data: JSON.stringify({ magic_power: 5 })
      }
    ];

    for (const item of testItems) {
      await connection.query(
        'INSERT IGNORE INTO items (item_id, item_name, description, item_type, category, is_stackable, max_stack_size, data) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [item.item_id, item.item_name, item.description, item.item_type, item.category, item.is_stackable, item.max_stack_size, item.data]
      );
    }

    // Create test quests
    console.log('🎯 Creating test quests...');
    await connection.query(
      'INSERT IGNORE INTO quests (id, title, accept_message_0, accept_message_1, decline_message_0, reward_message_0_0) VALUES (?, ?, ?, ?, ?, ?)',
      [1, 'Test Quest', 'Welcome to the test quest!', 'Are you ready to begin?', 'Maybe next time.', 'Congratulations on completing the quest!']
    );

    // Create test quest steps
    await connection.query(
      'INSERT IGNORE INTO quest_steps (id, quest_id, step_order, goal, summary, intro_0) VALUES (?, ?, ?, ?, ?, ?)',
      [1, 1, 0, 'Talk to the NPC', 'Start your adventure', 'Hello, adventurer!']
    );

    // Create test quest step responses
    await connection.query(
      'INSERT IGNORE INTO quest_step_responses (id, step_id, npc_id, response_type, message_0) VALUES (?, ?, ?, ?, ?)',
      [1, 1, 1, 'finished', 'Well done! You have completed this step.']
    );

    // Create shop categories
    console.log('🛍️ Creating shop categories...');
    const shopCategories = [
      { id: 1, name: 'Weapons', display_name: 'Weapons', description: 'Combat equipment', sort_order: 1 },
      { id: 2, name: 'Clothing', display_name: 'Clothing', description: 'Fashion items', sort_order: 2 },
      { id: 3, name: 'Consumables', display_name: 'Consumables', description: 'Potions and food', sort_order: 3 }
    ];

    for (const category of shopCategories) {
      await connection.query(
        'INSERT IGNORE INTO shop_categories (id, name, display_name, description, sort_order) VALUES (?, ?, ?, ?, ?)',
        [category.id, category.name, category.display_name, category.description, category.sort_order]
      );
    }

    // Create shop items
    console.log('💰 Creating shop items...');
    const shopItems = [
      {
        id: 1,
        item_id: 'test_item_1',
        category_id: 1,
        name: 'Test Sword',
        description: 'A basic test sword for sale',
        price_gold: 100,
        price_diamonds: 0,
        is_available: true,
        remaining_stock: null
      },
      {
        id: 2,
        item_id: 'test_item_2',
        category_id: 3,
        name: 'Health Potion',
        description: 'Restores health for sale',
        price_gold: 25,
        price_diamonds: 0,
        is_available: true,
        remaining_stock: 100
      }
    ];

    for (const shopItem of shopItems) {
      await connection.query(
        'INSERT IGNORE INTO shop_items (id, item_id, category_id, name, description, price_gold, price_diamonds, is_available, remaining_stock) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [shopItem.id, shopItem.item_id, shopItem.category_id, shopItem.name, shopItem.description, shopItem.price_gold, shopItem.price_diamonds, shopItem.is_available, shopItem.remaining_stock]
      );
    }

    console.log('✅ Test data setup completed successfully!');
    console.log('📊 Created:');
    console.log(`   - ${testUsers.length} test users`);
    console.log(`   - ${testItems.length} test items`);
    console.log(`   - 1 test quest with steps and responses`);
    console.log(`   - ${shopCategories.length} shop categories`);
    console.log(`   - ${shopItems.length} shop items`);

  } catch (error) {
    console.error('❌ Error setting up test data:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupTestData()
    .then(() => {
      console.log('🎉 Test data setup completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test data setup failed:', error);
      process.exit(1);
    });
}

module.exports = { setupTestData };
