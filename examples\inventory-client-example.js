/**
 * Inventory System Client Example
 * 
 * This example demonstrates how to interact with the inventory system
 * through HTTP API calls.
 * 
 * To run this example:
 * 1. Make sure the server is running (npm start)
 * 2. Make sure the inventory system is set up (npm run setup-shop-system)
 * 3. Run: node examples/inventory-client-example.js
 */

const axios = require('axios');

class InventoryClientExample {
  constructor() {
    this.baseURL = 'http://localhost:2567/api/inventory';
    this.testUserId = 'example_inventory_user_' + Date.now();
  }

  async runExample() {
    console.log('📦 Inventory System Client Example');
    console.log('==================================\n');

    try {
      // Step 1: Get inventory categories information
      console.log('1️⃣ Getting inventory categories...');
      const categories = await this.getInventoryCategories();
      console.log('Available categories:');
      Object.entries(categories).forEach(([key, category]) => {
        console.log(`  - ${key}: ${category.name} (types: ${category.types.join(', ')})`);
      });

      // Step 2: Get user's inventory configuration
      console.log('\n2️⃣ Getting user inventory configuration...');
      const config = await this.getInventoryConfig();
      console.log(`Inventory configuration for user ${this.testUserId}:`);
      console.log(`  - Max capacity: ${config.max_capacity}`);
      console.log(`  - Default sort order: ${config.default_sort_order}`);
      console.log(`  - Auto sort enabled: ${config.auto_sort_enabled}`);
      console.log(`  - Show new items first: ${config.show_new_items_first}`);

      // Step 3: Get user's complete inventory
      console.log('\n3️⃣ Getting user inventory...');
      const inventory = await this.getInventory();
      console.log(`User has ${inventory.totalItems} items (${inventory.uniqueItems} unique):`);
      console.log(`  - Capacity used: ${inventory.capacityUsed}/${inventory.maxCapacity}`);
      console.log(`  - Has new items: ${inventory.hasNewItems}`);
      console.log(`  - Total pages: ${inventory.totalPages}`);
      
      console.log('Category breakdown:');
      Object.entries(inventory.categories).forEach(([category, count]) => {
        console.log(`  - ${category}: ${count} items`);
      });

      if (inventory.items.length > 0) {
        console.log('\nFirst few items:');
        inventory.items.slice(0, 3).forEach(item => {
          console.log(`  - ${item.item_name} x${item.quantity} (${item.rarity})`);
          console.log(`    Type: ${item.item_type}, Category: ${item.item_category}`);
          console.log(`    New: ${item.is_new}, Locked: ${item.is_locked}, Equipped: ${item.is_equipped}`);
          console.log(`    Can use: ${item.can_use}, Can sell: ${item.can_sell}, Can gift: ${item.can_gift}`);
        });
      }

      // Step 4: Get inventory by category
      console.log('\n4️⃣ Getting fashion items...');
      const fashionInventory = await this.getInventoryByCategory('FASHION');
      console.log(`Found ${fashionInventory.totalItems} fashion items`);

      // Step 5: Search inventory
      console.log('\n5️⃣ Searching for items...');
      const searchResults = await this.searchInventory('dress');
      console.log(`Found ${searchResults.length} items matching "dress":`);
      searchResults.slice(0, 3).forEach(item => {
        console.log(`  - ${item.item_name}: ${item.item_description || 'No description'}`);
      });

      // Step 6: Get recently acquired items
      console.log('\n6️⃣ Getting recently acquired items...');
      const recentItems = await this.getRecentItems();
      console.log(`${recentItems.length} recently acquired items:`);
      recentItems.forEach(item => {
        console.log(`  - ${item.item_name} x${item.quantity} (acquired: ${new Date(item.acquired_at).toLocaleString()})`);
      });

      // Step 7: Update inventory configuration
      console.log('\n7️⃣ Updating inventory configuration...');
      const configUpdates = {
        default_sort_order: 'name_asc',
        auto_sort_enabled: true,
        show_new_items_first: false
      };
      const updatedConfig = await this.updateInventoryConfig(configUpdates);
      console.log('✅ Configuration updated successfully');
      console.log(`  - New default sort order: ${updatedConfig.default_sort_order}`);
      console.log(`  - Auto sort enabled: ${updatedConfig.auto_sort_enabled}`);

      // Step 8: Mark items as seen
      if (inventory.hasNewItems) {
        console.log('\n8️⃣ Marking new items as seen...');
        const newItems = inventory.items.filter(item => item.is_new);
        const newItemIds = newItems.map(item => item.item_id);
        
        if (newItemIds.length > 0) {
          await this.markItemsAsSeen(newItemIds);
          console.log(`✅ Marked ${newItemIds.length} items as seen`);
        }
      } else {
        console.log('\n8️⃣ No new items to mark as seen');
      }

      // Step 9: Demonstrate item operations (if items exist)
      if (inventory.items.length > 0) {
        const testItem = inventory.items[0];
        
        console.log(`\n9️⃣ Demonstrating item operations with "${testItem.item_name}"...`);
        
        // Lock/unlock item
        try {
          await this.toggleItemLock(testItem.item_id, true);
          console.log('✅ Item locked successfully');
          
          await this.toggleItemLock(testItem.item_id, false);
          console.log('✅ Item unlocked successfully');
        } catch (error) {
          console.log(`ℹ️ Lock operation: ${error.response?.data?.error?.message || error.message}`);
        }

        // Try to equip item (if it's wearable)
        if (testItem.can_equip) {
          try {
            await this.toggleItemEquip(testItem.item_id, true);
            console.log('✅ Item equipped successfully');
            
            await this.toggleItemEquip(testItem.item_id, false);
            console.log('✅ Item unequipped successfully');
          } catch (error) {
            console.log(`ℹ️ Equip operation: ${error.response?.data?.error?.message || error.message}`);
          }
        }

        // Try to use item (if it's usable)
        if (testItem.can_use && testItem.quantity > 0) {
          try {
            const useResult = await this.useItem(testItem.item_id, 1);
            console.log('✅ Item used successfully');
            if (useResult.effects) {
              console.log('  Effects:', useResult.effects);
            }
          } catch (error) {
            console.log(`ℹ️ Use operation: ${error.response?.data?.error?.message || error.message}`);
          }
        }

        // Try to sell item (if it's sellable)
        if (testItem.can_sell && testItem.quantity > 0) {
          try {
            const sellResult = await this.sellItem(testItem.item_id, 1);
            console.log(`✅ Item sold for ${sellResult.goldEarned} gold`);
          } catch (error) {
            console.log(`ℹ️ Sell operation: ${error.response?.data?.error?.message || error.message}`);
          }
        }
      }

      // Step 10: Get operation history
      console.log('\n🔟 Getting inventory operation history...');
      const history = await this.getOperationHistory();
      console.log(`Found ${history.length} operations in history:`);
      history.slice(0, 5).forEach(operation => {
        console.log(`  - ${operation.operation_type}: ${operation.item_name} (${operation.quantity_change > 0 ? '+' : ''}${operation.quantity_change})`);
        console.log(`    Source: ${operation.operation_source}, Time: ${new Date(operation.created_at).toLocaleString()}`);
      });

      // Step 11: Get user currency
      console.log('\n1️⃣1️⃣ Getting user currency...');
      const currency = await this.getUserCurrency();
      console.log(`User currency:`);
      console.log(`  - Gold coins: ${currency.gold_coins}`);
      console.log(`  - Diamonds: ${currency.diamonds}`);

      // Step 12: Demonstrate inventory expansion
      console.log('\n1️⃣2️⃣ Demonstrating inventory expansion...');
      try {
        const expandedConfig = await this.expandInventory(10, 50); // 10 slots for 50 diamonds
        console.log(`✅ Inventory expanded! New capacity: ${expandedConfig.max_capacity}`);
      } catch (error) {
        console.log(`ℹ️ Expansion failed: ${error.response?.data?.error?.message || error.message}`);
      }

      console.log('\n🎉 Inventory system demo completed successfully!');

    } catch (error) {
      console.error('❌ Demo failed:', error.response?.data || error.message);
    }
  }

  async getInventoryCategories() {
    const response = await axios.get(`${this.baseURL}/categories`);
    return response.data.data;
  }

  async getInventoryConfig() {
    const response = await axios.get(`${this.baseURL}/${this.testUserId}/config`);
    return response.data.data;
  }

  async updateInventoryConfig(updates) {
    const response = await axios.put(`${this.baseURL}/${this.testUserId}/config`, updates);
    return response.data.data;
  }

  async getInventory(filters = {}, sortOrder = null, page = 1, pageSize = 20) {
    const params = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString(),
      ...filters
    });
    
    if (sortOrder) {
      params.append('sortOrder', sortOrder);
    }

    const response = await axios.get(`${this.baseURL}/${this.testUserId}?${params}`);
    return response.data.data;
  }

  async getInventoryByCategory(category) {
    return this.getInventory({ category });
  }

  async searchInventory(searchTerm) {
    const response = await axios.get(`${this.baseURL}/${this.testUserId}/search?q=${encodeURIComponent(searchTerm)}`);
    return response.data.data;
  }

  async getRecentItems(limit = 10) {
    const response = await axios.get(`${this.baseURL}/${this.testUserId}/recent?limit=${limit}`);
    return response.data.data;
  }

  async markItemsAsSeen(itemIds = null) {
    const response = await axios.post(`${this.baseURL}/${this.testUserId}/mark-seen`, {
      itemIds
    });
    return response.data;
  }

  async toggleItemLock(itemId, locked) {
    const response = await axios.post(`${this.baseURL}/${this.testUserId}/items/${itemId}/lock`, {
      locked
    });
    return response.data;
  }

  async toggleItemEquip(itemId, equipped) {
    const response = await axios.post(`${this.baseURL}/${this.testUserId}/items/${itemId}/equip`, {
      equipped
    });
    return response.data;
  }

  async useItem(itemId, quantity = 1) {
    const response = await axios.post(`${this.baseURL}/${this.testUserId}/items/${itemId}/use`, {
      quantity
    });
    return response.data.data;
  }

  async sellItem(itemId, quantity = 1) {
    const response = await axios.post(`${this.baseURL}/${this.testUserId}/items/${itemId}/sell`, {
      quantity
    });
    return response.data.data;
  }

  async destroyItem(itemId, quantity = 1) {
    const response = await axios.post(`${this.baseURL}/${this.testUserId}/items/${itemId}/destroy`, {
      quantity,
      confirm: true
    });
    return response.data;
  }

  async expandInventory(additionalSlots, costDiamonds) {
    const response = await axios.post(`${this.baseURL}/${this.testUserId}/expand`, {
      additionalSlots,
      costDiamonds
    });
    return response.data.data;
  }

  async getOperationHistory(itemId = null, operationType = null, limit = 50) {
    const params = new URLSearchParams({ limit: limit.toString() });
    if (itemId) params.append('itemId', itemId);
    if (operationType) params.append('operationType', operationType);

    const response = await axios.get(`${this.baseURL}/${this.testUserId}/history?${params}`);
    return response.data.data;
  }

  async getUserCurrency() {
    const response = await axios.get(`${this.baseURL}/${this.testUserId}/currency`);
    return response.data.data;
  }
}

// Check if axios is available
try {
  require('axios');
} catch (error) {
  console.error('❌ This example requires axios. Please install it with: npm install axios');
  process.exit(1);
}

// Run the example
const example = new InventoryClientExample();
example.runExample().catch(error => {
  console.error('❌ Example failed:', error.message);
  process.exit(1);
});
