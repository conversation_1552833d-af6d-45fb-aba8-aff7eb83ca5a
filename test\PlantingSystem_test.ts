import { expect } from 'chai';
import PlantingService from '../src/services/PlantingService';
import PlantingOperationsService from '../src/services/PlantingOperationsService';
import CurrencyService from '../src/services/CurrencyService';
import InventoryService from '../src/services/InventoryService';

describe('Planting System Tests', () => {
  const testUserId = '2'; // Use existing test user
  const testSeedId = 'sunflower_seed';
  const testFertilizerId = 'basic_fertilizer';

  before(async () => {
    // Initialize test user with currency and seeds
    try {
      await CurrencyService.initializeUserCurrency(testUserId, 10000, 500);
    } catch (error: any) {
      if (error.code !== 'ER_DUP_ENTRY') {
        throw error;
      }
    }

    // Add test seeds to inventory
    try {
      await InventoryService.addItem(testUserId, testSeedId, 10, 'test', 'planting_test');
      await InventoryService.addItem(testUserId, testFertilizerId, 5, 'test', 'planting_test');
    } catch (error: any) {
      if (error.code !== 'ER_DUP_ENTRY') {
        console.log('⚠️  Test items already exist in inventory');
      }
    }
  });

  describe('Garden Management', () => {
    it('should initialize user garden with default plots', async () => {
      const garden = await PlantingService.initializeUserGarden(testUserId);

      expect(garden).to.be.an('array');
      expect(garden.length).to.equal(12); // 12 total plots

      // First 4 plots should be unlocked by default
      const unlockedPlots = garden.filter(plot => plot.is_unlocked);
      expect(unlockedPlots.length).to.equal(4);
    });

    it('should get user garden plots', async () => {
      const garden = await PlantingService.getUserGarden(testUserId);

      expect(garden).to.be.an('array');
      expect(garden.length).to.be.greaterThan(0);

      // Check plot structure
      const plot = garden[0];
      expect(plot).to.have.property('id');
      expect(plot).to.have.property('user_id');
      expect(plot).to.have.property('plot_index');
      expect(plot).to.have.property('is_unlocked');
    });

    it('should unlock a garden plot', async () => {
      const plotIndex = 4; // First locked plot

      const result = await PlantingService.unlockPlot(testUserId, plotIndex);

      if (result.success) {
        expect(result.success).to.be.true;
        expect(result.exp_gained).to.be.a('number');
        expect(result.exp_gained).to.be.greaterThan(0);
      } else {
        // Plot might already be unlocked or insufficient resources
        expect(result.message).to.be.a('string');
      }
    });
  });

  describe('Planting Level System', () => {
    it('should get user planting level', async () => {
      const level = await PlantingService.getUserPlantingLevel(testUserId);

      expect(level).to.have.property('user_id');
      expect(level).to.have.property('current_level');
      expect(level).to.have.property('current_exp');
      expect(level).to.have.property('total_plants');
      expect(level).to.have.property('total_harvests');

      expect(level.current_level).to.be.a('number');
      expect(level.current_level).to.be.greaterThan(0);
    });

    it('should calculate experience correctly', async () => {
      const plantExp = PlantingService.calculateExpGain('plant', 'common');
      const waterExp = PlantingService.calculateExpGain('water', 'common');
      const harvestExp = PlantingService.calculateExpGain('harvest', 'rare');

      expect(plantExp).to.be.a('number');
      expect(plantExp).to.be.greaterThan(0);
      expect(waterExp).to.be.a('number');
      expect(harvestExp).to.be.greaterThan(plantExp); // Harvest should give more exp

      // Rare seeds should give more exp than common
      const commonHarvestExp = PlantingService.calculateExpGain('harvest', 'common');
      expect(harvestExp).to.be.greaterThan(commonHarvestExp);
    });

    it('should calculate level requirements correctly', async () => {
      const level1Exp = PlantingService.calculateExpForLevel(1);
      const level2Exp = PlantingService.calculateExpForLevel(2);
      const level5Exp = PlantingService.calculateExpForLevel(5);

      expect(level1Exp).to.be.a('number');
      expect(level2Exp).to.be.greaterThan(level1Exp);
      expect(level5Exp).to.be.greaterThan(level2Exp);
    });
  });

  describe('Seed Management', () => {
    it('should get available seeds', async () => {
      const seeds = await PlantingService.getAvailableSeeds();

      expect(seeds).to.be.an('array');
      expect(seeds.length).to.be.greaterThan(0);

      // Check seed structure
      const seed = seeds[0];
      expect(seed).to.have.property('seed_id');
      expect(seed).to.have.property('seed_name');
      expect(seed).to.have.property('rarity');
      expect(seed).to.have.property('growth_time_minutes');
      expect(seed).to.have.property('required_level');
    });

    it('should filter seeds by user level', async () => {
      const allSeeds = await PlantingService.getAvailableSeeds();
      const level1Seeds = await PlantingService.getAvailableSeeds(1);
      const level10Seeds = await PlantingService.getAvailableSeeds(10);

      expect(level1Seeds.length).to.be.lessThanOrEqual(allSeeds.length);
      expect(level10Seeds.length).to.be.greaterThanOrEqual(level1Seeds.length);

      // All level 1 seeds should have required_level <= 1
      level1Seeds.forEach(seed => {
        expect(seed.required_level).to.be.lessThanOrEqual(1);
      });
    });
  });

  describe('Planting Operations', () => {
    let plantedFlowerId: number;

    it('should plant a seed successfully', async () => {
      const plotIndex = 0; // Use first unlocked plot

      const result = await PlantingOperationsService.plantSeed(testUserId, testSeedId, plotIndex);

      expect(result.success).to.be.true;
      expect(result.message).to.include('planted successfully');
      expect(result.data).to.have.property('planted_flower_id');
      expect(result.exp_gained).to.be.a('number');
      expect(result.exp_gained).to.be.greaterThan(0);

      plantedFlowerId = result.data.planted_flower_id;
    });

    it('should not plant on occupied plot', async () => {
      const plotIndex = 0; // Same plot as above

      const result = await PlantingOperationsService.plantSeed(testUserId, testSeedId, plotIndex);

      expect(result.success).to.be.false;
      expect(result.message).to.include('occupied');
    });

    it('should water a planted flower', async () => {
      if (!plantedFlowerId) {
        console.log('⚠️  Skipping water test - no planted flower');
        return;
      }

      const result = await PlantingOperationsService.waterFlower(testUserId, plantedFlowerId);

      expect(result.success).to.be.true;
      expect(result.message).to.include('watered successfully');
      expect(result.data).to.have.property('water_count');
      expect(result.exp_gained).to.be.a('number');
    });

    it('should apply fertilizer to a planted flower', async () => {
      if (!plantedFlowerId) {
        console.log('⚠️  Skipping fertilizer test - no planted flower');
        return;
      }

      const result = await PlantingOperationsService.fertilizeFlower(testUserId, plantedFlowerId, testFertilizerId);

      expect(result.success).to.be.true;
      expect(result.message).to.include('applied successfully');
      expect(result.data).to.have.property('fertilizer_count');
      expect(result.data).to.have.property('yield_boost');
      expect(result.exp_gained).to.be.a('number');
    });

    it('should get planted flowers', async () => {
      const flowers = await PlantingService.getUserPlantedFlowers(testUserId);

      expect(flowers).to.be.an('array');

      if (flowers.length > 0) {
        const flower = flowers[0];
        expect(flower).to.have.property('id');
        expect(flower).to.have.property('seed_id');
        expect(flower).to.have.property('plant_stage');
        expect(flower).to.have.property('planted_at');
        expect(flower).to.have.property('seed_name'); // Joined from seed template
      }
    });

    it('should update flower states', async () => {
      // This is mainly a smoke test to ensure the method doesn't crash
      await PlantingOperationsService.updateFlowerStates(testUserId);

      // Get flowers after update
      const flowers = await PlantingService.getUserPlantedFlowers(testUserId);

      // Should not crash and should return array
      expect(flowers).to.be.an('array');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid user ID', async () => {
      try {
        await PlantingService.getUserGarden('invalid_user');
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.exist;
      }
    });

    it('should handle invalid seed ID', async () => {
      const result = await PlantingOperationsService.plantSeed(testUserId, 'invalid_seed', 0);

      expect(result.success).to.be.false;
      expect(result.message).to.include('not have this seed');
    });

    it('should handle invalid plot index', async () => {
      const result = await PlantingOperationsService.plantSeed(testUserId, testSeedId, 999);

      expect(result.success).to.be.false;
      expect(result.message).to.include('not found');
    });

    it('should handle insufficient level for seed', async () => {
      // Try to plant a high-level seed
      const result = await PlantingOperationsService.plantSeed(testUserId, 'phoenix_flower_seed', 1);

      expect(result.success).to.be.false;
      // Could fail due to either insufficient level or not having the seed
      expect(result.message).to.satisfy((msg: string) =>
        msg.includes('level') || msg.includes('not have this seed')
      );
    });
  });

  describe('Integration Tests', () => {
    it('should complete a full planting cycle', async () => {
      // This test simulates a complete planting cycle
      const plotIndex = 2; // Use another plot

      // 1. Plant seed
      const plantResult = await PlantingOperationsService.plantSeed(testUserId, testSeedId, plotIndex);
      if (!plantResult.success) {
        console.log('⚠️  Skipping integration test - could not plant seed:', plantResult.message);
        return;
      }

      const flowerId = plantResult.data.planted_flower_id;

      // 2. Water the flower
      const waterResult = await PlantingOperationsService.waterFlower(testUserId, flowerId);
      expect(waterResult.success).to.be.true;

      // 3. Apply fertilizer
      const fertilizeResult = await PlantingOperationsService.fertilizeFlower(testUserId, flowerId, testFertilizerId);
      expect(fertilizeResult.success).to.be.true;

      // 4. Check flower status
      const flowers = await PlantingService.getUserPlantedFlowers(testUserId);
      const ourFlower = flowers.find(f => f.id === flowerId);
      expect(ourFlower).to.exist;

      if (ourFlower) {
        expect(ourFlower.water_count).to.be.greaterThan(0);
        expect(ourFlower.fertilizer_count).to.be.greaterThan(0);
        console.log(`✅ Integration test completed - Flower ID: ${flowerId}, Stage: ${ourFlower.plant_stage}`);
      }
    });
  });
});
