const mysql = require('mysql2/promise');
require('dotenv').config();

async function createMissingTables() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🔧 Creating missing tables...');
    
    // Create user_inventory_config table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS user_inventory_config (
        user_id BIGINT NOT NULL COMMENT 'Reference to user ID',
        max_capacity INT UNSIGNED NOT NULL DEFAULT 100 COMMENT 'Maximum inventory capacity',
        default_sort_order ENUM('acquired_time_desc', 'acquired_time_asc', 'name_asc', 'name_desc', 'quantity_desc', 'quantity_asc', 'rarity_desc') NOT NULL DEFAULT 'acquired_time_desc' COMMENT 'Default sort order',
        auto_sort_enabled BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether auto-sort is enabled',
        show_new_items_first BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether to show new items first',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (user_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores user inventory configuration and preferences'
    `);
    
    console.log('✅ user_inventory_config table created');
    
    // Create inventory_operations_log table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS inventory_operations_log (
        id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique log entry ID',
        user_id BIGINT NOT NULL COMMENT 'Reference to user ID',
        item_id VARCHAR(255) NOT NULL COMMENT 'Reference to item template',
        operation_type ENUM('add', 'remove', 'use', 'sell', 'gift_send', 'gift_receive', 'equip', 'unequip', 'lock', 'unlock', 'destroy') NOT NULL COMMENT 'Type of operation',
        quantity_change INT NOT NULL COMMENT 'Quantity change (positive for add, negative for remove)',
        quantity_before BIGINT UNSIGNED NOT NULL COMMENT 'Quantity before operation',
        quantity_after BIGINT UNSIGNED NOT NULL COMMENT 'Quantity after operation',
        operation_source VARCHAR(255) COMMENT 'Source of operation (shop, quest, gift, etc.)',
        metadata JSON COMMENT 'Additional operation metadata',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (item_id) REFERENCES items(item_id) ON DELETE CASCADE,
        INDEX idx_user_operation (user_id, operation_type, created_at),
        INDEX idx_item_operation (item_id, operation_type, created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Logs all inventory operations for auditing and analytics'
    `);
    
    console.log('✅ inventory_operations_log table created');
    
    // Create room_registry table if missing
    await connection.query(`
      CREATE TABLE IF NOT EXISTS room_registry (
        room_id VARCHAR(255) NOT NULL COMMENT 'Dynamic room ID (e.g., home_123, garden_456)',
        room_type ENUM('home', 'garden', 'quest') NOT NULL COMMENT 'Type of room',
        owner_uid BIGINT NOT NULL COMMENT 'User ID who owns this room',
        access_level ENUM('private', 'friends_only', 'public') NOT NULL DEFAULT 'private' COMMENT 'Room access level',
        is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether room is currently active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When room was first created',
        last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last time room was accessed',
        PRIMARY KEY (room_id),
        FOREIGN KEY (owner_uid) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_owner_type (owner_uid, room_type),
        INDEX idx_type_access (room_type, access_level),
        INDEX idx_active_accessed (is_active, last_accessed)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Registry for dynamic room management'
    `);
    
    console.log('✅ room_registry table created');
    
    // Create notifications table if missing
    await connection.query(`
      CREATE TABLE IF NOT EXISTS notifications (
        id VARCHAR(255) NOT NULL COMMENT 'Unique notification ID',
        type ENUM('friend_request_sent', 'friend_request_received', 'friend_request_accepted', 'friend_request_declined', 'friend_removed', 'user_blocked', 'user_unblocked', 'user_online', 'user_offline', 'room_invitation', 'system_message') NOT NULL COMMENT 'Type of notification',
        from_user_id BIGINT NULL COMMENT 'User ID who triggered the notification (can be null for system messages)',
        to_user_id BIGINT NOT NULL COMMENT 'User ID who should receive the notification',
        title VARCHAR(255) NOT NULL COMMENT 'Notification title',
        message TEXT NOT NULL COMMENT 'Notification message content',
        data JSON NULL COMMENT 'Additional notification data as JSON',
        is_read BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether the notification has been read',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When notification was created',
        expires_at TIMESTAMP NOT NULL COMMENT 'When notification expires and can be deleted',
        PRIMARY KEY (id),
        FOREIGN KEY (from_user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (to_user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_to_user_created (to_user_id, created_at),
        INDEX idx_to_user_read (to_user_id, is_read),
        INDEX idx_expires_at (expires_at),
        INDEX idx_type (type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Real-time notifications for users'
    `);
    
    console.log('✅ notifications table created');
    
    console.log('🎉 All missing tables created successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await connection.end();
  }
}

createMissingTables();
