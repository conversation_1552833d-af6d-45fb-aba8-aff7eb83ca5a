import { BaseError } from './BaseError';

/**
 * Validation related errors
 */
export class ValidationError extends BaseError {
  constructor(message: string = 'Validation failed', details?: any) {
    super('ValidationError', 400, message, true, details);
  }
}

/**
 * Missing required field error
 */
export class MissingFieldError extends ValidationError {
  constructor(fieldName: string, message?: string) {
    const errorMessage = message || `Required field '${fieldName}' is missing`;
    super(errorMessage, { field: fieldName });
    this.name = 'MissingFieldError';
  }
}

/**
 * Invalid field type error
 */
export class InvalidFieldTypeError extends ValidationError {
  constructor(fieldName: string, expectedType: string, actualType: string) {
    const message = `Field '${fieldName}' must be of type '${expectedType}', got '${actualType}'`;
    super(message, { field: fieldName, expectedType, actualType });
    this.name = 'InvalidFieldTypeError';
  }
}

/**
 * Invalid field value error
 */
export class InvalidFieldValueError extends ValidationError {
  constructor(fieldName: string, value: any, allowedValues?: any[]) {
    let message = `Invalid value '${value}' for field '${fieldName}'`;
    if (allowedValues) {
      message += `. Allowed values: ${allowedValues.join(', ')}`;
    }
    super(message, { field: fieldName, value, allowedValues });
    this.name = 'InvalidFieldValueError';
  }
}

/**
 * Field length validation error
 */
export class FieldLengthError extends ValidationError {
  constructor(fieldName: string, actualLength: number, minLength?: number, maxLength?: number) {
    let message = `Field '${fieldName}' length is ${actualLength}`;
    if (minLength !== undefined && maxLength !== undefined) {
      message += `, must be between ${minLength} and ${maxLength} characters`;
    } else if (minLength !== undefined) {
      message += `, must be at least ${minLength} characters`;
    } else if (maxLength !== undefined) {
      message += `, must be at most ${maxLength} characters`;
    }
    
    super(message, { field: fieldName, actualLength, minLength, maxLength });
    this.name = 'FieldLengthError';
  }
}

/**
 * Invalid format error (e.g., email, phone number)
 */
export class InvalidFormatError extends ValidationError {
  constructor(fieldName: string, format: string, value?: any) {
    const message = `Field '${fieldName}' has invalid format. Expected: ${format}`;
    super(message, { field: fieldName, format, value });
    this.name = 'InvalidFormatError';
  }
}
