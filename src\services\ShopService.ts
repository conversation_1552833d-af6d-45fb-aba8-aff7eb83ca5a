import pool from '../utils/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';
import { ShopError } from '../errors';
import CurrencyService from './CurrencyService';
import InventoryService from './InventoryService';
import ItemService from './ItemService';

/**
 * Shop category interface
 */
export interface ShopCategory extends RowDataPacket {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
  parent_category_id?: number;
  created_at: Date;
  updated_at: Date;
}

/**
 * Shop item interface
 */
export interface ShopItem extends RowDataPacket {
  id: number;
  item_id: string;
  category_id: number;
  name: string;
  description?: string;
  price_gold?: number;
  price_diamonds?: number;
  original_price_gold?: number;
  original_price_diamonds?: number;
  is_available: boolean;
  is_featured: boolean;
  is_limited_time: boolean;
  available_from?: Date;
  available_until?: Date;
  max_purchases_per_user?: number;
  total_stock?: number;
  remaining_stock?: number;
  sort_order: number;
  tags?: string; // JSON string
  metadata?: string; // JSON string
  created_at: Date;
  updated_at: Date;
}

/**
 * Shop item with details interface
 */
export interface ShopItemWithDetails extends ShopItem {
  item_name: string;
  item_description?: string;
  item_type: string;
  item_category?: string;
  is_stackable: boolean;
  max_stack_size: number;
  item_data?: string;
  category_name: string;
  category_display_name: string;
}

/**
 * Purchase request interface
 */
export interface PurchaseRequest {
  userId: string;
  shopItemId: number;
  quantity: number;
  giftRecipientId?: string;
  promotionId?: number;
}

/**
 * Purchase result interface
 */
export interface PurchaseResult {
  success: boolean;
  transactionId: number;
  itemsAdded: Array<{ itemId: string; quantity: number }>;
  currencySpent: { gold: number; diamonds: number };
  remainingCurrency: { gold: number; diamonds: number };
}

/**
 * Shop service for managing the shop system
 */
class ShopService {
  private static instance: ShopService;
  private categoryCache = new Map<number, ShopCategory>();
  private shopItemCache = new Map<number, ShopItem>();

  private constructor() {
    this.loadShopCache();
  }

  public static getInstance(): ShopService {
    if (!ShopService.instance) {
      ShopService.instance = new ShopService();
    }
    return ShopService.instance;
  }

  /**
   * Load shop data into cache
   */
  private async loadShopCache(): Promise<void> {
    try {
      // Load categories
      const [categories] = await pool.query<ShopCategory[]>(
        'SELECT * FROM shop_categories WHERE is_active = TRUE ORDER BY sort_order'
      );
      categories.forEach(category => {
        this.categoryCache.set(category.id, category);
      });

      // Load shop items
      const [items] = await pool.query<ShopItem[]>(
        'SELECT * FROM shop_items WHERE is_available = TRUE ORDER BY category_id, sort_order'
      );
      items.forEach(item => {
        this.shopItemCache.set(item.id, item);
      });

      console.log(`Loaded ${categories.length} categories and ${items.length} shop items into cache`);
    } catch (error) {
      console.error('Failed to load shop cache:', error);
    }
  }

  /**
   * Get all shop categories
   */
  async getCategories(): Promise<ShopCategory[]> {
    try {
      const [rows] = await pool.query<ShopCategory[]>(
        'SELECT * FROM shop_categories WHERE is_active = TRUE ORDER BY sort_order'
      );
      return rows;
    } catch (error) {
      console.error('Failed to get shop categories:', error);
      throw ShopError.invalidShopData('Failed to retrieve shop categories');
    }
  }

  /**
   * Get category by ID
   */
  async getCategory(categoryId: number): Promise<ShopCategory | null> {
    try {
      const [rows] = await pool.query<ShopCategory[]>(
        'SELECT * FROM shop_categories WHERE id = ? AND is_active = TRUE',
        [categoryId]
      );
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('Failed to get shop category:', error);
      throw ShopError.categoryNotFound(categoryId);
    }
  }

  /**
   * Get shop items by category
   */
  async getItemsByCategory(categoryId: number): Promise<ShopItemWithDetails[]> {
    try {
      const [rows] = await pool.query<ShopItemWithDetails[]>(
        `SELECT
          si.*,
          i.item_name, i.description as item_description, i.item_type, i.category as item_category,
          i.is_stackable, i.max_stack_size, i.data as item_data,
          sc.name as category_name, sc.display_name as category_display_name
         FROM shop_items si
         JOIN items i ON si.item_id = i.item_id
         JOIN shop_categories sc ON si.category_id = sc.id
         WHERE si.category_id = ? AND si.is_available = TRUE
         AND (si.available_from IS NULL OR si.available_from <= NOW())
         AND (si.available_until IS NULL OR si.available_until > NOW())
         ORDER BY si.sort_order`,
        [categoryId]
      );
      return rows;
    } catch (error) {
      console.error('Failed to get shop items by category:', error);
      throw ShopError.invalidShopData('Failed to retrieve shop items');
    }
  }

  /**
   * Get shop item by ID
   */
  async getShopItem(shopItemId: number): Promise<ShopItemWithDetails | null> {
    try {
      const [rows] = await pool.query<ShopItemWithDetails[]>(
        `SELECT
          si.*,
          i.item_name, i.description as item_description, i.item_type, i.category as item_category,
          i.is_stackable, i.max_stack_size, i.data as item_data,
          sc.name as category_name, sc.display_name as category_display_name
         FROM shop_items si
         JOIN items i ON si.item_id = i.item_id
         JOIN shop_categories sc ON si.category_id = sc.id
         WHERE si.id = ?`,
        [shopItemId]
      );

      if (rows.length === 0) {
        throw ShopError.itemNotFound(shopItemId);
      }

      return rows[0];
    } catch (error) {
      console.error('Failed to get shop item:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.invalidShopData('Database error while fetching shop item');
    }
  }

  /**
   * Get featured items
   */
  async getFeaturedItems(): Promise<ShopItemWithDetails[]> {
    try {
      const [rows] = await pool.query<ShopItemWithDetails[]>(
        `SELECT
          si.*,
          i.item_name, i.description as item_description, i.item_type, i.category as item_category,
          i.is_stackable, i.max_stack_size, i.data as item_data,
          sc.name as category_name, sc.display_name as category_display_name
         FROM shop_items si
         JOIN items i ON si.item_id = i.item_id
         JOIN shop_categories sc ON si.category_id = sc.id
         WHERE si.is_featured = TRUE AND si.is_available = TRUE
         AND (si.available_from IS NULL OR si.available_from <= NOW())
         AND (si.available_until IS NULL OR si.available_until > NOW())
         ORDER BY si.sort_order`
      );
      return rows;
    } catch (error) {
      console.error('Failed to get featured items:', error);
      throw ShopError.invalidShopData('Failed to retrieve featured items');
    }
  }

  /**
   * Get limited time items
   */
  async getLimitedTimeItems(): Promise<ShopItemWithDetails[]> {
    try {
      const [rows] = await pool.query<ShopItemWithDetails[]>(
        `SELECT
          si.*,
          i.item_name, i.description as item_description, i.item_type, i.category as item_category,
          i.is_stackable, i.max_stack_size, i.data as item_data,
          sc.name as category_name, sc.display_name as category_display_name
         FROM shop_items si
         JOIN items i ON si.item_id = i.item_id
         JOIN shop_categories sc ON si.category_id = sc.id
         WHERE si.is_limited_time = TRUE AND si.is_available = TRUE
         AND (si.available_from IS NULL OR si.available_from <= NOW())
         AND (si.available_until IS NULL OR si.available_until > NOW())
         ORDER BY si.available_until ASC`
      );
      return rows;
    } catch (error) {
      console.error('Failed to get limited time items:', error);
      throw ShopError.invalidShopData('Failed to retrieve limited time items');
    }
  }

  /**
   * Search shop items
   */
  async searchItems(query: string, categoryId?: number): Promise<ShopItemWithDetails[]> {
    try {
      let sql = `SELECT
        si.*,
        i.item_name, i.description as item_description, i.item_type, i.category as item_category,
        i.is_stackable, i.max_stack_size, i.data as item_data,
        sc.name as category_name, sc.display_name as category_display_name
       FROM shop_items si
       JOIN items i ON si.item_id = i.item_id
       JOIN shop_categories sc ON si.category_id = sc.id
       WHERE si.is_available = TRUE
       AND (si.available_from IS NULL OR si.available_from <= NOW())
       AND (si.available_until IS NULL OR si.available_until > NOW())
       AND (si.name LIKE ? OR i.item_name LIKE ? OR si.description LIKE ?)`;

      const params = [`%${query}%`, `%${query}%`, `%${query}%`];

      if (categoryId) {
        sql += ' AND si.category_id = ?';
        params.push(categoryId.toString());
      }

      sql += ' ORDER BY si.sort_order';

      const [rows] = await pool.query<ShopItemWithDetails[]>(sql, params);
      return rows;
    } catch (error) {
      console.error('Failed to search shop items:', error);
      throw ShopError.invalidShopData('Failed to search shop items');
    }
  }

  /**
   * Check if user can purchase item
   */
  async canPurchaseItem(userId: string, shopItemId: number, quantity: number = 1): Promise<{
    canPurchase: boolean;
    reason?: string;
    requiredGold?: number;
    requiredDiamonds?: number;
  }> {
    try {
      const shopItem = await this.getShopItem(shopItemId);
      if (!shopItem) {
        return { canPurchase: false, reason: 'Item not found' };
      }

      // Check availability
      if (!shopItem.is_available) {
        return { canPurchase: false, reason: 'Item not available' };
      }

      // Check time restrictions
      const now = new Date();
      if (shopItem.available_from && new Date(shopItem.available_from) > now) {
        return { canPurchase: false, reason: 'Item not yet available' };
      }
      if (shopItem.available_until && new Date(shopItem.available_until) <= now) {
        return { canPurchase: false, reason: 'Item no longer available' };
      }

      // Check stock
      if (shopItem.remaining_stock !== null && shopItem.remaining_stock !== undefined && shopItem.remaining_stock < quantity) {
        return { canPurchase: false, reason: 'Insufficient stock' };
      }

      // Check purchase limits
      if (shopItem.max_purchases_per_user) {
        const [limitRows] = await pool.query<RowDataPacket[]>(
          'SELECT purchase_count FROM user_purchase_limits WHERE user_id = ? AND shop_item_id = ?',
          [userId, shopItemId]
        );
        const currentPurchases = limitRows.length > 0 ? limitRows[0].purchase_count : 0;
        if (currentPurchases + quantity > shopItem.max_purchases_per_user) {
          return { canPurchase: false, reason: 'Purchase limit exceeded' };
        }
      }

      // Check currency
      const totalGold = (shopItem.price_gold || 0) * quantity;
      const totalDiamonds = (shopItem.price_diamonds || 0) * quantity;

      if (totalGold === 0 && totalDiamonds === 0) {
        return { canPurchase: false, reason: 'Invalid price configuration' };
      }

      const hasSufficientCurrency = await CurrencyService.hasSufficientCurrency(
        userId,
        totalGold,
        totalDiamonds
      );

      if (!hasSufficientCurrency) {
        return {
          canPurchase: false,
          reason: 'Insufficient currency',
          requiredGold: totalGold,
          requiredDiamonds: totalDiamonds
        };
      }

      return { canPurchase: true };

    } catch (error) {
      console.error('Failed to check purchase eligibility:', error);
      return { canPurchase: false, reason: 'System error' };
    }
  }

  /**
   * Purchase item from shop
   */
  async purchaseItem(request: PurchaseRequest): Promise<PurchaseResult> {
    const { userId, shopItemId, quantity, giftRecipientId } = request;

    // Validate purchase eligibility
    const eligibility = await this.canPurchaseItem(userId, shopItemId, quantity);
    if (!eligibility.canPurchase) {
      throw ShopError.itemNotAvailable(shopItemId, eligibility.reason);
    }

    const shopItem = await this.getShopItem(shopItemId);
    if (!shopItem) {
      throw ShopError.itemNotFound(shopItemId);
    }

    const totalGold = (shopItem.price_gold || 0) * quantity;
    const totalDiamonds = (shopItem.price_diamonds || 0) * quantity;

    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Deduct currency from buyer
      const remainingCurrency = await CurrencyService.deductCurrency(
        userId,
        totalGold,
        totalDiamonds,
        `Purchase: ${shopItem.name} x${quantity}`
      );

      // Add items to inventory (buyer or recipient)
      const recipientId = giftRecipientId || userId;
      const inventoryResult = await InventoryService.addItem(recipientId, shopItem.item_id, quantity);

      // Update stock if limited
      if (shopItem.remaining_stock !== null) {
        await connection.query<ResultSetHeader>(
          'UPDATE shop_items SET remaining_stock = remaining_stock - ? WHERE id = ?',
          [quantity, shopItemId]
        );
      }

      // Update purchase limits
      if (shopItem.max_purchases_per_user) {
        await connection.query<ResultSetHeader>(
          `INSERT INTO user_purchase_limits (user_id, shop_item_id, purchase_count, last_purchase_at)
           VALUES (?, ?, ?, NOW())
           ON DUPLICATE KEY UPDATE
           purchase_count = purchase_count + ?,
           last_purchase_at = NOW()`,
          [userId, shopItemId, quantity, quantity]
        );
      }

      // Record transaction
      const [transactionResult] = await connection.query<ResultSetHeader>(
        `INSERT INTO purchase_history
         (user_id, shop_item_id, item_id, quantity, price_gold, price_diamonds, transaction_type, gift_recipient_id)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          userId,
          shopItemId,
          shopItem.item_id,
          quantity,
          totalGold || null,
          totalDiamonds || null,
          giftRecipientId ? 'gift_sent' : 'purchase',
          giftRecipientId || null
        ]
      );

      // If it's a gift, record for recipient too
      if (giftRecipientId) {
        await connection.query<ResultSetHeader>(
          `INSERT INTO purchase_history
           (user_id, shop_item_id, item_id, quantity, price_gold, price_diamonds, transaction_type, gift_sender_id)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            giftRecipientId,
            shopItemId,
            shopItem.item_id,
            quantity,
            0,
            0,
            'gift_received',
            userId
          ]
        );
      }

      await connection.commit();

      return {
        success: true,
        transactionId: transactionResult.insertId,
        itemsAdded: [{ itemId: shopItem.item_id, quantity }],
        currencySpent: { gold: totalGold, diamonds: totalDiamonds },
        remainingCurrency: { gold: remainingCurrency.gold_coins, diamonds: remainingCurrency.diamonds }
      };

    } catch (error) {
      await connection.rollback();
      console.error('Failed to purchase item:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.transactionFailed('Purchase transaction failed');
    } finally {
      connection.release();
    }
  }

  /**
   * Get user's purchase history
   */
  async getPurchaseHistory(userId: string, limit: number = 50): Promise<Array<{
    id: number;
    shop_item_id: number;
    item_id: string;
    item_name: string;
    quantity: number;
    price_gold?: number;
    price_diamonds?: number;
    transaction_type: string;
    gift_recipient_id?: string;
    gift_sender_id?: string;
    created_at: Date;
  }>> {
    try {
      const [rows] = await pool.query<RowDataPacket[]>(
        `SELECT
          ph.id, ph.shop_item_id, ph.item_id, ph.quantity, ph.price_gold, ph.price_diamonds,
          ph.transaction_type, ph.gift_recipient_id, ph.gift_sender_id, ph.created_at,
          i.item_name
         FROM purchase_history ph
         JOIN items i ON ph.item_id = i.item_id
         WHERE ph.user_id = ?
         ORDER BY ph.created_at DESC
         LIMIT ?`,
        [userId, limit]
      );
      return rows as any[];
    } catch (error) {
      console.error('Failed to get purchase history:', error);
      throw ShopError.invalidShopData('Failed to retrieve purchase history');
    }
  }

  /**
   * Refresh shop cache
   */
  async refreshCache(): Promise<void> {
    this.categoryCache.clear();
    this.shopItemCache.clear();
    await this.loadShopCache();
  }
}

export default ShopService.getInstance();
