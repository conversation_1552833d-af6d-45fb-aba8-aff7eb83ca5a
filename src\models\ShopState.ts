import { Schema, type, MapSchema } from '@colyseus/schema';

/**
 * Represents a user's currency state
 */
export class UserCurrencyState extends Schema {
  @type('string') userId: string = '';
  @type('number') goldCoins: number = 0;
  @type('number') diamonds: number = 0;
  @type('number') lastUpdated: number = 0;

  constructor(userId: string = '', goldCoins: number = 0, diamonds: number = 0) {
    super();
    this.userId = userId;
    this.goldCoins = goldCoins;
    this.diamonds = diamonds;
    this.lastUpdated = Date.now();
  }

  /**
   * Update currency amounts
   */
  updateCurrency(goldCoins: number, diamonds: number): void {
    this.goldCoins = goldCoins;
    this.diamonds = diamonds;
    this.lastUpdated = Date.now();
  }

  /**
   * Check if user has sufficient currency
   */
  hasSufficientCurrency(goldRequired: number = 0, diamondsRequired: number = 0): boolean {
    return this.goldCoins >= goldRequired && this.diamonds >= diamondsRequired;
  }
}

/**
 * Represents an inventory item
 */
export class InventoryItemState extends Schema {
  @type('string') itemId: string = '';
  @type('number') quantity: number = 0;
  @type('number') acquiredAt: number = 0;

  constructor(itemId: string = '', quantity: number = 0) {
    super();
    this.itemId = itemId;
    this.quantity = quantity;
    this.acquiredAt = Date.now();
  }

  /**
   * Add quantity to this item
   */
  addQuantity(amount: number): void {
    this.quantity += amount;
  }

  /**
   * Remove quantity from this item
   */
  removeQuantity(amount: number): boolean {
    if (this.quantity >= amount) {
      this.quantity -= amount;
      return true;
    }
    return false;
  }
}

/**
 * Represents a user's inventory state
 */
export class UserInventoryState extends Schema {
  @type('string') userId: string = '';
  @type({ map: InventoryItemState }) items = new MapSchema<InventoryItemState>();
  @type('number') lastUpdated: number = 0;

  constructor(userId: string = '') {
    super();
    this.userId = userId;
    this.lastUpdated = Date.now();
  }

  /**
   * Add item to inventory
   */
  addItem(itemId: string, quantity: number): void {
    const existingItem = this.items.get(itemId);
    if (existingItem) {
      existingItem.addQuantity(quantity);
    } else {
      this.items.set(itemId, new InventoryItemState(itemId, quantity));
    }
    this.lastUpdated = Date.now();
  }

  /**
   * Remove item from inventory
   */
  removeItem(itemId: string, quantity: number): boolean {
    const existingItem = this.items.get(itemId);
    if (existingItem && existingItem.removeQuantity(quantity)) {
      if (existingItem.quantity === 0) {
        this.items.delete(itemId);
      }
      this.lastUpdated = Date.now();
      return true;
    }
    return false;
  }

  /**
   * Get item quantity
   */
  getItemQuantity(itemId: string): number {
    const item = this.items.get(itemId);
    return item ? item.quantity : 0;
  }

  /**
   * Check if user has item
   */
  hasItem(itemId: string, requiredQuantity: number = 1): boolean {
    return this.getItemQuantity(itemId) >= requiredQuantity;
  }

  /**
   * Get all items as array
   */
  getItemsArray(): InventoryItemState[] {
    return Array.from(this.items.values());
  }

  /**
   * Get total number of unique items
   */
  getUniqueItemCount(): number {
    return this.items.size;
  }

  /**
   * Get total quantity of all items
   */
  getTotalItemCount(): number {
    let total = 0;
    this.items.forEach(item => {
      total += item.quantity;
    });
    return total;
  }
}

/**
 * Represents a shop item state
 */
export class ShopItemState extends Schema {
  @type('number') shopItemId: number = 0;
  @type('string') itemId: string = '';
  @type('string') name: string = '';
  @type('string') description: string = '';
  @type('number') priceGold: number = 0;
  @type('number') priceDiamonds: number = 0;
  @type('boolean') isAvailable: boolean = true;
  @type('boolean') isFeatured: boolean = false;
  @type('boolean') isLimitedTime: boolean = false;
  @type('number') availableUntil: number = 0; // Unix timestamp
  @type('number') remainingStock: number = -1; // -1 means unlimited
  @type('string') tags: string = '[]'; // JSON string
  @type('string') metadata: string = '{}'; // JSON string

  constructor(
    shopItemId: number = 0,
    itemId: string = '',
    name: string = '',
    priceGold: number = 0,
    priceDiamonds: number = 0
  ) {
    super();
    this.shopItemId = shopItemId;
    this.itemId = itemId;
    this.name = name;
    this.priceGold = priceGold;
    this.priceDiamonds = priceDiamonds;
  }

  /**
   * Get tags as parsed array
   */
  getTagsArray(): string[] {
    try {
      return JSON.parse(this.tags);
    } catch (error) {
      return [];
    }
  }

  /**
   * Get metadata as parsed object
   */
  getMetadata(): any {
    try {
      return JSON.parse(this.metadata);
    } catch (error) {
      return {};
    }
  }

  /**
   * Check if item is currently available
   */
  isCurrentlyAvailable(): boolean {
    if (!this.isAvailable) return false;
    if (this.isLimitedTime && this.availableUntil > 0) {
      return Date.now() < this.availableUntil;
    }
    if (this.remainingStock >= 0) {
      return this.remainingStock > 0;
    }
    return true;
  }

  /**
   * Check if user can afford this item
   */
  canAfford(userCurrency: UserCurrencyState, quantity: number = 1): boolean {
    const totalGold = this.priceGold * quantity;
    const totalDiamonds = this.priceDiamonds * quantity;
    return userCurrency.hasSufficientCurrency(totalGold, totalDiamonds);
  }
}

/**
 * Represents a shop category state
 */
export class ShopCategoryState extends Schema {
  @type('number') categoryId: number = 0;
  @type('string') name: string = '';
  @type('string') displayName: string = '';
  @type('string') description: string = '';
  @type('string') icon: string = '';
  @type('number') sortOrder: number = 0;
  @type('boolean') isActive: boolean = true;
  @type({ map: ShopItemState }) items = new MapSchema<ShopItemState>();

  constructor(categoryId: number = 0, name: string = '', displayName: string = '') {
    super();
    this.categoryId = categoryId;
    this.name = name;
    this.displayName = displayName;
  }

  /**
   * Add item to category
   */
  addItem(item: ShopItemState): void {
    this.items.set(item.shopItemId.toString(), item);
  }

  /**
   * Remove item from category
   */
  removeItem(shopItemId: number): void {
    this.items.delete(shopItemId.toString());
  }

  /**
   * Get available items
   */
  getAvailableItems(): ShopItemState[] {
    const availableItems: ShopItemState[] = [];
    this.items.forEach(item => {
      if (item.isCurrentlyAvailable()) {
        availableItems.push(item);
      }
    });
    return availableItems;
  }

  /**
   * Get featured items
   */
  getFeaturedItems(): ShopItemState[] {
    const featuredItems: ShopItemState[] = [];
    this.items.forEach(item => {
      if (item.isFeatured && item.isCurrentlyAvailable()) {
        featuredItems.push(item);
      }
    });
    return featuredItems;
  }
}

/**
 * Represents the shop system state for a room
 */
export class ShopSystemState extends Schema {
  @type({ map: ShopCategoryState }) categories = new MapSchema<ShopCategoryState>();
  @type({ map: UserCurrencyState }) userCurrencies = new MapSchema<UserCurrencyState>();
  @type({ map: UserInventoryState }) userInventories = new MapSchema<UserInventoryState>();
  @type('number') lastUpdated: number = 0;

  constructor() {
    super();
    this.lastUpdated = Date.now();
  }

  /**
   * Add or update category
   */
  setCategory(category: ShopCategoryState): void {
    this.categories.set(category.categoryId.toString(), category);
    this.lastUpdated = Date.now();
  }

  /**
   * Get category by ID
   */
  getCategory(categoryId: number): ShopCategoryState | undefined {
    return this.categories.get(categoryId.toString());
  }

  /**
   * Get user currency
   */
  getUserCurrency(userId: string): UserCurrencyState | undefined {
    return this.userCurrencies.get(userId);
  }

  /**
   * Set user currency
   */
  setUserCurrency(currency: UserCurrencyState): void {
    this.userCurrencies.set(currency.userId, currency);
    this.lastUpdated = Date.now();
  }

  /**
   * Get user inventory
   */
  getUserInventory(userId: string): UserInventoryState | undefined {
    return this.userInventories.get(userId);
  }

  /**
   * Set user inventory
   */
  setUserInventory(inventory: UserInventoryState): void {
    this.userInventories.set(inventory.userId, inventory);
    this.lastUpdated = Date.now();
  }

  /**
   * Get all featured items across all categories
   */
  getAllFeaturedItems(): ShopItemState[] {
    const featuredItems: ShopItemState[] = [];
    this.categories.forEach(category => {
      featuredItems.push(...category.getFeaturedItems());
    });
    return featuredItems;
  }

  /**
   * Get all limited time items
   */
  getAllLimitedTimeItems(): ShopItemState[] {
    const limitedItems: ShopItemState[] = [];
    this.categories.forEach(category => {
      category.items.forEach(item => {
        if (item.isLimitedTime && item.isCurrentlyAvailable()) {
          limitedItems.push(item);
        }
      });
    });
    return limitedItems;
  }

  /**
   * Search items by name or tags
   */
  searchItems(query: string): ShopItemState[] {
    const results: ShopItemState[] = [];
    const searchTerm = query.toLowerCase();

    this.categories.forEach(category => {
      category.items.forEach(item => {
        if (item.isCurrentlyAvailable()) {
          // Search in name and description
          if (item.name.toLowerCase().includes(searchTerm) ||
              item.description.toLowerCase().includes(searchTerm)) {
            results.push(item);
            return;
          }

          // Search in tags
          const tags = item.getTagsArray();
          if (tags.some(tag => tag.toLowerCase().includes(searchTerm))) {
            results.push(item);
          }
        }
      });
    });

    return results;
  }
}
