# 数据库表结构总览

## 表统计

**总计：21 张表**

- 核心系统：6 张表
- 家园系统：2 张表  
- 任务系统：4 张表
- 商店系统：9 张表

## 表列表

### 核心系统表 (6 张)

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `users` | 用户账户表 | id, username, password_hash |
| `user_auth_tokens` | 用户认证令牌表 | uid, token, expires_at |
| `friend_relationships` | 好友关系表 | user_one_id, user_two_id, status |
| `characters` | 角色信息表 | character_id, user_id, character_name, level |
| `items` | 物品模板表 | item_id, item_name, item_type, category |
| `user_private_spaces` | 用户私人空间设置表 | owner_user_id, home_access_level, garden_access_level |

### 家园系统表 (2 张)

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `home_items` | 家具实例表 | item_instance_id, owner_user_id, item_template_id, pos_x, pos_y |
| `garden_plots` | 花园地块表 | plot_id, owner_user_id, seed_id, growth_stage |

### 任务系统表 (4 张)

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `quests` | 任务模板表 | id, title, accept_message_0, reward_message_0_0 |
| `quest_steps` | 任务步骤表 | id, quest_id, step_order, goal |
| `quest_step_responses` | NPC 响应表 | id, step_id, npc_id, response_type, message_0 |
| `player_quests` | 玩家任务进度表 | player_id, quest_id, current_step_order, is_completed |

### 商店系统表 (9 张)

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `user_currencies` | 用户货币表 | user_id, gold_coins, diamonds |
| `shop_categories` | 商店分类表 | id, name, display_name, parent_category_id |
| `shop_items` | 商店商品表 | id, item_id, category_id, price_gold, price_diamonds |
| `user_inventory` | 用户库存表 | id, user_id, item_id, quantity |
| `purchase_history` | 购买历史表 | id, user_id, shop_item_id, transaction_type |
| `user_purchase_limits` | 用户购买限制表 | user_id, shop_item_id, purchase_count |
| `promotions` | 促销活动表 | id, name, type, discount_percentage |
| `user_promotion_usage` | 用户促销使用记录表 | user_id, promotion_id, usage_count |
| `wishlist` | 用户心愿单表 | id, user_id, shop_item_id |

## 关键关系

### 用户中心关系
- `users` (1) ←→ (N) `user_auth_tokens`
- `users` (1) ←→ (N) `characters`
- `users` (1) ←→ (1) `user_currencies`
- `users` (1) ←→ (N) `user_inventory`

### 物品系统关系
- `items` (1) ←→ (N) `shop_items`
- `items` (1) ←→ (N) `user_inventory`
- `items` (1) ←→ (N) `home_items`
- `items` (1) ←→ (N) `garden_plots`

### 任务系统关系
- `quests` (1) ←→ (N) `quest_steps`
- `quest_steps` (1) ←→ (N) `quest_step_responses`
- `users` (1) ←→ (N) `player_quests`

### 商店系统关系
- `shop_categories` (1) ←→ (N) `shop_items`
- `shop_items` (1) ←→ (N) `purchase_history`
- `shop_items` (1) ←→ (N) `wishlist`

## 数据类型说明

### 主键类型
- **BIGINT AUTO_INCREMENT**: 用户相关表 (users, characters, purchase_history 等)
- **INT AUTO_INCREMENT**: 系统配置表 (quests, shop_categories, shop_items 等)
- **VARCHAR**: 字符串标识符 (item_id, plot_id 等)
- **复合主键**: 关系表 (friend_relationships, player_quests 等)

### 常用字段类型
- **VARCHAR(255)**: 名称、标题、描述
- **TEXT**: 长文本内容
- **JSON**: 复杂数据结构
- **ENUM**: 状态和类型字段
- **TIMESTAMP**: 时间戳字段
- **BIGINT UNSIGNED**: 货币和数量字段
- **BOOLEAN**: 开关状态字段

### 索引策略
- **UNIQUE**: 用户名、令牌、用户-物品组合
- **INDEX**: 外键、状态查询、时间排序
- **复合索引**: 多字段查询优化

## 约束说明

### 外键约束
- **ON DELETE CASCADE**: 用户删除时级联删除相关数据
- **ON DELETE RESTRICT**: 防止删除被引用的模板数据
- **ON DELETE SET NULL**: 可选引用，删除时设为空

### 数据完整性
- **NOT NULL**: 必填字段
- **DEFAULT**: 默认值设置
- **UNSIGNED**: 防止负数
- **AUTO_INCREMENT**: 自动递增主键

## 性能考虑

### 查询优化
- 用户相关查询：通过 user_id 索引
- 物品查询：通过 item_id 和分类索引
- 时间范围查询：通过时间戳索引
- 状态筛选：通过状态字段索引

### 存储优化
- JSON 字段存储复杂数据结构
- 枚举类型限制状态值
- 适当的字段长度设置
- 合理的数据类型选择

## 扩展性设计

### 预留扩展
- JSON 字段支持灵活数据结构
- 分类表支持层级结构
- 促销系统支持多种类型
- 元数据字段支持自定义配置

### 系统集成
- 统一的用户体系
- 一致的物品模板系统
- 标准化的时间戳字段
- 规范化的外键关系
