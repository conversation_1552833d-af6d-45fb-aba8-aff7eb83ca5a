import pool from '../utils/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';
import { ShopError } from '../errors';
import PlantingService, { PlantedFlower, FlowerSeed, GardenPlot, PlantingOperationResult } from './PlantingService';
import InventoryService from './InventoryService';

/**
 * Harvest Result Interface
 */
export interface HarvestResult {
  flowers: number;
  materials: any[];
  rare_items: any[];
  total_value: number;
}

/**
 * PlantingOperationsService - Handles planting, watering, fertilizing, and harvesting
 */
class PlantingOperationsService {

  /**
   * Plant a seed in a garden plot
   */
  async plantSeed(userId: string, seedId: string, plotIndex: number): Promise<PlantingOperationResult> {
    try {
      const userIdNum = parseInt(userId);

      // Check if user has the seed in inventory
      const seedQuantity = await InventoryService.getItemQuantity(userId, seedId);
      if (seedQuantity <= 0) {
        return {
          success: false,
          message: 'You do not have this seed in your inventory'
        };
      }

      // Get seed template
      const [seedTemplates] = await pool.query<FlowerSeed[]>(
        'SELECT * FROM flower_seeds WHERE seed_id = ? AND is_available = TRUE',
        [seedId]
      );

      if (seedTemplates.length === 0) {
        return {
          success: false,
          message: 'Seed not found or not available'
        };
      }

      const seedTemplate = seedTemplates[0];

      // Check user level requirement
      const userLevel = await PlantingService.getUserPlantingLevel(userId);
      if (userLevel.current_level < seedTemplate.required_level) {
        return {
          success: false,
          message: `Requires planting level ${seedTemplate.required_level}`
        };
      }

      // Get garden plot
      const [plots] = await pool.query<GardenPlot[]>(
        'SELECT * FROM garden_plots WHERE user_id = ? AND plot_index = ?',
        [userIdNum, plotIndex]
      );

      if (plots.length === 0) {
        return {
          success: false,
          message: 'Garden plot not found'
        };
      }

      const plot = plots[0];

      if (!plot.is_unlocked) {
        return {
          success: false,
          message: 'Garden plot is not unlocked'
        };
      }

      // Check if plot is already occupied
      const [existingPlants] = await pool.query<PlantedFlower[]>(
        'SELECT * FROM planted_flowers WHERE plot_id = ? AND plant_stage != "withered"',
        [plot.id]
      );

      if (existingPlants.length > 0) {
        return {
          success: false,
          message: 'Garden plot is already occupied'
        };
      }

      // Calculate harvest time
      const plantedAt = new Date();
      const harvestTime = new Date(plantedAt.getTime() + (seedTemplate.growth_time_minutes * 60 * 1000));

      // Remove seed from inventory
      await InventoryService.removeItem(userId, seedId, 1, 'use', 'planting_system');

      // Plant the seed
      const [result] = await pool.query<ResultSetHeader>(
        `INSERT INTO planted_flowers
         (user_id, plot_id, seed_id, plant_stage, planted_at, expected_harvest_time, needs_water)
         VALUES (?, ?, ?, 'planted', ?, ?, TRUE)`,
        [userIdNum, plot.id, seedId, plantedAt, harvestTime]
      );

      // Update user stats
      await pool.query<ResultSetHeader>(
        `UPDATE user_planting_levels
         SET total_plants = total_plants + 1, updated_at = CURRENT_TIMESTAMP
         WHERE user_id = ?`,
        [userIdNum]
      );

      // Add experience
      const expGain = PlantingService.calculateExpGain('plant', seedTemplate.rarity);
      const levelResult = await PlantingService.addExperience(userId, expGain, 'plant');

      // Log operation
      await PlantingService.logPlantingOperation(userId, 'plant', plot.id, result.insertId, seedId, expGain);

      return {
        success: true,
        message: `${seedTemplate.seed_name} planted successfully`,
        data: {
          planted_flower_id: result.insertId,
          expected_harvest_time: harvestTime
        },
        exp_gained: expGain,
        level_up: levelResult.levelUp,
        new_level: levelResult.newLevel
      };

    } catch (error: any) {
      console.error('Failed to plant seed:', error);
      throw ShopError.inventoryOperationFailed('plant_seed', error.message);
    }
  }

  /**
   * Water a planted flower
   */
  async waterFlower(userId: string, plantedFlowerId: number): Promise<PlantingOperationResult> {
    try {
      const userIdNum = parseInt(userId);

      // Get planted flower
      const [flowers] = await pool.query<PlantedFlower[]>(
        `SELECT pf.*, fs.rarity as seed_rarity, fs.water_intervals, fs.growth_time_minutes
         FROM planted_flowers pf
         JOIN flower_seeds fs ON pf.seed_id = fs.seed_id
         WHERE pf.id = ? AND pf.user_id = ?`,
        [plantedFlowerId, userIdNum]
      );

      if (flowers.length === 0) {
        return {
          success: false,
          message: 'Planted flower not found'
        };
      }

      const flower = flowers[0];

      if (flower.plant_stage === 'mature' || flower.plant_stage === 'withered') {
        return {
          success: false,
          message: 'Flower is already mature or withered'
        };
      }

      if (!flower.needs_water) {
        return {
          success: false,
          message: 'Flower does not need watering right now'
        };
      }

      // Update flower state
      const newWaterCount = flower.water_count + 1;
      const needsWater = newWaterCount < flower.water_intervals;

      // Calculate growth boost (watering on time gives small boost)
      const timeSinceLastWater = flower.last_watered ?
        (Date.now() - new Date(flower.last_watered).getTime()) / (1000 * 60) : 0;
      const growthBoost = timeSinceLastWater < 60 ? 1.1 : 1.0; // 10% boost if watered within 1 hour

      await pool.query<ResultSetHeader>(
        `UPDATE planted_flowers
         SET last_watered = CURRENT_TIMESTAMP, water_count = ?, needs_water = ?,
             growth_boost = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [newWaterCount, needsWater, growthBoost, plantedFlowerId]
      );

      // Add experience
      const expGain = PlantingService.calculateExpGain('water', flower.seed_rarity);
      const levelResult = await PlantingService.addExperience(userId, expGain, 'water');

      // Log operation
      await PlantingService.logPlantingOperation(userId, 'water', flower.plot_id, plantedFlowerId, flower.seed_id, expGain);

      return {
        success: true,
        message: 'Flower watered successfully',
        data: {
          water_count: newWaterCount,
          needs_water: needsWater,
          growth_boost: growthBoost
        },
        exp_gained: expGain,
        level_up: levelResult.levelUp,
        new_level: levelResult.newLevel
      };

    } catch (error: any) {
      console.error('Failed to water flower:', error);
      throw ShopError.inventoryOperationFailed('water_flower', error.message);
    }
  }

  /**
   * Apply fertilizer to a planted flower
   */
  async fertilizeFlower(userId: string, plantedFlowerId: number, fertilizerId: string): Promise<PlantingOperationResult> {
    try {
      const userIdNum = parseInt(userId);

      // Check if user has fertilizer in inventory
      const fertilizerQuantity = await InventoryService.getItemQuantity(userId, fertilizerId);
      if (fertilizerQuantity <= 0) {
        return {
          success: false,
          message: 'You do not have this fertilizer in your inventory'
        };
      }

      // Get planted flower
      const [flowers] = await pool.query<PlantedFlower[]>(
        `SELECT pf.*, fs.rarity as seed_rarity, fs.fertilizer_boost
         FROM planted_flowers pf
         JOIN flower_seeds fs ON pf.seed_id = fs.seed_id
         WHERE pf.id = ? AND pf.user_id = ?`,
        [plantedFlowerId, userIdNum]
      );

      if (flowers.length === 0) {
        return {
          success: false,
          message: 'Planted flower not found'
        };
      }

      const flower = flowers[0];

      if (flower.plant_stage === 'mature' || flower.plant_stage === 'withered') {
        return {
          success: false,
          message: 'Flower is already mature or withered'
        };
      }

      // Remove fertilizer from inventory
      await InventoryService.removeItem(userId, fertilizerId, 1, 'use', 'planting_system');

      // Apply fertilizer effects
      const newFertilizerCount = flower.fertilizer_count + 1;

      // Safely parse numeric values with fallbacks
      let currentYieldBoost = 1.0;
      let currentGrowthBoost = 1.0;
      let fertilizerBoostValue = 1.2;

      try {
        currentYieldBoost = parseFloat(flower.yield_boost?.toString() || '1.0') || 1.0;
        currentGrowthBoost = parseFloat(flower.growth_boost?.toString() || '1.0') || 1.0;
        fertilizerBoostValue = parseFloat(flower.fertilizer_boost?.toString() || '1.2') || 1.2;
      } catch (error) {
        console.warn('Error parsing fertilizer values, using defaults:', error);
      }

      const yieldBoost = Math.min(currentYieldBoost + (fertilizerBoostValue - 1.0), 3.0); // Max 3x yield
      const growthBoost = Math.min(currentGrowthBoost + 0.2, 2.0); // Max 2x growth speed

      // Validate final values
      if (isNaN(yieldBoost) || isNaN(growthBoost)) {
        console.error('Invalid boost values calculated:', { yieldBoost, growthBoost, flower });
        return {
          success: false,
          message: 'Failed to calculate fertilizer effects'
        };
      }

      // Reduce harvest time by 10%
      const currentTime = new Date();
      const originalHarvestTime = new Date(flower.expected_harvest_time);
      const timeReduction = (originalHarvestTime.getTime() - currentTime.getTime()) * 0.1;
      const newHarvestTime = new Date(originalHarvestTime.getTime() - timeReduction);

      await pool.query<ResultSetHeader>(
        `UPDATE planted_flowers
         SET last_fertilized = CURRENT_TIMESTAMP, fertilizer_count = ?,
             yield_boost = ?, growth_boost = ?, expected_harvest_time = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [newFertilizerCount, yieldBoost, growthBoost, newHarvestTime, plantedFlowerId]
      );

      // Add experience
      const expGain = PlantingService.calculateExpGain('fertilize', flower.seed_rarity);
      const levelResult = await PlantingService.addExperience(userId, expGain, 'fertilize');

      // Log operation
      await PlantingService.logPlantingOperation(userId, 'fertilize', flower.plot_id, plantedFlowerId, flower.seed_id, expGain);

      return {
        success: true,
        message: 'Fertilizer applied successfully',
        data: {
          fertilizer_count: newFertilizerCount,
          yield_boost: yieldBoost,
          growth_boost: growthBoost,
          new_harvest_time: newHarvestTime
        },
        exp_gained: expGain,
        level_up: levelResult.levelUp,
        new_level: levelResult.newLevel
      };

    } catch (error: any) {
      console.error('Failed to fertilize flower:', error);
      throw ShopError.inventoryOperationFailed('fertilize_flower', error.message);
    }
  }

  /**
   * Harvest a mature flower
   */
  async harvestFlower(userId: string, plantedFlowerId: number): Promise<PlantingOperationResult> {
    try {
      const userIdNum = parseInt(userId);

      // Get planted flower with seed details
      const [flowers] = await pool.query<PlantedFlower[]>(
        `SELECT pf.*, fs.rarity as seed_rarity, fs.base_yield, fs.max_yield, fs.rare_drop_chance, fs.seed_name
         FROM planted_flowers pf
         JOIN flower_seeds fs ON pf.seed_id = fs.seed_id
         WHERE pf.id = ? AND pf.user_id = ?`,
        [plantedFlowerId, userIdNum]
      );

      if (flowers.length === 0) {
        return {
          success: false,
          message: 'Planted flower not found'
        };
      }

      const flower = flowers[0];

      if (!flower.is_ready_for_harvest && new Date() < new Date(flower.expected_harvest_time)) {
        return {
          success: false,
          message: 'Flower is not ready for harvest yet'
        };
      }

      if (flower.plant_stage === 'withered') {
        return {
          success: false,
          message: 'Flower has withered and cannot be harvested'
        };
      }

      // Calculate harvest results
      const harvestResult = await this.calculateHarvestRewards(flower);

      // Add items to inventory
      const itemsReceived = [];

      // Add flowers (main product)
      if (harvestResult.flowers > 0) {
        const flowerItemId = `flower_${flower.seed_id}`;
        await InventoryService.addItem(userId, flowerItemId, harvestResult.flowers, 'harvest', 'planting_system');
        itemsReceived.push({ item_id: flowerItemId, quantity: harvestResult.flowers, type: 'flower' });
      }

      // Add materials
      for (const material of harvestResult.materials) {
        await InventoryService.addItem(userId, material.item_id, material.quantity, 'harvest', 'planting_system');
        itemsReceived.push(material);
      }

      // Add rare items
      for (const rareItem of harvestResult.rare_items) {
        await InventoryService.addItem(userId, rareItem.item_id, rareItem.quantity, 'harvest', 'planting_system');
        itemsReceived.push(rareItem);
      }

      // Remove the planted flower
      await pool.query<ResultSetHeader>(
        'DELETE FROM planted_flowers WHERE id = ?',
        [plantedFlowerId]
      );

      // Update user stats
      const isRareHarvest = harvestResult.rare_items.length > 0;
      await pool.query<ResultSetHeader>(
        `UPDATE user_planting_levels
         SET total_harvests = total_harvests + 1,
             rare_harvests = rare_harvests + ?,
             updated_at = CURRENT_TIMESTAMP
         WHERE user_id = ?`,
        [isRareHarvest ? 1 : 0, userIdNum]
      );

      // Add experience
      const expGain = PlantingService.calculateExpGain('harvest', flower.seed_rarity);
      const levelResult = await PlantingService.addExperience(userId, expGain, 'harvest');

      // Log operation
      await PlantingService.logPlantingOperation(userId, 'harvest', flower.plot_id, plantedFlowerId, flower.seed_id, expGain, itemsReceived);

      return {
        success: true,
        message: `${flower.seed_name} harvested successfully`,
        data: harvestResult,
        items_received: itemsReceived,
        exp_gained: expGain,
        level_up: levelResult.levelUp,
        new_level: levelResult.newLevel
      };

    } catch (error: any) {
      console.error('Failed to harvest flower:', error);
      throw ShopError.inventoryOperationFailed('harvest_flower', error.message);
    }
  }

  /**
   * Calculate harvest rewards based on flower state
   */
  private async calculateHarvestRewards(flower: PlantedFlower): Promise<HarvestResult> {
    const baseYield = flower.base_yield || 1;
    const maxYield = flower.max_yield || 3;
    const yieldBoost = flower.yield_boost || 1.0;
    const rareDropChance = flower.rare_drop_chance || 0.0;

    // Calculate flower yield
    const boostedYield = Math.floor(baseYield * yieldBoost);
    const finalYield = Math.min(boostedYield, maxYield);

    const result: HarvestResult = {
      flowers: finalYield,
      materials: [],
      rare_items: [],
      total_value: finalYield * 10 // Base value per flower
    };

    // Random material drops
    if (Math.random() < 0.3) { // 30% chance for materials
      result.materials.push({
        item_id: 'plant_fiber',
        quantity: Math.floor(Math.random() * 3) + 1,
        type: 'material'
      });
    }

    if (Math.random() < 0.15) { // 15% chance for seeds
      result.materials.push({
        item_id: 'generic_seed',
        quantity: 1,
        type: 'seed'
      });
    }

    // Rare drops
    if (Math.random() < rareDropChance) {
      const rareItems = ['rare_essence', 'magic_pollen', 'golden_nectar'];
      const rareItem = rareItems[Math.floor(Math.random() * rareItems.length)];
      result.rare_items.push({
        item_id: rareItem,
        quantity: 1,
        type: 'rare_material'
      });
      result.total_value += 100; // Rare items are valuable
    }

    return result;
  }

  /**
   * Update flower growth stages and status
   */
  async updateFlowerStates(userId?: string): Promise<void> {
    try {
      let query = `
        SELECT pf.*, fs.water_intervals, fs.growth_time_minutes
        FROM planted_flowers pf
        JOIN flower_seeds fs ON pf.seed_id = fs.seed_id
        WHERE pf.plant_stage != 'mature' AND pf.plant_stage != 'withered'
      `;
      const params: any[] = [];

      if (userId) {
        query += ' AND pf.user_id = ?';
        params.push(parseInt(userId));
      }

      const [flowers] = await pool.query<PlantedFlower[]>(query, params);

      for (const flower of flowers) {
        await this.updateSingleFlowerState(flower);
      }
    } catch (error) {
      console.error('Failed to update flower states:', error);
    }
  }

  /**
   * Update a single flower's state
   */
  private async updateSingleFlowerState(flower: PlantedFlower): Promise<void> {
    try {
      const now = new Date();
      const plantedAt = new Date(flower.planted_at);
      const expectedHarvest = new Date(flower.expected_harvest_time);
      const growthProgress = (now.getTime() - plantedAt.getTime()) / (expectedHarvest.getTime() - plantedAt.getTime());

      let newStage = flower.plant_stage;
      let needsWater = flower.needs_water;
      let isReady = false;

      // Determine growth stage
      if (growthProgress >= 1.0) {
        newStage = 'mature';
        isReady = true;
        needsWater = false;
      } else if (growthProgress >= 0.75) {
        newStage = 'blooming';
      } else if (growthProgress >= 0.5) {
        newStage = 'growing';
      } else if (growthProgress >= 0.25) {
        newStage = 'sprouting';
      }

      // Check if needs water (every 25% of growth cycle)
      const waterInterval = flower.water_intervals || 2;
      const waterCheckpoints = waterInterval;
      const currentCheckpoint = Math.floor(growthProgress * waterCheckpoints);

      if (currentCheckpoint > flower.water_count && newStage !== 'mature') {
        needsWater = true;
      }

      // Update database if anything changed
      if (newStage !== flower.plant_stage || needsWater !== flower.needs_water || isReady !== flower.is_ready_for_harvest) {
        await pool.query<ResultSetHeader>(
          `UPDATE planted_flowers
           SET plant_stage = ?, needs_water = ?, is_ready_for_harvest = ?, updated_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [newStage, needsWater, isReady, flower.id]
        );
      }
    } catch (error) {
      console.error('Failed to update single flower state:', error);
    }
  }

  /**
   * Clear a withered or harvested plot
   */
  async clearPlot(userId: string, plotIndex: number): Promise<PlantingOperationResult> {
    try {
      const userIdNum = parseInt(userId);

      // Get garden plot
      const [plots] = await pool.query<GardenPlot[]>(
        'SELECT * FROM garden_plots WHERE user_id = ? AND plot_index = ?',
        [userIdNum, plotIndex]
      );

      if (plots.length === 0) {
        return {
          success: false,
          message: 'Garden plot not found'
        };
      }

      const plot = plots[0];

      // Remove any withered plants
      await pool.query<ResultSetHeader>(
        'DELETE FROM planted_flowers WHERE plot_id = ? AND plant_stage = "withered"',
        [plot.id]
      );

      // Log operation
      await PlantingService.logPlantingOperation(userId, 'clear', plot.id);

      return {
        success: true,
        message: 'Plot cleared successfully'
      };

    } catch (error: any) {
      console.error('Failed to clear plot:', error);
      throw ShopError.inventoryOperationFailed('clear_plot', error.message);
    }
  }
}

export default new PlantingOperationsService();
