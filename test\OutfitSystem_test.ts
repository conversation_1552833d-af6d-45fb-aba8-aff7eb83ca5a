import { expect } from 'chai';
import OutfitService from '../src/services/OutfitService';
import { OutfitState, OutfitItemState } from '../src/models/OutfitState';

describe('Outfit System Tests', () => {
  const testUserId = '2'; // Use existing test user

  describe('Database Tables', () => {
    it('should have outfit_templates table', async () => {
      try {
        const templates = await OutfitService.getOutfitTemplates();
        expect(templates).to.be.an('array');
        console.log(`✅ outfit_templates table exists with ${templates.length} templates`);
      } catch (error: any) {
        console.log('❌ outfit_templates table missing or has issues:', error.message);
        expect.fail('outfit_templates table is not accessible');
      }
    });

    it('should have user_outfits table', async () => {
      try {
        const outfits = await OutfitService.getUserOutfits(testUserId);
        expect(outfits).to.be.an('array');
        console.log(`✅ user_outfits table exists with ${outfits.length} user outfits`);
      } catch (error: any) {
        console.log('❌ user_outfits table missing or has issues:', error.message);
        expect.fail('user_outfits table is not accessible');
      }
    });
  });

  describe('OutfitState Schema', () => {
    it('should create OutfitState with default items', () => {
      const outfit = new OutfitState('test_outfit', 'Test Outfit');

      expect(outfit.outfitId).to.equal('test_outfit');
      expect(outfit.outfitName).to.equal('Test Outfit');
      expect(outfit.items).to.exist;

      // Check default items
      expect(outfit.hasItem('body')).to.be.true;
      expect(outfit.hasItem('hair')).to.be.true;
      expect(outfit.hasItem('top')).to.be.true;
      expect(outfit.hasItem('bottom')).to.be.true;
      expect(outfit.hasItem('shoes')).to.be.true;

      console.log('✅ OutfitState creates with default items');
    });

    it('should equip and unequip items', () => {
      const outfit = new OutfitState('test_outfit', 'Test Outfit');

      // Equip a new hat
      outfit.equipItem('hat', 'hat_baseball', {
        color: '#FF0000',
        layer: 25
      });

      expect(outfit.hasItem('hat')).to.be.true;
      const hat = outfit.getItem('hat');
      expect(hat?.itemTemplateId).to.equal('hat_baseball');
      expect(hat?.color).to.equal('#FF0000');
      expect(hat?.layer).to.equal(25);

      // Unequip the hat
      const unequipped = outfit.unequipItem('hat');
      expect(unequipped).to.be.true;
      expect(outfit.hasItem('hat')).to.be.false;

      console.log('✅ OutfitState equip/unequip functionality works');
    });

    it('should convert to/from plain object', () => {
      const outfit = new OutfitState('test_outfit', 'Test Outfit');
      outfit.equipItem('hat', 'hat_baseball', { color: '#FF0000' });

      // Convert to plain object
      const plainObject = outfit.toPlainObject();
      expect(plainObject).to.have.property('outfitId', 'test_outfit');
      expect(plainObject).to.have.property('outfitName', 'Test Outfit');
      expect(plainObject).to.have.property('items');
      expect(plainObject.items).to.have.property('hat');

      // Create new outfit from plain object
      const newOutfit = new OutfitState();
      newOutfit.fromPlainObject(plainObject);

      expect(newOutfit.outfitId).to.equal('test_outfit');
      expect(newOutfit.outfitName).to.equal('Test Outfit');
      expect(newOutfit.hasItem('hat')).to.be.true;
      expect(newOutfit.getItem('hat')?.itemTemplateId).to.equal('hat_baseball');

      console.log('✅ OutfitState serialization works');
    });
  });

  describe('OutfitService', () => {
    it('should get default outfit data', () => {
      const defaultOutfit = OutfitService.getDefaultOutfitData();

      expect(defaultOutfit).to.be.an('object');
      expect(defaultOutfit).to.have.property('items');
      expect(defaultOutfit.items).to.have.property('body');
      expect(defaultOutfit.items).to.have.property('hair');

      console.log('✅ OutfitService provides default outfit data');
    });

    it('should validate outfit data', () => {
      const validOutfit = {
        outfitId: 'test',
        outfitName: 'Test',
        items: {
          body: {
            itemId: 'body_1',
            itemTemplateId: 'body_default',
            category: 'body',
            color: '#FFDBAC',
            layer: 0
          }
        }
      };

      const validation = OutfitService.validateOutfitData(validOutfit);
      expect(validation.isValid).to.be.true;
      expect(validation.errors).to.be.an('array').that.is.empty;

      console.log('✅ OutfitService validation works');
    });

    it('should load user current outfit', async () => {
      try {
        const outfit = await OutfitService.loadUserCurrentOutfit(testUserId);

        expect(outfit).to.be.an('object');
        expect(outfit).to.have.property('items');

        console.log('✅ OutfitService can load user current outfit');
      } catch (error: any) {
        console.log('⚠️  OutfitService load current outfit failed:', error.message);
        // This might fail if no current outfit exists, which is okay
      }
    });

    it('should save user current outfit', async () => {
      try {
        const testOutfit = OutfitService.getDefaultOutfitData();
        testOutfit.outfitName = 'Test Current Outfit';

        const success = await OutfitService.saveUserCurrentOutfit(testUserId, testOutfit, 'Test Current Outfit');

        expect(success).to.be.true;
        console.log('✅ OutfitService can save user current outfit');
      } catch (error: any) {
        console.log('❌ OutfitService save current outfit failed:', error.message);
        expect.fail('Failed to save current outfit');
      }
    });

    it('should save named outfit', async () => {
      try {
        const testOutfit = OutfitService.getDefaultOutfitData();
        const uniqueOutfitName = `Test Named Outfit ${Date.now()}`;

        const result = await OutfitService.saveUserOutfit(testUserId, uniqueOutfitName, testOutfit, false);

        if (!result.success) {
          console.log('❌ Save outfit failed:', result.message);
        }

        expect(result.success).to.be.true;
        expect(result.outfitId).to.be.a('string');

        console.log('✅ OutfitService can save named outfit');
      } catch (error: any) {
        console.log('❌ OutfitService save named outfit failed:', error.message);
        expect.fail('Failed to save named outfit');
      }
    });

    it('should get user outfits', async () => {
      try {
        const outfits = await OutfitService.getUserOutfits(testUserId);

        expect(outfits).to.be.an('array');
        console.log(`✅ OutfitService retrieved ${outfits.length} user outfits`);
      } catch (error: any) {
        console.log('❌ OutfitService get user outfits failed:', error.message);
        expect.fail('Failed to get user outfits');
      }
    });
  });

  describe('Integration Tests', () => {
    it('should complete a full outfit workflow', async () => {
      try {
        // 1. Create a custom outfit
        const customOutfit = OutfitService.getDefaultOutfitData();
        const uniqueOutfitName = `Integration Test Outfit ${Date.now()}`;
        customOutfit.outfitName = uniqueOutfitName;

        // Modify the outfit
        if (customOutfit.items.hair) {
          customOutfit.items.hair.color = '#FF0000'; // Red hair
        }

        // 2. Save the outfit
        const saveResult = await OutfitService.saveUserOutfit(testUserId, uniqueOutfitName, customOutfit, true);

        console.log('🔍 Integration save result:', {
          success: saveResult.success,
          message: saveResult.message,
          outfitId: saveResult.outfitId
        });

        if (!saveResult.success) {
          console.log('❌ Integration save outfit failed:', saveResult.message);
          expect.fail(`Save outfit failed: ${saveResult.message}`);
        }

        expect(saveResult.success).to.be.true;

        const outfitId = saveResult.outfitId!;

        // 3. Load the outfit back
        console.log('🔍 Loading outfit back...');
        const loadedOutfit = await OutfitService.loadUserOutfit(testUserId, outfitId);
        console.log('🔍 Loaded outfit:', loadedOutfit ? 'exists' : 'null');
        expect(loadedOutfit).to.exist;
        expect(loadedOutfit.outfitName).to.equal(uniqueOutfitName);

        // 4. Verify it's set as current
        console.log('🔍 Checking current outfit...');
        const currentOutfit = await OutfitService.loadUserCurrentOutfit(testUserId);
        console.log('🔍 Current outfit:', currentOutfit ? currentOutfit.outfitName : 'null');
        expect(currentOutfit.outfitName).to.equal(uniqueOutfitName);

        // 5. Clean up - delete the test outfit
        console.log('🔍 Deleting test outfit...');
        const deleteResult = await OutfitService.deleteUserOutfit(testUserId, outfitId);
        console.log('🔍 Delete result:', deleteResult);

        if (!deleteResult.success) {
          console.log('⚠️  Delete failed, but continuing test:', deleteResult.message);
          // Don't fail the test if delete fails, as the main functionality worked
        }

        console.log('✅ Complete outfit workflow integration test passed');
      } catch (error: any) {
        console.log('❌ Integration test failed:', error.message);
        expect.fail('Integration test failed');
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid user ID', async () => {
      try {
        const outfits = await OutfitService.getUserOutfits('invalid_user');
        expect(outfits).to.be.an('array').that.is.empty;
      } catch (error) {
        // Error is expected for invalid user
        expect(error).to.exist;
      }
    });

    it('should handle invalid outfit data', () => {
      const invalidOutfit = {
        // Missing required fields
        items: null
      };

      const validation = OutfitService.validateOutfitData(invalidOutfit);
      expect(validation.isValid).to.be.false;
      expect(validation.errors).to.be.an('array').that.is.not.empty;
    });
  });
});
