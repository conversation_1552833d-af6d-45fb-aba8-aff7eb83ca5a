import pool from '../utils/db'; // Database connection pool utility
import { ResultSetHeader, RowDataPacket } from 'mysql2';
import { v4 as uuidv4 } from 'uuid'; // For generating unique instance IDs

// Import dependent services (assuming they are singletons and export their instances)
import ItemService, { ItemDetails } from './ItemService';
import UserService, { UserBasicInfo } from './UserService';
import FriendService from './friendService';

// Import related data models for type clarity, though not strictly Colyseus Schemas here
import { HomeItemState } from '../models/HomeItemState';
import { GardenPlotState } from '../models/GardenPlotState';

/**
 * @export
 * @interface UserPrivateSpaceSettings
 * @description Represents settings for a user's private spaces (home and garden) from the `user_private_spaces` table.
 * @property {string} owner_user_id - The ID of the user who owns these spaces.
 * @property {string} home_background_id - The item ID for the home's background.
 * @property {'private' | 'friends_only' | 'public'} home_access_level - Access control for the home.
 * @property {string} garden_background_id - The item ID for the garden's background.
 * @property {'private' | 'friends_only' | 'public'} garden_access_level - Access control for the garden.
 * @property {Date} [updated_at] - Timestamp of the last update.
 */
export interface UserPrivateSpaceSettings extends RowDataPacket {
  owner_user_id: string;
  home_background_id: string;
  home_access_level: 'private' | 'friends_only' | 'public';
  garden_background_id: string;
  garden_access_level: 'private' | 'friends_only' | 'public';
  updated_at?: Date;
}

/**
 * @export
 * @interface HomeItemRecord
 * @description Represents an item instance placed in a user's home, from the `home_items` table.
 * @property {string} item_instance_id - Unique ID for this specific instance of the item.
 * @property {string} owner_user_id - ID of the user who owns this item instance.
 * @property {string} item_template_id - ID of the item template from the `items` table.
 * @property {number} pos_x - X position of the item.
 * @property {number} pos_y - Y position of the item.
 * @property {number} rotation - Rotation of the item.
 * @property {boolean} is_flipped - Whether the item is flipped horizontally.
 * @property {Date} [placed_at] - Timestamp when the item was placed.
 */
export interface HomeItemRecord extends RowDataPacket {
  item_instance_id: string;
  owner_user_id: string;
  item_template_id: string;
  pos_x: number;
  pos_y: number;
  rotation: number;
  is_flipped: boolean;
  placed_at?: Date;
}

/**
 * @export
 * @interface GardenPlotRecord
 * @description Represents a garden plot instance in a user's garden, from the `garden_plots` table.
 * @property {string} plot_instance_id - Unique ID for this plot instance.
 * @property {string} owner_user_id - ID of the user who owns this plot.
 * @property {string} plot_template_id - ID of the plot template from the `items` table.
 * @property {string | null} seed_item_id - ID of the seed item planted, if any.
 * @property {Date | null} plant_timestamp - Timestamp when the seed was planted.
 * @property {number} growth_stage - Current growth stage of the plant.
 * @property {Date | null} last_watered_timestamp - Timestamp when the plot was last watered.
 * @property {Date} [created_at] - Timestamp of plot creation.
 * @property {Date} [updated_at] - Timestamp of the last update.
 */
export interface GardenPlotRecord extends RowDataPacket {
  plot_instance_id: string;
  owner_user_id: string;
  plot_template_id: string;
  seed_item_id: string | null;
  plant_timestamp: Date | null;
  growth_stage: number;
  last_watered_timestamp: Date | null;
  created_at?: Date;
  updated_at?: Date;
}

// --- Constants for Space Service ---
const DEFAULT_HOME_BACKGROUND = 'default_home_bg'; // Example default background item ID
const DEFAULT_GARDEN_BACKGROUND = 'default_garden_bg'; // Example default background item ID
const DEFAULT_ACCESS_LEVEL = 'private';
const DEFAULT_PLOT_TEMPLATE_ID = 'standard_plot'; // Example default plot template item ID
const DEFAULT_NUM_GARDEN_PLOTS = 4; // Default number of plots for a new garden

/**
 * @class SpaceService
 * @description Manages user-specific spaces like homes and gardens, including item placement and interactions.
 *              Implemented as a singleton.
 */
class SpaceService {
  private static instance: SpaceService;
  private itemService: typeof ItemService;
  private userService: typeof UserService; // Kept for potential future use, e.g., fetching owner details.
  private friendService: typeof FriendService;

  /**
   * Private constructor to enforce singleton pattern and inject dependencies.
   * @private
   * @param {ItemService} itemService - Instance of ItemService.
   * @param {UserService} userService - Instance of UserService.
   * @param {FriendService} friendService - Instance of FriendService.
   */
  private constructor(
    itemService: typeof ItemService,
    userService: typeof UserService,
    friendService: typeof FriendService
  ) {
    this.itemService = itemService;
    this.userService = userService;
    this.friendService = friendService;
  }

  /**
   * Gets the singleton instance of the SpaceService.
   * Dependencies are injected here.
   * @public
   * @static
   * @returns {SpaceService} The singleton instance.
   */
  public static getInstance(): SpaceService {
    if (!SpaceService.instance) {
      // Ensure dependent services are also singletons and obtained correctly.
      SpaceService.instance = new SpaceService(
        ItemService,
        UserService,
        FriendService // FriendService is exported as `new FriendService()`
      );
    }
    return SpaceService.instance;
  }

  /**
   * Retrieves settings for a user's private spaces (home and garden).
   * If no settings exist, default settings are created and returned.
   * @param {string} ownerUserId - The ID of the user whose space settings are being fetched.
   * @returns {Promise<UserPrivateSpaceSettings>} A promise that resolves with the user's space settings.
   * @throws {Error} If `ownerUserId` is invalid or if database interaction fails critically.
   */
  async getOrCreateUserSpaceSettings(ownerUserId: string): Promise<UserPrivateSpaceSettings> {
    if (!ownerUserId || typeof ownerUserId !== 'string') {
      throw new Error('SpaceService: getOrCreateUserSpaceSettings - ownerUserId must be a non-empty string.');
    }
    try {
      const [settingsRows] = await pool.query<UserPrivateSpaceSettings[]>(
        'SELECT * FROM user_private_spaces WHERE owner_user_id = ?',
        [ownerUserId]
      );

      if (settingsRows.length > 0) {
        return settingsRows[0];
      } else {
        // Create default settings if none exist.
        console.log(`SpaceService: No settings found for user ${ownerUserId}. Creating defaults.`);
        const [insertResult] = await pool.query<ResultSetHeader>(
          `INSERT INTO user_private_spaces (owner_user_id, home_background_id, home_access_level, garden_background_id, garden_access_level)
           VALUES (?, ?, ?, ?, ?)`,
          [ownerUserId, DEFAULT_HOME_BACKGROUND, DEFAULT_ACCESS_LEVEL, DEFAULT_GARDEN_BACKGROUND, DEFAULT_ACCESS_LEVEL]
        );
        if (insertResult.affectedRows === 0) {
            throw new Error('Failed to create default space settings due to a database error.');
        }
        // Re-fetch to get the newly created record with all defaults and DB-generated fields.
        const [newSettingsRows] = await pool.query<UserPrivateSpaceSettings[]>(
             'SELECT * FROM user_private_spaces WHERE owner_user_id = ?',
            [ownerUserId]
        );
        if (newSettingsRows.length === 0) { // Should not happen if insert was successful
            throw new Error('Failed to retrieve newly created default space settings.');
        }
        return newSettingsRows[0];
      }
    } catch (error: any) {
      console.error(`SpaceService: Error getting/creating space settings for user ${ownerUserId}: ${error.message}`);
      throw new Error(`Failed to get or create space settings for user ${ownerUserId}.`);
    }
  }

  /**
   * Initializes default garden plots for a user if they don't already exist.
   * @param {string} ownerUserId - The ID of the user for whom to initialize plots.
   * @param {number} [numPlots=DEFAULT_NUM_GARDEN_PLOTS] - The number of default plots to create.
   * @returns {Promise<boolean>} True if plots were initialized or already existed, false on error.
   * @throws {Error} If `ownerUserId` is invalid or if a database error occurs.
   */
  async initializeDefaultGardenPlots(ownerUserId: string, numPlots: number = DEFAULT_NUM_GARDEN_PLOTS): Promise<boolean> {
     if (!ownerUserId || typeof ownerUserId !== 'string') {
      throw new Error('SpaceService: initializeDefaultGardenPlots - ownerUserId must be a non-empty string.');
    }
    if (typeof numPlots !== 'number' || numPlots <= 0) {
        throw new Error('SpaceService: initializeDefaultGardenPlots - numPlots must be a positive number.');
    }
    try {
        const [existingPlots] = await pool.query<GardenPlotRecord[]>(
            'SELECT plot_instance_id FROM garden_plots WHERE user_id = ?',
            [ownerUserId]
        );

        if (existingPlots.length >= numPlots) { // Check if enough plots already exist
            console.log(`SpaceService: User ${ownerUserId} already has sufficient garden plots. Skipping initialization.`);
            return true;
        }

        const plotsToCreate = numPlots - existingPlots.length;
        if (plotsToCreate <= 0) return true; // Should be caught by above, but as a safeguard

        const plotValues: any[] = [];
        for (let i = existingPlots.length; i < numPlots; i++) {
            // Generate unique plot instance ID, e.g., combining user ID and plot index or using UUID.
            const plotInstanceId = `${ownerUserId}_plot_${i}`;
            plotValues.push([
                plotInstanceId, ownerUserId, DEFAULT_PLOT_TEMPLATE_ID,
                null, // seed_item_id
                null, // plant_timestamp
                0,    // growth_stage (empty)
                null  // last_watered_timestamp
            ]);
        }

        if (plotValues.length > 0) {
            const [insertResult] = await pool.query<ResultSetHeader>(
                `INSERT INTO garden_plots (plot_instance_id, user_id, plot_template_id, seed_item_id, plant_timestamp, growth_stage, last_watered_timestamp)
                 VALUES ?`, // Using bulk insert for multiple plots
                [plotValues]
            );
            if (insertResult.affectedRows !== plotsToCreate) {
                // This indicates a partial or failed bulk insert.
                console.warn(`SpaceService: Expected to create ${plotsToCreate} plots for user ${ownerUserId}, but ${insertResult.affectedRows} were inserted.`);
                // Depending on requirements, this might be a partial success or an error.
                // For simplicity, if not all expected plots were created, consider it an issue.
                throw new Error('Failed to initialize all default garden plots correctly.');
            }
            console.log(`SpaceService: Initialized ${plotsToCreate} new default garden plots for user ${ownerUserId}.`);
        }
        return true;
    } catch (error: any) {
        console.error(`SpaceService: Error initializing default garden plots for user ${ownerUserId}: ${error.message}`);
        throw new Error(`Failed to initialize default garden plots for user ${ownerUserId}.`);
    }
  }

  /**
   * Fetches all data related to a user's private spaces (settings, home items, garden plots).
   * Ensures default settings and garden plots are created if they don't exist.
   * @param {string} ownerUserId - The ID of the user whose space data is being fetched.
   * @returns {Promise<{ settings: UserPrivateSpaceSettings; homeItems: HomeItemRecord[]; gardenPlots: GardenPlotRecord[]; }>} Full space data.
   * @throws {Error} If `ownerUserId` is invalid or if a critical database error occurs.
   */
  async getFullPrivateSpaceData(ownerUserId: string): Promise<{
    settings: UserPrivateSpaceSettings;
    homeItems: HomeItemRecord[];
    gardenPlots: GardenPlotRecord[];
  }> {
    if (!ownerUserId || typeof ownerUserId !== 'string') {
      throw new Error('SpaceService: getFullPrivateSpaceData - ownerUserId must be a non-empty string.');
    }
    try {
      const settings = await this.getOrCreateUserSpaceSettings(ownerUserId);
      // `getOrCreateUserSpaceSettings` already throws if it fails critically.

      // Ensure default plots are created if they don't exist.
      await this.initializeDefaultGardenPlots(ownerUserId, DEFAULT_NUM_GARDEN_PLOTS);

      const [homeItems] = await pool.query<HomeItemRecord[]>(
        'SELECT * FROM home_items WHERE owner_user_id = ?',
        [ownerUserId]
      );
      const [gardenPlots] = await pool.query<GardenPlotRecord[]>(
        'SELECT * FROM garden_plots WHERE user_id = ?',
        [ownerUserId]
      );

      return { settings, homeItems, gardenPlots };
    } catch (error: any) {
      console.error(`SpaceService: Error fetching full private space data for user ${ownerUserId}: ${error.message}`);
      throw new Error(`Failed to fetch full private space data for user ${ownerUserId}.`);
    }
  }

  /**
   * Checks if a visitor has access to a specific type of space owned by another user.
   * @param {string} ownerUserId - The ID of the space owner.
   * @param {string} visitorUserId - The ID of the user attempting to visit.
   * @param {'home' | 'garden'} spaceType - The type of space to check access for.
   * @returns {Promise<boolean>} True if access is granted, false otherwise.
   * @throws {Error} If user IDs are invalid or a database error occurs during settings/friendship check.
   */
  async checkAccess(ownerUserId: string, visitorUserId: string, spaceType: 'home' | 'garden'): Promise<boolean> {
    if (!ownerUserId || typeof ownerUserId !== 'string' || !visitorUserId || typeof visitorUserId !== 'string') {
        throw new Error('SpaceService: checkAccess - ownerUserId and visitorUserId must be non-empty strings.');
    }
     if (spaceType !== 'home' && spaceType !== 'garden') {
        throw new Error("SpaceService: checkAccess - spaceType must be 'home' or 'garden'.");
    }

    if (ownerUserId === visitorUserId) {
      return true; // Owner always has access.
    }
    try {
      // Settings must exist for access checks (they are created on demand if missing).
      const settings = await this.getOrCreateUserSpaceSettings(ownerUserId);

      const accessLevel = spaceType === 'home' ? settings.home_access_level : settings.garden_access_level;

      switch (accessLevel) {
        case 'private':
          return false; // Only owner can access (already handled by ownerUserId === visitorUserId check).
        case 'friends_only':
          // Delegate to FriendService to check if they are accepted friends.
          return await this.friendService.areFriends(ownerUserId, visitorUserId);
        case 'public':
          return true; // Public spaces are accessible to anyone.
        default:
          // Should not happen if accessLevel ENUM is correctly managed.
          console.warn(`SpaceService: Unknown access level '${accessLevel}' for user ${ownerUserId}'s ${spaceType}. Denying access.`);
          return false;
      }
    } catch (error: any) {
      console.error(`SpaceService: Error checking access for visitor ${visitorUserId} to ${ownerUserId}'s ${spaceType}: ${error.message}`);
      // Re-throw to indicate a failure in the access check process.
      throw new Error(`Failed to check access for ${spaceType}: ${error.message}`);
    }
  }

  /**
   * Places a new item into a user's home.
   * @param {string} ownerUserId - The ID of the user who owns the home.
   * @param {string} itemTemplateId - The template ID of the item to place.
   * @param {number} x - The X position for the item.
   * @param {number} y - The Y position for the item.
   * @param {number} [rotation=0] - The rotation of the item.
   * @param {boolean} [isFlipped=false] - Whether the item is flipped.
   * @returns {Promise<{ success: boolean; message: string; itemInstance?: HomeItemRecord }>} Result of the operation.
   * @throws {Error} If inputs are invalid, item is not placeable, inventory check fails, or DB error occurs.
   */
  async placeHomeItem(
    ownerUserId: string,
    itemTemplateId: string,
    x: number,
    y: number,
    rotation: number = 0,
    isFlipped: boolean = false
  ): Promise<{ success: true; message: string; itemInstance: HomeItemRecord } | { success: false; message: string }> {
    // Input validation
    if (!ownerUserId || !itemTemplateId || typeof x !== 'number' || typeof y !== 'number') {
      throw new Error('SpaceService: placeHomeItem - ownerUserId, itemTemplateId, x, and y are required.');
    }

    try {
      // 1. Validate item template existence and type (e.g., must be 'furniture').
      const itemDetails = await this.itemService.getItemDetails(itemTemplateId);
      if (!itemDetails) {
        throw new Error(`Item template '${itemTemplateId}' not found.`);
      }
      if (itemDetails.type !== 'furniture') { // Example: only 'furniture' type can be placed in home.
        throw new Error(`Item '${itemDetails.name}' (type: ${itemDetails.type}) cannot be placed in a home.`);
      }

      // 2. Check and use item from inventory (real inventory system).
      // This will throw if the item is not available or cannot be used.
      const itemUsed = await this.itemService.checkAndUseInventoryItem(ownerUserId, itemTemplateId, 1);
      if (!itemUsed) {
        throw new Error(`Failed to use item '${itemTemplateId}' from inventory.`);
      }

      const itemInstanceId = uuidv4(); // Generate a unique ID for this instance of the item.

      // Create a partial record for insertion, DB defaults will apply for `placed_at`.
      const newItemRecord: Omit<HomeItemRecord, 'placed_at' | 'updated_at'> = {
        item_instance_id: itemInstanceId,
        owner_user_id: ownerUserId,
        item_template_id: itemTemplateId,
        pos_x: x,
        pos_y: y,
        rotation: rotation || 0,
        is_flipped: isFlipped || false,
      };

      const [insertResult] = await pool.query<ResultSetHeader>(
        'INSERT INTO home_items SET ?',
        newItemRecord
      );

      if (insertResult.affectedRows === 0) {
        // This indicates an issue with the insert that didn't throw an SQL error.
        // TODO: Consider attempting to "return" item to inventory if this step fails.
        throw new Error('Failed to place item in home due to a database error (0 rows affected).');
      }

      // Re-fetch the placed item to get all its data including DB defaults like `placed_at`.
      const [rows] = await pool.query<HomeItemRecord[]>("SELECT * FROM home_items WHERE item_instance_id = ?", [itemInstanceId]);
      if (rows.length === 0) {
          // Should not happen if insert was successful.
          throw new Error('Failed to retrieve newly placed item details.');
      }

      return { success: true, message: 'Item placed successfully.', itemInstance: rows[0] };
    } catch (error: any) {
      console.error(`SpaceService: Error placing home item '${itemTemplateId}' for user ${ownerUserId}: ${error.message}`);
      // TODO: Add logic here to attempt to "return" the item to inventory if checkAndUseInventoryItem was successful
      // but a subsequent step failed (e.g., DB insert error). This requires careful transaction management in a real scenario.
      // For now, re-throw the caught error or a new generic one.
      if (error.message.startsWith('Item template') || error.message.startsWith('Item ') || error.message.startsWith('Insufficient quantity')) {
          throw error; // Re-throw specific validation/inventory errors.
      }
      throw new Error(`Server error placing item in home: ${error.message}`);
    }
  }

  /**
   * Updates the details (position, rotation, flip state) of an existing home item.
   * @param {string} ownerUserId - The ID of the user who owns the item.
   * @param {string} itemInstanceId - The instance ID of the item to update.
   * @param {Partial<Pick<HomeItemRecord, 'pos_x' | 'pos_y' | 'rotation' | 'is_flipped'>>} updates - An object with fields to update.
   * @returns {Promise<{ success: boolean; message: string }>} Result of the operation.
   * @throws {Error} If inputs are invalid, item not found/owned, or DB error occurs.
   */
  async updateHomeItemDetails(
    ownerUserId: string,
    itemInstanceId: string,
    updates: Partial<Pick<HomeItemRecord, 'pos_x' | 'pos_y' | 'rotation' | 'is_flipped'>>
  ): Promise<{ success: true; message: string } | { success: false; message: string }> {
    if (!ownerUserId || !itemInstanceId || !updates || Object.keys(updates).length === 0) {
      throw new Error('SpaceService: updateHomeItemDetails - ownerUserId, itemInstanceId, and updates object are required.');
    }

    try {
      const setClauses: string[] = [];
      const queryParams: any[] = [];

      // Build SET clause dynamically based on provided updates.
      if (updates.pos_x !== undefined) {
        if (typeof updates.pos_x !== 'number') throw new Error("Invalid 'pos_x': must be a number.");
        setClauses.push('pos_x = ?'); queryParams.push(updates.pos_x);
      }
      if (updates.pos_y !== undefined) {
        if (typeof updates.pos_y !== 'number') throw new Error("Invalid 'pos_y': must be a number.");
        setClauses.push('pos_y = ?'); queryParams.push(updates.pos_y);
      }
      if (updates.rotation !== undefined) {
        if (typeof updates.rotation !== 'number') throw new Error("Invalid 'rotation': must be a number.");
        setClauses.push('rotation = ?'); queryParams.push(updates.rotation);
      }
      if (updates.is_flipped !== undefined) {
        if (typeof updates.is_flipped !== 'boolean') throw new Error("Invalid 'is_flipped': must be a boolean.");
        setClauses.push('is_flipped = ?'); queryParams.push(updates.is_flipped);
      }

      if (setClauses.length === 0) {
        throw new Error('No valid fields provided for update or no changes specified.');
      }

      queryParams.push(itemInstanceId);
      queryParams.push(ownerUserId); // Ensure user owns the item for the update.

      const [result] = await pool.query<ResultSetHeader>(
        `UPDATE home_items SET ${setClauses.join(', ')}, updated_at = NOW() WHERE item_instance_id = ? AND owner_user_id = ?`,
        queryParams
      );

      if (result.affectedRows > 0) {
        return { success: true, message: 'Item details updated successfully.' };
      } else {
        // This implies the item was not found for that user, or data was identical.
        // Check if item exists at all to give a more specific error.
        const [itemCheck] = await pool.query<HomeItemRecord[]>("SELECT owner_user_id FROM home_items WHERE item_instance_id = ?", [itemInstanceId]);
        if (itemCheck.length === 0) throw new Error(`Item with instance ID '${itemInstanceId}' not found.`);
        if (itemCheck[0].owner_user_id.toString() !== ownerUserId.toString()) throw new Error('Access denied: You do not own this item.');
        // If item exists and owned, but no rows affected, it means data was identical.
        return { success: false, message: 'No changes made to the item. Data might be identical or item not found for user.' };
      }
    } catch (error: any) {
      console.error(`SpaceService: Error updating home item '${itemInstanceId}' for user ${ownerUserId}: ${error.message}`);
      if (error.message.startsWith('Invalid') || error.message.startsWith('No valid fields') || error.message.startsWith('Item with instance ID') || error.message.startsWith('Access denied')) {
          throw error;
      }
      throw new Error(`Server error updating item details: ${error.message}`);
    }
  }

  /**
   * Removes an item from a user's home and (simulates) returning it to their inventory.
   * @param {string} ownerUserId - The ID of the user who owns the item.
   * @param {string} itemInstanceId - The instance ID of the item to remove.
   * @returns {Promise<{ success: boolean; message: string; returnedItemId?: string }>} Result of the operation.
   * @throws {Error} If inputs are invalid, item not found/owned, or DB/inventory error occurs.
   */
  async removeHomeItem(ownerUserId: string, itemInstanceId: string): Promise<{ success: true; message: string; returnedItemId: string } | { success: false; message: string }> {
    if (!ownerUserId || !itemInstanceId) {
      throw new Error('SpaceService: removeHomeItem - ownerUserId and itemInstanceId are required.');
    }
    try {
      // First, retrieve the item_template_id to "return" it to inventory.
      // Also verifies ownership before deleting.
      const [itemRows] = await pool.query<HomeItemRecord[]>(
          'SELECT item_template_id FROM home_items WHERE item_instance_id = ? AND owner_user_id = ?',
          [itemInstanceId, ownerUserId]
      );

      if (itemRows.length === 0) {
          throw new Error(`Item with instance ID '${itemInstanceId}' not found or not owned by user '${ownerUserId}'.`);
      }
      const itemTemplateId = itemRows[0].item_template_id;

      // Delete the item from the home.
      const [deleteResult] = await pool.query<ResultSetHeader>(
        'DELETE FROM home_items WHERE item_instance_id = ? AND owner_user_id = ?',
        [itemInstanceId, ownerUserId] // owner_user_id is redundant here if PK is just item_instance_id, but good for safety.
      );

      if (deleteResult.affectedRows > 0) {
        // Return item to inventory using real inventory system. This should throw if it fails.
        const itemReturned = await this.itemService.addInventoryItem(ownerUserId, itemTemplateId, 1, 'home_item_removal');
        if (!itemReturned) {
          console.warn(`SpaceService: Item '${itemTemplateId}' was removed from home but failed to return to inventory for user '${ownerUserId}'.`);
          // Note: In a production system, you might want to implement compensation logic here
          // or log this for manual intervention
        }
        return { success: true, message: `Item '${itemTemplateId}' removed and returned to inventory.`, returnedItemId: itemTemplateId };
      } else {
        // This case should ideally be caught by the SELECT query above.
        throw new Error(`Failed to remove item '${itemInstanceId}'. It might have been already removed or ownership issue.`);
      }
    } catch (error: any) {
      console.error(`SpaceService: Error removing home item '${itemInstanceId}' for user ${ownerUserId}: ${error.message}`);
      if (error.message.startsWith('Item with instance ID') || error.message.includes('inventory')) {
          throw error;
      }
      throw new Error(`Server error removing item from home: ${error.message}`);
    }
  }

  // --- Garden Management Methods ---

  /**
   * Plants a seed in a specified garden plot.
   * @param {string} ownerUserId - The ID of the user who owns the garden.
   * @param {string} plotInstanceId - The ID of the plot to plant in.
   * @param {string} seedItemId - The template ID of the seed to plant.
   * @returns {Promise<{ success: boolean; message: string }>} Result of the operation.
   * @throws {Error} If inputs are invalid, seed/plot not found, plot not empty, inventory check fails, or DB error.
   */
  async plantSeed(
    ownerUserId: string,
    plotInstanceId: string,
    seedItemId: string
  ): Promise<{ success: true; message: string } | { success: false; message: string }> {
    if (!ownerUserId || !plotInstanceId || !seedItemId) {
      throw new Error('SpaceService: plantSeed - ownerUserId, plotInstanceId, and seedItemId are required.');
    }
    try {
      // 1. Validate seed item type.
      const seedDetails = await this.itemService.getItemDetails(seedItemId);
      if (!seedDetails) {
        throw new Error(`Seed item '${seedItemId}' not found.`);
      }
      if (seedDetails.type !== 'seed') {
        throw new Error(`Item '${seedDetails.name}' is not a valid seed (type: ${seedDetails.type}).`);
      }

      // 2. Check plot existence, ownership, and if it's empty.
      const [plotRows] = await pool.query<GardenPlotRecord[]>(
        'SELECT seed_item_id, growth_stage FROM garden_plots WHERE plot_instance_id = ? AND user_id = ?',
        [plotInstanceId, ownerUserId]
      );
      if (plotRows.length === 0) {
        throw new Error(`Garden plot '${plotInstanceId}' not found or not owned by user '${ownerUserId}'.`);
      }
      const plot = plotRows[0];
      if (plot.seed_item_id || plot.growth_stage !== 0) { // growth_stage 0 means empty.
        throw new Error(`Plot '${plotInstanceId}' is not empty and cannot be planted.`);
      }

      // 3. Check and use seed from inventory (real inventory system).
      const seedUsed = await this.itemService.checkAndUseInventoryItem(ownerUserId, seedItemId, 1);
      if (!seedUsed) {
        throw new Error(`Failed to use seed '${seedItemId}' from inventory.`);
      }

      const plantTimestamp = new Date();
      const lastWateredTimestamp = new Date(); // Assume initial watering upon planting.

      // Update the plot record with the new seed and planting details.
      const [updateResult] = await pool.query<ResultSetHeader>(
        `UPDATE garden_plots SET seed_item_id = ?, plant_timestamp = ?, growth_stage = 1, last_watered_timestamp = ?, updated_at = NOW()
         WHERE plot_instance_id = ? AND user_id = ?`,
        [seedItemId, plantTimestamp, lastWateredTimestamp, plotInstanceId, ownerUserId]
      );

      if (updateResult.affectedRows === 0) {
          // TODO: Consider attempting to "return" seed to inventory if this step fails.
          throw new Error('Failed to update plot with new seed information.');
      }

      return { success: true, message: `Seed '${seedDetails.name}' planted successfully in plot '${plotInstanceId}'.` };
    } catch (error: any) {
      console.error(`SpaceService: Error planting seed '${seedItemId}' in plot '${plotInstanceId}' for user ${ownerUserId}: ${error.message}`);
      // TODO: Add logic here to attempt to "return" the seed to inventory if checkAndUseInventoryItem was successful.
      if (error.message.startsWith('Seed item') || error.message.startsWith('Item ') || error.message.startsWith('Garden plot') || error.message.startsWith('Plot ')) {
          throw error;
      }
      throw new Error(`Server error planting seed: ${error.message}`);
    }
  }

  /**
   * Waters a plant in a specific garden plot.
   * @param {string} ownerUserId - The ID of the user who owns the garden (for ownership check of the plot).
   * @param {string} plotInstanceId - The ID of the plot to water.
   * @returns {Promise<{ success: boolean; message: string }>} Result of the operation.
   * @throws {Error} If inputs are invalid, plot not found/owned, or plant is not in a waterable state, or DB error.
   */
  async waterPlant(ownerUserId: string, plotInstanceId: string): Promise<{ success: true; message: string } | { success: false; message: string }> {
    if (!ownerUserId || !plotInstanceId) {
      throw new Error('SpaceService: waterPlant - ownerUserId and plotInstanceId are required.');
    }
    try {
       const [plotRows] = await pool.query<GardenPlotRecord[]>(
        'SELECT growth_stage, seed_item_id FROM garden_plots WHERE plot_instance_id = ? AND user_id = ?',
        [plotInstanceId, ownerUserId]
      );
      if (plotRows.length === 0) {
        throw new Error(`Plot '${plotInstanceId}' not found or not owned by user '${ownerUserId}'.`);
      }
      const plot = plotRows[0];
      if (!plot.seed_item_id || plot.growth_stage === 0) {
         throw new Error(`Cannot water an empty plot ('${plotInstanceId}').`);
      }
      // Assuming stage 4 is harvestable, and higher stages might be withered/dead.
      // This logic might need refinement based on specific game mechanics for plant states.
      if (plot.growth_stage >= 4) {
         throw new Error(`Plant in plot '${plotInstanceId}' is already mature or past its watering stage.`);
      }

      const [updateResult] = await pool.query<ResultSetHeader>(
        'UPDATE garden_plots SET last_watered_timestamp = NOW(), updated_at = NOW() WHERE plot_instance_id = ? AND user_id = ?',
        [plotInstanceId, ownerUserId]
      );
       if (updateResult.affectedRows === 0) {
          throw new Error('Failed to update watering timestamp for the plot.'); // Should not happen if select was successful.
      }
      return { success: true, message: `Plant in plot '${plotInstanceId}' watered successfully.` };
    } catch (error: any) {
      console.error(`SpaceService: Error watering plant in plot '${plotInstanceId}' for user ${ownerUserId}: ${error.message}`);
       if (error.message.startsWith('Plot ') || error.message.startsWith('Cannot water')) {
          throw error;
      }
      throw new Error(`Server error watering plant: ${error.message}`);
    }
  }

  /**
   * Manually updates a plant's growth stage. Primarily for admin or specific game mechanics,
   * as growth is usually time-based.
   * @param {string} ownerUserId - The ID of the user who owns the plot.
   * @param {string} plotInstanceId - The ID of the plot to update.
   * @param {number} newStage - The new growth stage to set.
   * @returns {Promise<{ success: boolean; message: string }>} Result of the operation.
   * @throws {Error} If inputs invalid, plot not found/owned/planted, or DB error.
   */
  async updatePlantGrowthStage(ownerUserId: string, plotInstanceId: string, newStage: number): Promise<{ success: true; message: string } | { success: false; message: string }> {
    if (!ownerUserId || !plotInstanceId || typeof newStage !== 'number' || newStage < 0) {
      throw new Error('SpaceService: updatePlantGrowthStage - ownerUserId, plotInstanceId, and a valid newStage are required.');
    }
    try {
      // Ensure the plot exists, is owned, and has something planted.
      const [plotCheck] = await pool.query<GardenPlotRecord[]>(
        'SELECT seed_item_id FROM garden_plots WHERE plot_instance_id = ? AND user_id = ?',
        [plotInstanceId, ownerUserId]
      );
      if (plotCheck.length === 0) {
        throw new Error(`Plot '${plotInstanceId}' not found or not owned by user '${ownerUserId}'.`);
      }
      if (!plotCheck[0].seed_item_id) {
        throw new Error(`Plot '${plotInstanceId}' has no seed planted; cannot update growth stage.`);
      }

      const [result] = await pool.query<ResultSetHeader>(
        'UPDATE garden_plots SET growth_stage = ?, updated_at = NOW() WHERE plot_instance_id = ? AND user_id = ?',
        [newStage, plotInstanceId, ownerUserId]
      );
       if (result.affectedRows > 0) {
        return { success: true, message: `Plant growth stage in plot '${plotInstanceId}' updated to ${newStage}.` };
      } else {
        // Should ideally not happen if select check passed, unless DB issue or race condition.
        throw new Error('Failed to update plant growth stage. Plot may have been modified.');
      }
    } catch (error: any) {
      console.error(`SpaceService: Error updating growth stage for plot '${plotInstanceId}' for user ${ownerUserId}: ${error.message}`);
      if (error.message.startsWith('Plot ')) {
          throw error;
      }
      throw new Error(`Server error updating plant growth stage: ${error.message}`);
    }
  }

  /**
   * Harvests a mature plant from a garden plot.
   * @param {string} ownerUserId - The ID of the user who owns the plot.
   * @param {string} plotInstanceId - The ID of the plot to harvest from.
   * @returns {Promise<{ success: boolean; message: string; harvestedItems?: { itemId: string; quantity: number }[] }>} Result of the operation.
   * @throws {Error} If inputs invalid, plot not found/owned, plant not harvestable, or DB/inventory error.
   */
  async harvestPlant(ownerUserId: string, plotInstanceId: string): Promise<{ success: true; message: string; harvestedItems: { itemId: string; quantity: number }[] } | { success: false; message: string }> {
    if (!ownerUserId || !plotInstanceId) {
      throw new Error('SpaceService: harvestPlant - ownerUserId and plotInstanceId are required.');
    }
    try {
      const [plotRows] = await pool.query<GardenPlotRecord[]>(
        'SELECT * FROM garden_plots WHERE plot_instance_id = ? AND user_id = ?',
        [plotInstanceId, ownerUserId]
      );
      if (plotRows.length === 0) {
        throw new Error(`Plot '${plotInstanceId}' not found or not owned by user '${ownerUserId}'.`);
      }
      const plot = plotRows[0];
      if (!plot.seed_item_id) {
        throw new Error(`Plot '${plotInstanceId}' is empty, nothing to harvest.`);
      }

      // Determine harvestability based on seed data (e.g., in `items.data` for the seed).
      // For this simulation, let's assume stage 4 is harvestable. This should be configurable per plant.
      const seedDetails = await this.itemService.getItemDetails(plot.seed_item_id);
      if (!seedDetails) {
        // Data integrity issue: planted seed that doesn't exist in items table.
        throw new Error(`Critical error: Original seed data for '${plot.seed_item_id}' not found. Cannot determine harvest details.`);
      }

      let requiredGrowthStage = 4; // Default harvestable stage
      let harvestedItemId = `crop_from_${plot.seed_item_id}`; // Default crop ID convention
      let harvestedQuantity = 1; // Default quantity

      if (seedDetails.data) {
        try {
          const seedDataJson = JSON.parse(seedDetails.data);
          if (typeof seedDataJson.harvestStage === 'number') requiredGrowthStage = seedDataJson.harvestStage;
          if (seedDataJson.yields && typeof seedDataJson.yields.itemId === 'string') harvestedItemId = seedDataJson.yields.itemId;
          if (seedDataJson.yields && typeof seedDataJson.yields.quantity === 'number') harvestedQuantity = seedDataJson.yields.quantity;
        } catch (e: any) {
          console.warn(`SpaceService: Could not parse seed data JSON for '${plot.seed_item_id}' to determine precise yield/harvest stage: ${e.message}. Using defaults.`);
        }
      }

      if (plot.growth_stage < requiredGrowthStage) {
        throw new Error(`Plant in plot '${plotInstanceId}' is not yet mature for harvest (current stage: ${plot.growth_stage}, required: ${requiredGrowthStage}).`);
      }

      // Reset plot in the database.
      const [updateResult] = await pool.query<ResultSetHeader>(
        `UPDATE garden_plots SET seed_item_id = NULL, plant_timestamp = NULL, growth_stage = 0, last_watered_timestamp = NULL, updated_at = NOW()
         WHERE plot_instance_id = ? AND user_id = ?`,
        [plotInstanceId, ownerUserId]
      );
      if (updateResult.affectedRows === 0) {
          throw new Error('Failed to reset plot after harvest. Database consistency issue.');
      }

      // Add harvested items to inventory using real inventory system. This should throw if it fails.
      const itemAdded = await this.itemService.addInventoryItem(ownerUserId, harvestedItemId, harvestedQuantity, 'harvest');
      if (!itemAdded) {
        console.warn(`SpaceService: Plot was harvested but failed to add items to inventory for user '${ownerUserId}'.`);
        // Note: In a production system, you might want to implement compensation logic here
      }

      const cropDetails = await this.itemService.getItemDetails(harvestedItemId);
      const cropName = cropDetails ? cropDetails.name : harvestedItemId;

      return {
        success: true,
        message: `Successfully harvested ${harvestedQuantity}x ${cropName} from plot '${plotInstanceId}'. Plot is now empty.`,
        harvestedItems: [{ itemId: harvestedItemId, quantity: harvestedQuantity }],
      };
    } catch (error: any) {
      console.error(`SpaceService: Error harvesting plant from plot '${plotInstanceId}' for user ${ownerUserId}: ${error.message}`);
      if (error.message.startsWith('Plot ') || error.message.startsWith('Plant in plot') || error.message.startsWith('Critical error') || error.message.includes('inventory')) {
          throw error;
      }
      throw new Error(`Server error during harvest: ${error.message}`);
    }
  }

  /**
   * Updates the background for a user's home or garden.
   * @param {string} ownerUserId - The ID of the user whose space background is being updated.
   * @param {'home' | 'garden'} spaceType - The type of space to update.
   * @param {string} backgroundId - The item ID of the new background.
   * @returns {Promise<{ success: boolean; message: string }>} Result of the operation.
   * @throws {Error} If inputs invalid, background item not valid, or DB error.
   */
  async updateSpaceBackground(ownerUserId: string, spaceType: 'home' | 'garden', backgroundId: string): Promise<{ success: true; message: string } | { success: false; message: string }> {
    if (!ownerUserId || !backgroundId || (spaceType !== 'home' && spaceType !== 'garden')) {
      throw new Error('SpaceService: updateSpaceBackground - ownerUserId, spaceType, and backgroundId are required.');
    }
    try {
      // 1. Validate that backgroundId is a valid background item.
      const itemDetails = await this.itemService.getItemDetails(backgroundId);
      if (!itemDetails) {
        throw new Error(`Item '${backgroundId}' not found.`);
      }

      // Check if it's a background item by checking the data field
      let isBackgroundItem = false;
      if (itemDetails.data) {
        try {
          const itemData = JSON.parse(itemDetails.data);
          isBackgroundItem = itemData.type === 'background' || itemDetails.category === 'background';
        } catch (e) {
          // If data is not valid JSON, check category
          isBackgroundItem = itemDetails.category === 'background';
        }
      } else {
        isBackgroundItem = itemDetails.category === 'background';
      }

      if (!isBackgroundItem) {
        throw new Error(`Item '${backgroundId}' is not a valid background item.`);
      }
      // Optional: Check if itemDetails.data contains allowed_scenes and if this background is allowed for this spaceType.

      const fieldToUpdate = spaceType === 'home' ? 'home_background_id' : 'garden_background_id';

      const [result] = await pool.query<ResultSetHeader>(
        `UPDATE user_private_spaces SET ${fieldToUpdate} = ?, updated_at = NOW() WHERE owner_user_id = ?`,
        [backgroundId, ownerUserId]
      );

      if (result.affectedRows > 0) {
        return { success: true, message: `${spaceType} background updated successfully to '${itemDetails.name}'.` };
      } else {
        // This implies user_private_spaces record for ownerUserId might not exist,
        // but getOrCreateUserSpaceSettings should prevent this if called appropriately by rooms.
        // Or, the background ID was the same as the current one.
        throw new Error('User space settings not found, or background ID was already set to this value.');
      }
    } catch (error: any) {
      console.error(`SpaceService: Error updating ${spaceType} background for user ${ownerUserId} to ${backgroundId}: ${error.message}`);
      if (error.message.startsWith('Item ') || error.message.startsWith('User space settings not found')) {
          throw error;
      }
      throw new Error(`Server error updating ${spaceType} background: ${error.message}`);
    }
  }

  /**
   * Updates the access level (private, friends_only, public) for a user's home or garden.
   * @param {string} ownerUserId - The ID of the user whose space access level is being updated.
   * @param {'home' | 'garden'} spaceType - The type of space to update.
   * @param {'private' | 'friends_only' | 'public'} accessLevel - The new access level.
   * @returns {Promise<{ success: boolean; message: string }>} Result of the operation.
   * @throws {Error} If inputs invalid or DB error.
   */
  async updateSpaceAccessLevel(ownerUserId: string, spaceType: 'home' | 'garden', accessLevel: 'private' | 'friends_only' | 'public'): Promise<{ success: true; message: string } | { success: false; message: string }> {
    if (!ownerUserId || !accessLevel || (spaceType !== 'home' && spaceType !== 'garden')) {
      throw new Error('SpaceService: updateSpaceAccessLevel - ownerUserId, spaceType, and accessLevel are required.');
    }
    if (!['private', 'friends_only', 'public'].includes(accessLevel)) {
      throw new Error("SpaceService: updateSpaceAccessLevel - Invalid access level provided. Must be 'private', 'friends_only', or 'public'.");
    }

    try {
      const fieldToUpdate = spaceType === 'home' ? 'home_access_level' : 'garden_access_level';

      const [result] = await pool.query<ResultSetHeader>(
        `UPDATE user_private_spaces SET ${fieldToUpdate} = ?, updated_at = NOW() WHERE owner_user_id = ?`,
        [accessLevel, ownerUserId]
      );

      if (result.affectedRows > 0) {
        return { success: true, message: `${spaceType} access level updated to '${accessLevel}'.` };
      } else {
        throw new Error('User space settings not found, or access level was already set to this value.');
      }
    } catch (error: any) {
      console.error(`SpaceService: Error updating ${spaceType} access level for user ${ownerUserId} to ${accessLevel}: ${error.message}`);
       if (error.message.startsWith('User space settings not found')) {
          throw error;
      }
      throw new Error(`Server error updating ${spaceType} access level: ${error.message}`);
    }
  }
}

// Export a singleton instance of SpaceService, ensuring dependent services are passed.
export default SpaceService.getInstance();
