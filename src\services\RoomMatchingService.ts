import { Room, Client, MatchMakerDriver } from '@colyseus/core';
import pool from '../utils/db';
import { RowDataPacket } from 'mysql2';
import SpaceService from './SpaceService';

/**
 * Room access levels
 */
export type AccessLevel = 'private' | 'friends_only' | 'public';

/**
 * Room types
 */
export type RoomType = 'home' | 'garden' | 'quest';

/**
 * Room information interface
 */
export interface RoomInfo extends RowDataPacket {
  room_id: string;
  room_type: RoomType;
  owner_uid: number; // Changed from string to number to match database BIGINT
  access_level: AccessLevel;
  is_active: boolean;
  created_at: Date;
  last_accessed: Date;
}

/**
 * Room matching options
 */
export interface RoomMatchingOptions {
  ownerUid?: string;
  roomType: RoomType;
  requestingUid: string;
  token: string;
  createIfNotExists?: boolean;
}

/**
 * Room creation result
 */
export interface RoomCreationResult {
  roomId: string;
  success: boolean;
  message?: string;
}

/**
 * Service for managing dynamic room creation and matching
 */
class RoomMatchingService {
  private static instance: RoomMatchingService;

  private constructor() {}

  public static getInstance(): RoomMatchingService {
    if (!RoomMatchingService.instance) {
      RoomMatchingService.instance = new RoomMatchingService();
    }
    return RoomMatchingService.instance;
  }

  /**
   * Generate dynamic room ID
   */
  generateRoomId(roomType: RoomType, ownerUid: string): string {
    return `${roomType}_${ownerUid}`;
  }

  /**
   * Parse room ID to extract type and owner
   */
  parseRoomId(roomId: string): { roomType: RoomType; ownerUid: string } | null {
    const parts = roomId.split('_');
    if (parts.length < 2) return null;

    const roomType = parts[0] as RoomType;
    const ownerUid = parts.slice(1).join('_'); // Handle UIDs that might contain underscores

    if (!['home', 'garden', 'quest'].includes(roomType)) {
      return null;
    }

    return { roomType, ownerUid };
  }

  /**
   * Check if user has access to a specific room
   */
  async checkRoomAccess(
    ownerUid: string,
    requestingUid: string,
    roomType: RoomType
  ): Promise<boolean> {
    try {
      // Use existing SpaceService for access checking
      if (roomType === 'home' || roomType === 'garden') {
        return await SpaceService.checkAccess(ownerUid, requestingUid, roomType);
      }

      // For quest rooms, implement different logic if needed
      if (roomType === 'quest') {
        // Quest rooms might have different access rules
        // For now, allow access if user is authenticated
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking room access:', error);
      return false;
    }
  }

  /**
   * Get room information
   */
  async getRoomInfo(roomId: string): Promise<RoomInfo | null> {
    try {
      const [rows] = await pool.query<RoomInfo[]>(
        'SELECT * FROM room_registry WHERE room_id = ?',
        [roomId]
      );

      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('Error getting room info:', error);
      return null;
    }
  }

  /**
   * Register a room in the registry
   */
  async registerRoom(
    roomId: string,
    roomType: RoomType,
    ownerUid: string | number,
    accessLevel: AccessLevel = 'private'
  ): Promise<boolean> {
    try {
      // Convert string to number if needed
      const ownerUidNum = typeof ownerUid === 'string' ? parseInt(ownerUid, 10) : ownerUid;

      await pool.query(
        `INSERT INTO room_registry (room_id, room_type, owner_uid, access_level, is_active, last_accessed)
         VALUES (?, ?, ?, ?, TRUE, NOW())
         ON DUPLICATE KEY UPDATE
         is_active = TRUE,
         last_accessed = NOW(),
         access_level = VALUES(access_level)`,
        [roomId, roomType, ownerUidNum, accessLevel]
      );

      return true;
    } catch (error) {
      console.error('Error registering room:', error);
      return false;
    }
  }

  /**
   * Unregister a room from the registry
   */
  async unregisterRoom(roomId: string): Promise<boolean> {
    try {
      await pool.query(
        'UPDATE room_registry SET is_active = FALSE WHERE room_id = ?',
        [roomId]
      );

      return true;
    } catch (error) {
      console.error('Error unregistering room:', error);
      return false;
    }
  }

  /**
   * Update room access level
   */
  async updateRoomAccessLevel(
    roomId: string,
    accessLevel: AccessLevel
  ): Promise<boolean> {
    try {
      await pool.query(
        'UPDATE room_registry SET access_level = ? WHERE room_id = ?',
        [accessLevel, roomId]
      );

      return true;
    } catch (error) {
      console.error('Error updating room access level:', error);
      return false;
    }
  }

  /**
   * Get active rooms for a user
   */
  async getUserActiveRooms(ownerUid: string): Promise<RoomInfo[]> {
    try {
      const [rows] = await pool.query<RoomInfo[]>(
        'SELECT * FROM room_registry WHERE owner_uid = ? AND is_active = TRUE ORDER BY last_accessed DESC',
        [ownerUid]
      );

      return rows;
    } catch (error) {
      console.error('Error getting user active rooms:', error);
      return [];
    }
  }

  /**
   * Get public rooms of a specific type
   */
  async getPublicRooms(roomType: RoomType, limit: number = 20): Promise<RoomInfo[]> {
    try {
      const [rows] = await pool.query<RoomInfo[]>(
        `SELECT * FROM room_registry
         WHERE room_type = ? AND access_level = 'public' AND is_active = TRUE
         ORDER BY last_accessed DESC
         LIMIT ?`,
        [roomType, limit]
      );

      return rows;
    } catch (error) {
      console.error('Error getting public rooms:', error);
      return [];
    }
  }

  /**
   * Get friend rooms that user can access
   */
  async getFriendRooms(
    requestingUid: string,
    roomType: RoomType,
    limit: number = 20
  ): Promise<RoomInfo[]> {
    try {
      const [rows] = await pool.query<RoomInfo[]>(
        `SELECT rr.* FROM room_registry rr
         JOIN friend_relationships fr ON (
           (fr.user_one_id = ? AND fr.user_two_id = rr.owner_uid) OR
           (fr.user_two_id = ? AND fr.user_one_id = rr.owner_uid)
         )
         WHERE rr.room_type = ?
         AND rr.access_level IN ('friends_only', 'public')
         AND rr.is_active = TRUE
         AND fr.status = 'accepted'
         ORDER BY rr.last_accessed DESC
         LIMIT ?`,
        [requestingUid, requestingUid, roomType, limit]
      );

      return rows;
    } catch (error) {
      console.error('Error getting friend rooms:', error);
      return [];
    }
  }

  /**
   * Clean up inactive rooms (for maintenance)
   */
  async cleanupInactiveRooms(olderThanHours: number = 24): Promise<number> {
    try {
      const [result] = await pool.query(
        `UPDATE room_registry
         SET is_active = FALSE
         WHERE is_active = TRUE
         AND last_accessed < DATE_SUB(NOW(), INTERVAL ? HOUR)`,
        [olderThanHours]
      );

      return (result as any).affectedRows || 0;
    } catch (error) {
      console.error('Error cleaning up inactive rooms:', error);
      return 0;
    }
  }

  /**
   * Update room last accessed time
   */
  async updateRoomLastAccessed(roomId: string): Promise<void> {
    try {
      await pool.query(
        'UPDATE room_registry SET last_accessed = NOW() WHERE room_id = ?',
        [roomId]
      );
    } catch (error) {
      console.error('Error updating room last accessed:', error);
    }
  }

  /**
   * Get room statistics
   */
  async getRoomStatistics(): Promise<{
    totalRooms: number;
    activeRooms: number;
    roomsByType: Record<RoomType, number>;
    roomsByAccess: Record<AccessLevel, number>;
  }> {
    try {
      const [totalRows] = await pool.query<RowDataPacket[]>(
        'SELECT COUNT(*) as total FROM room_registry'
      );

      const [activeRows] = await pool.query<RowDataPacket[]>(
        'SELECT COUNT(*) as active FROM room_registry WHERE is_active = TRUE'
      );

      const [typeRows] = await pool.query<RowDataPacket[]>(
        `SELECT room_type, COUNT(*) as count
         FROM room_registry
         WHERE is_active = TRUE
         GROUP BY room_type`
      );

      const [accessRows] = await pool.query<RowDataPacket[]>(
        `SELECT access_level, COUNT(*) as count
         FROM room_registry
         WHERE is_active = TRUE
         GROUP BY access_level`
      );

      const roomsByType: Record<RoomType, number> = {
        home: 0,
        garden: 0,
        quest: 0
      };

      const roomsByAccess: Record<AccessLevel, number> = {
        private: 0,
        friends_only: 0,
        public: 0
      };

      typeRows.forEach(row => {
        roomsByType[row.room_type as RoomType] = row.count;
      });

      accessRows.forEach(row => {
        roomsByAccess[row.access_level as AccessLevel] = row.count;
      });

      return {
        totalRooms: totalRows[0].total,
        activeRooms: activeRows[0].active,
        roomsByType,
        roomsByAccess
      };
    } catch (error) {
      console.error('Error getting room statistics:', error);
      return {
        totalRooms: 0,
        activeRooms: 0,
        roomsByType: { home: 0, garden: 0, quest: 0 },
        roomsByAccess: { private: 0, friends_only: 0, public: 0 }
      };
    }
  }
}

export default RoomMatchingService.getInstance();
