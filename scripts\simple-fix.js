const mysql = require('mysql2/promise');
require('dotenv').config();

async function simpleFix() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🔧 Adding missing columns to user_inventory...');
    
    // Add columns one by one
    const alterStatements = [
      "ALTER TABLE user_inventory ADD COLUMN is_locked BOOLEAN NOT NULL DEFAULT FALSE",
      "ALTER TABLE user_inventory ADD COLUMN is_new BOOLEAN NOT NULL DEFAULT TRUE", 
      "ALTER TABLE user_inventory ADD COLUMN is_equipped BOOLEAN NOT NULL DEFAULT FALSE",
      "ALTER TABLE user_inventory ADD COLUMN custom_data JSON NULL"
    ];
    
    for (const statement of alterStatements) {
      try {
        await connection.query(statement);
        console.log('✅ Added column successfully');
      } catch (error) {
        if (error.message.includes('Duplicate column')) {
          console.log('⚠️  Column already exists');
        } else {
          console.log('❌ Error:', error.message);
        }
      }
    }
    
    console.log('🎉 Done!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await connection.end();
  }
}

simpleFix();
