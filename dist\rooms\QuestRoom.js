"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestRoom = void 0;
const core_1 = require("@colyseus/core");
const QuestRoomState_1 = require("./schema/QuestRoomState");
const QuestState_1 = require("../models/QuestState");
const QuestService_1 = __importDefault(require("../services/QuestService"));
const errors_1 = require("../errors");
/**
 * Room with integrated quest system
 */
class QuestRoom extends core_1.Room {
    constructor() {
        super(...arguments);
        this.maxClients = 10;
        this.state = new QuestRoomState_1.QuestRoomState();
    }
    onCreate(options) {
        console.log('QuestRoom created with options:', options);
        // Handle quest actions
        this.onMessage('quest_action', async (client, message) => {
            try {
                await this.handleQuestAction(client, message);
            }
            catch (error) {
                console.error('Quest action error:', error);
                if (error instanceof errors_1.QuestError) {
                    client.send('quest_error', error.toColyseusError());
                }
                else {
                    client.send('quest_error', {
                        code: 500,
                        message: 'Internal quest system error'
                    });
                }
            }
        });
        // Handle player movement
        this.onMessage('player_movement', (client, message) => {
            try {
                this.handlePlayerMovement(client, message);
            }
            catch (error) {
                console.error('Player movement error:', error);
                client.send('movement_error', {
                    code: 400,
                    message: 'Invalid movement data'
                });
            }
        });
        // Handle player outfit update
        this.onMessage('update_outfit', (client, outfit) => {
            try {
                this.state.updatePlayerOutfit(client.sessionId, outfit);
            }
            catch (error) {
                console.error('Outfit update error:', error);
            }
        });
        // Handle NPC interaction
        this.onMessage('npc_interaction', async (client, message) => {
            try {
                await this.handleNpcInteraction(client, message);
            }
            catch (error) {
                console.error('NPC interaction error:', error);
                if (error instanceof errors_1.QuestError) {
                    client.send('npc_interaction_error', error.toColyseusError());
                }
            }
        });
        // Set up periodic quest progress sync
        this.clock.setInterval(() => {
            this.syncQuestProgress();
        }, 30000); // Sync every 30 seconds
    }
    async onJoin(client, options) {
        console.log(`${client.sessionId} joined QuestRoom`);
        // Validate required options
        if (!options.uid) {
            throw new Error('User ID is required to join quest room');
        }
        // Add player to room state
        const player = this.state.addPlayer(client.sessionId, options.uid, options.x || 0, options.y || 0);
        // Load player's quest progress from database
        try {
            await this.loadPlayerQuests(options.uid);
            // Send initial quest data to client
            const playerQuests = await QuestService_1.default.getPlayerQuests(options.uid);
            client.send('quest_data_loaded', {
                quests: playerQuests,
                availableQuests: QuestService_1.default.getAllQuests()
            });
        }
        catch (error) {
            console.error('Failed to load player quests:', error);
            client.send('quest_load_error', {
                code: 500,
                message: 'Failed to load quest data'
            });
        }
        // Send welcome message
        client.send('room_joined', {
            sessionId: client.sessionId,
            roomStats: this.state.getRoomStats()
        });
    }
    async onLeave(client, consented) {
        console.log(`${client.sessionId} left QuestRoom (consented: ${consented})`);
        const player = this.state.getPlayer(client.sessionId);
        if (player) {
            // Save player's quest progress to database
            try {
                await this.savePlayerQuests(player.uid);
            }
            catch (error) {
                console.error('Failed to save player quests on leave:', error);
            }
            // Remove player from room state
            this.state.removePlayer(client.sessionId);
        }
    }
    onDispose() {
        console.log('QuestRoom disposing...');
    }
    /**
     * Handle quest-related actions
     */
    async handleQuestAction(client, message) {
        const player = this.state.getPlayer(client.sessionId);
        if (!player) {
            throw new errors_1.QuestError('Player not found in room');
        }
        switch (message.action) {
            case 'start_quest':
                if (!message.questId) {
                    throw errors_1.QuestError.invalidQuestData('Quest ID is required');
                }
                await this.startPlayerQuest(player.uid, message.questId);
                client.send('quest_started', { questId: message.questId });
                break;
            case 'update_progress':
                if (!message.questId || typeof message.stepOrder !== 'number') {
                    throw errors_1.QuestError.invalidQuestData('Quest ID and step order are required');
                }
                await this.updatePlayerQuestProgress(player.uid, message.questId, message.stepOrder, message.progressData || {});
                client.send('quest_progress_updated', {
                    questId: message.questId,
                    stepOrder: message.stepOrder
                });
                break;
            case 'complete_step':
                if (!message.questId || typeof message.stepOrder !== 'number') {
                    throw errors_1.QuestError.invalidQuestData('Quest ID and step order are required');
                }
                await this.completeQuestStep(player.uid, message.questId, message.stepOrder, message.progressData || {});
                client.send('quest_step_completed', {
                    questId: message.questId,
                    stepOrder: message.stepOrder
                });
                break;
            case 'get_npc_response':
                if (!message.questId || typeof message.stepOrder !== 'number' || !message.npcId || !message.responseType) {
                    throw errors_1.QuestError.invalidQuestData('All parameters are required for NPC response');
                }
                const response = QuestService_1.default.getNpcResponse(message.questId, message.stepOrder, message.npcId, message.responseType);
                client.send('npc_response', response);
                break;
            default:
                throw errors_1.QuestError.invalidQuestAction(message.action, message.questId || 0);
        }
    }
    /**
     * Handle player movement
     */
    handlePlayerMovement(client, message) {
        if (typeof message.x !== 'number' || typeof message.y !== 'number') {
            throw new Error('Invalid movement coordinates');
        }
        this.state.updatePlayerPosition(client.sessionId, message.x, message.y, message.dir);
        if (message.animation) {
            this.state.updatePlayerAnimation(client.sessionId, message.animation, message.isFlipped);
        }
        if (typeof message.isSitting === 'boolean') {
            this.state.updatePlayerSitting(client.sessionId, message.isSitting);
        }
    }
    /**
     * Handle NPC interaction
     */
    async handleNpcInteraction(client, message) {
        const player = this.state.getPlayer(client.sessionId);
        if (!player) {
            throw new errors_1.QuestError('Player not found in room');
        }
        // If quest-related interaction
        if (message.questId && typeof message.stepOrder === 'number') {
            const playerQuest = this.state.questSystem.getPlayerQuest(player.uid, message.questId);
            if (!playerQuest) {
                throw errors_1.QuestError.questNotStarted(message.questId);
            }
            const responseType = playerQuest.isStepCompleted(message.stepOrder) ? 'finished' : 'unfinished';
            const response = QuestService_1.default.getNpcResponse(message.questId, message.stepOrder, message.npcId, responseType);
            client.send('npc_dialogue', {
                npcId: message.npcId,
                questId: message.questId,
                stepOrder: message.stepOrder,
                response: response
            });
        }
        else {
            // General NPC interaction
            client.send('npc_dialogue', {
                npcId: message.npcId,
                message: 'Hello! How can I help you today?'
            });
        }
    }
    /**
     * Load player quests from database into room state
     */
    async loadPlayerQuests(playerId) {
        const playerQuests = await QuestService_1.default.getPlayerQuests(playerId);
        for (const quest of playerQuests) {
            const questState = new QuestState_1.PlayerQuestState(quest.quest_id, quest.current_step_order);
            questState.isCompleted = quest.is_completed;
            questState.startedAt = quest.started_at.getTime();
            if (quest.completed_at) {
                questState.completedAt = quest.completed_at.getTime();
            }
            // Parse step progress
            if (quest.step_progress) {
                const stepProgress = typeof quest.step_progress === 'string'
                    ? JSON.parse(quest.step_progress)
                    : quest.step_progress;
                for (const [stepKey, progress] of Object.entries(stepProgress)) {
                    const stepOrder = parseInt(stepKey.replace('step_', ''));
                    if (!isNaN(stepOrder)) {
                        questState.setStepProgress(stepOrder, progress.isCompleted || false, progress);
                    }
                }
            }
            this.state.questSystem.setPlayerQuest(playerId, questState);
        }
    }
    /**
     * Save player quests from room state to database
     */
    async savePlayerQuests(playerId) {
        // This would typically be handled by the QuestService
        // For now, we'll just log that we should save
        console.log(`Should save quest progress for player ${playerId}`);
    }
    /**
     * Start a quest for a player
     */
    async startPlayerQuest(playerId, questId) {
        const playerQuest = await QuestService_1.default.startQuest(playerId, questId);
        // Update room state
        const questState = new QuestState_1.PlayerQuestState(questId, 0);
        questState.startedAt = playerQuest.started_at.getTime();
        this.state.questSystem.setPlayerQuest(playerId, questState);
    }
    /**
     * Update quest progress
     */
    async updatePlayerQuestProgress(playerId, questId, stepOrder, progressData) {
        await QuestService_1.default.updateQuestProgress(playerId, questId, stepOrder, progressData, false);
        // Update room state
        const questState = this.state.questSystem.getPlayerQuest(playerId, questId);
        if (questState) {
            questState.setStepProgress(stepOrder, false, progressData);
        }
    }
    /**
     * Complete a quest step
     */
    async completeQuestStep(playerId, questId, stepOrder, progressData) {
        const updatedQuest = await QuestService_1.default.updateQuestProgress(playerId, questId, stepOrder, progressData, true);
        // Update room state
        const questState = this.state.questSystem.getPlayerQuest(playerId, questId);
        if (questState) {
            questState.setStepProgress(stepOrder, true, progressData);
            questState.currentStepOrder = updatedQuest.current_step_order;
            if (updatedQuest.is_completed) {
                questState.complete();
            }
        }
    }
    /**
     * Sync quest progress periodically
     */
    async syncQuestProgress() {
        // This could be used to periodically sync quest progress
        // For now, we'll just log
        console.log('Syncing quest progress for all players...');
    }
}
exports.QuestRoom = QuestRoom;
