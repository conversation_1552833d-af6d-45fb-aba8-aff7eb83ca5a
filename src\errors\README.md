# 错误处理系统文档

本项目实现了一套结构化的自定义错误类系统，提供统一的错误处理机制。

## 错误类层次结构

```
BaseError (抽象基类)
├── AuthenticationError (认证错误)
│   ├── InvalidCredentialsError
│   ├── TokenExpiredError
│   ├── InvalidTokenError
│   ├── MissingTokenError
│   └── TokenRevokedError
├── ValidationError (验证错误)
│   ├── MissingFieldError
│   ├── InvalidFieldTypeError
│   ├── InvalidFieldValueError
│   ├── FieldLengthError
│   └── InvalidFormatError
├── BusinessLogicError (业务逻辑错误)
│   ├── NotFoundError
│   ├── ConflictError
│   ├── PermissionDeniedError
│   ├── RateLimitError
│   ├── InsufficientResourcesError
│   ├── OperationNotAllowedError
│   └── InvalidStateError
├── DatabaseError (数据库错误)
│   ├── DatabaseConnectionError
│   ├── DatabaseQueryError
│   ├── DatabaseTransactionError
│   ├── DatabaseConstraintError
│   └── DatabaseTimeoutError
└── GameError (游戏特定错误)
    ├── RoomError
    │   ├── RoomNotFoundError
    │   ├── RoomFullError
    │   ├── RoomAccessDeniedError
    │   └── InvalidRoomStateError
    ├── PlayerError
    │   ├── PlayerNotFoundError
    │   └── InvalidPlayerActionError
    ├── ItemError
    │   ├── ItemNotFoundError
    │   └── InvalidItemPlacementError
    └── CharacterError
        ├── CharacterNotFoundError
        └── CharacterNameExistsError
```

## 基本用法

### 1. 在服务中抛出错误

```typescript
import { NotFoundError, ValidationError, PermissionDeniedError } from '../errors';

class UserService {
  async getUserById(id: string) {
    if (!id) {
      throw new MissingFieldError('id');
    }
    
    const user = await this.findUser(id);
    if (!user) {
      throw new NotFoundError('User', id);
    }
    
    return user;
  }
  
  async updateUser(id: string, data: any, currentUserId: string) {
    if (id !== currentUserId) {
      throw new PermissionDeniedError('update user', 'User');
    }
    
    // 更新逻辑...
  }
}
```

### 2. 在Express路由中使用

```typescript
import { asyncHandler } from '../middleware/errorHandler';
import { MissingFieldError, InvalidFieldTypeError } from '../errors';

// 使用 asyncHandler 包装异步路由处理器
router.post('/users', asyncHandler(async (req, res) => {
  const { username, email } = req.body;
  
  if (!username) {
    throw new MissingFieldError('username');
  }
  
  if (typeof username !== 'string') {
    throw new InvalidFieldTypeError('username', 'string', typeof username);
  }
  
  const user = await userService.createUser({ username, email });
  res.status(201).json({ success: true, user });
}));
```

### 3. 在Colyseus房间中使用

```typescript
import { ColyseusErrorHandler } from '../middleware/errorHandler';
import { PermissionDeniedError, ValidationError } from '../errors';

export class GameRoom extends Room {
  registerMessageHandlers() {
    this.onMessage("action", async (client, message) => {
      const result = await ColyseusErrorHandler.safeAsync(client, async () => {
        // 权限检查
        if (!this.hasPermission(client, 'action')) {
          throw new PermissionDeniedError('perform action');
        }
        
        // 验证消息
        if (!message.data) {
          throw new MissingFieldError('data');
        }
        
        // 执行操作
        return await this.performAction(message.data);
      }, 'Failed to perform action');
      
      if (result) {
        client.send('action_success', result);
      }
    });
  }
}
```

## 验证工具

使用内置的验证工具来简化常见的验证操作：

```typescript
import { Validators } from '../utils/validators';

// 验证必需字段
Validators.required(value, 'fieldName');

// 验证类型
Validators.type(value, 'string', 'fieldName');

// 验证字符串长度
Validators.stringLength(value, 'username', 3, 20);

// 验证数字范围
Validators.numberRange(value, 'age', 0, 120);

// 验证枚举值
Validators.oneOf(value, ['option1', 'option2'], 'status');

// 验证邮箱格式
Validators.email(value);

// 验证用户名格式
Validators.username(value);

// 验证坐标
Validators.coordinates(x, y);
```

## 错误工厂

使用错误工厂来创建常见的错误类型：

```typescript
import { ErrorFactory } from '../errors';

// 创建认证错误
const authError = ErrorFactory.createAuthError('token_expired');

// 创建验证错误
const validationError = ErrorFactory.createMissingFieldError('username');

// 创建未找到错误
const notFoundError = ErrorFactory.createNotFoundError('User', userId);

// 创建权限错误
const permissionError = ErrorFactory.createPermissionError('delete', 'User');
```

## 错误响应格式

### HTTP API响应格式

```json
{
  "success": false,
  "error": {
    "name": "ValidationError",
    "message": "Required field 'username' is missing",
    "code": 400,
    "timestamp": "2023-12-07T10:30:00.000Z",
    "details": {
      "field": "username"
    }
  }
}
```

### Colyseus错误格式

```json
{
  "code": 400,
  "message": "Required field 'username' is missing",
  "details": {
    "field": "username"
  }
}
```

## 最佳实践

1. **使用具体的错误类型**：优先使用具体的错误类型而不是通用的错误类型
2. **提供有用的错误信息**：错误消息应该清晰地描述问题和可能的解决方案
3. **包含相关详情**：在错误详情中包含有助于调试的信息
4. **使用asyncHandler**：在Express路由中始终使用asyncHandler包装异步处理器
5. **使用ColyseusErrorHandler**：在Colyseus房间中使用safeAsync方法处理异步操作
6. **记录错误**：重要的错误应该被记录到日志中
7. **不要暴露敏感信息**：确保错误消息不包含敏感的系统信息

## 扩展错误系统

要添加新的错误类型，请遵循以下步骤：

1. 创建新的错误类，继承自适当的基类
2. 在 `src/errors/index.ts` 中导出新的错误类
3. 如果需要，在ErrorFactory中添加工厂方法
4. 更新文档

```typescript
// 示例：创建新的游戏特定错误
export class InventoryFullError extends GameError {
  constructor(maxSlots: number) {
    super(`Inventory is full (max ${maxSlots} slots)`, 400, { maxSlots });
    this.name = 'InventoryFullError';
  }
}
```
