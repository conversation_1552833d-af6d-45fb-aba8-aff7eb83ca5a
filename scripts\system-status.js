const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkSystemStatus() {
  console.log('🔍 系统状态检查报告');
  console.log('=' .repeat(50));
  
  try {
    // 数据库连接检查
    console.log('\n📊 数据库连接状态:');
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: Number(process.env.DB_PORT) || 3306,
    });
    console.log('✅ 数据库连接正常');

    // 检查关键表
    console.log('\n📋 关键数据表检查:');
    const requiredTables = [
      'users', 'user_auth_tokens', 'user_currencies', 'user_inventory',
      'items', 'shop_categories', 'shop_items', 'quests', 'quest_steps'
    ];
    
    const [tables] = await connection.query('SHOW TABLES');
    const existingTables = tables.map(table => Object.values(table)[0]);
    
    requiredTables.forEach(tableName => {
      if (existingTables.includes(tableName)) {
        console.log(`✅ ${tableName}`);
      } else {
        console.log(`❌ ${tableName} (缺失)`);
      }
    });

    // 检查用户数据
    console.log('\n👥 用户数据统计:');
    const [userCount] = await connection.query('SELECT COUNT(*) as count FROM users');
    console.log(`📊 总用户数: ${userCount[0].count}`);
    
    const [currencyCount] = await connection.query('SELECT COUNT(*) as count FROM user_currencies');
    console.log(`💰 有货币记录的用户: ${currencyCount[0].count}`);

    // 检查物品数据
    console.log('\n🎒 物品数据统计:');
    const [itemCount] = await connection.query('SELECT COUNT(*) as count FROM items');
    console.log(`📦 物品模板数: ${itemCount[0].count}`);
    
    const [inventoryCount] = await connection.query('SELECT COUNT(*) as count FROM user_inventory');
    console.log(`🎯 用户库存记录: ${inventoryCount[0].count}`);

    // 检查任务数据
    console.log('\n🎯 任务系统统计:');
    const [questCount] = await connection.query('SELECT COUNT(*) as count FROM quests');
    console.log(`📋 任务数量: ${questCount[0].count}`);
    
    const [questStepCount] = await connection.query('SELECT COUNT(*) as count FROM quest_steps');
    console.log(`🔗 任务步骤数: ${questStepCount[0].count}`);

    // 检查商店数据
    console.log('\n🛍️ 商店系统统计:');
    const [categoryCount] = await connection.query('SELECT COUNT(*) as count FROM shop_categories');
    console.log(`📂 商店分类数: ${categoryCount[0].count}`);
    
    const [shopItemCount] = await connection.query('SELECT COUNT(*) as count FROM shop_items');
    console.log(`🛒 商店物品数: ${shopItemCount[0].count}`);

    // 检查user_inventory表结构
    console.log('\n🔧 user_inventory 表结构检查:');
    const [columns] = await connection.query('DESCRIBE user_inventory');
    const columnNames = columns.map(col => col.Field);
    const requiredColumns = ['is_locked', 'is_new', 'is_equipped', 'custom_data'];
    
    requiredColumns.forEach(colName => {
      if (columnNames.includes(colName)) {
        console.log(`✅ ${colName} 字段存在`);
      } else {
        console.log(`❌ ${colName} 字段缺失`);
      }
    });

    await connection.end();
    
    console.log('\n🎉 系统状态检查完成!');
    console.log('=' .repeat(50));
    
  } catch (error) {
    console.error('❌ 系统检查失败:', error.message);
  }
}

checkSystemStatus();
