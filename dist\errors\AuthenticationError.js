"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenRevokedError = exports.MissingTokenError = exports.InvalidTokenError = exports.TokenExpiredError = exports.InvalidCredentialsError = exports.AuthenticationError = void 0;
const BaseError_1 = require("./BaseError");
/**
 * Authentication related errors
 */
class AuthenticationError extends BaseError_1.BaseError {
    constructor(message = 'Authentication failed', details) {
        super('AuthenticationError', 401, message, true, details);
    }
}
exports.AuthenticationError = AuthenticationError;
/**
 * Invalid credentials error
 */
class InvalidCredentialsError extends AuthenticationError {
    constructor(message = 'Invalid username or password') {
        super(message);
        this.name = 'InvalidCredentialsError';
    }
}
exports.InvalidCredentialsError = InvalidCredentialsError;
/**
 * Token expired error
 */
class TokenExpiredError extends AuthenticationError {
    constructor(message = 'Token has expired') {
        super(message);
        this.name = 'TokenExpiredError';
    }
}
exports.TokenExpiredError = TokenExpiredError;
/**
 * Invalid token error
 */
class InvalidTokenError extends AuthenticationError {
    constructor(message = 'Invalid or malformed token') {
        super(message);
        this.name = 'InvalidTokenError';
    }
}
exports.InvalidTokenError = InvalidTokenError;
/**
 * Missing token error
 */
class MissingTokenError extends AuthenticationError {
    constructor(message = 'Authentication token is required') {
        super(message);
        this.name = 'MissingTokenError';
    }
}
exports.MissingTokenError = MissingTokenError;
/**
 * Token revoked error
 */
class TokenRevokedError extends AuthenticationError {
    constructor(message = 'Token has been revoked') {
        super(message);
        this.name = 'TokenRevokedError';
    }
}
exports.TokenRevokedError = TokenRevokedError;
