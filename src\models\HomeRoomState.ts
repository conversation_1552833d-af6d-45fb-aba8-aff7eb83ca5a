import { Schema, MapSchema, type } from '@colyseus/schema';
import { PlayerState } from './PlayerState'; // Assuming PlayerState is in the same directory or adjust path
import { HomeItemState } from './HomeItemState'; // Assuming HomeItemState is in the same directory

export class HomeRoomState extends Schema {
  @type('string') ownerUid: string = ''; // UID of the player who owns this home
  @type('string') homeBackgroundId: string = 'default_background'; // Identifier for the home's background
  
  @type({ map: PlayerState })
  players = new MapSchema<PlayerState>();
  
  @type({ map: HomeItemState })
  furniture = new MapSchema<HomeItemState>(); // Furniture and other items placed in the home

  @type('string') accessLevel: 'private' | 'friends_only' | 'public' = 'private'; // Who can access this home

  constructor(ownerUid: string, accessLevel: 'private' | 'friends_only' | 'public' = 'private', homeBackgroundId: string = 'default_background') {
    super();
    this.ownerUid = ownerUid;
    this.accessLevel = accessLevel;
    this.homeBackgroundId = homeBackgroundId;
  }
}
