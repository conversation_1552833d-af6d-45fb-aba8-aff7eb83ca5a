// Simple test script to verify SpaceService behavior
const path = require('path');
require('ts-node').register({
  project: path.join(__dirname, '..', 'tsconfig.json')
});

const SpaceService = require('../src/services/SpaceService').default;

async function testSpaceService() {
  const testUserId = '2';
  
  try {
    console.log('🧪 Testing SpaceService methods...');
    
    // Test 1: Get full private space data
    console.log('\n1️⃣ Testing getFullPrivateSpaceData...');
    const spaceData = await SpaceService.getFullPrivateSpaceData(testUserId);
    console.log(`   Found ${spaceData.homeItems.length} home items`);
    console.log(`   Found ${spaceData.gardenPlots.length} garden plots`);
    console.log(`   Settings: ${JSON.stringify(spaceData.settings, null, 2)}`);
    
    if (spaceData.homeItems.length > 0) {
      const firstItem = spaceData.homeItems[0];
      console.log(`   First item: ${firstItem.item_instance_id} - ${firstItem.item_template_id}`);
      
      // Test 2: Update home item details
      console.log('\n2️⃣ Testing updateHomeItemDetails...');
      const updateResult = await SpaceService.updateHomeItemDetails(
        testUserId,
        firstItem.item_instance_id,
        {
          pos_x: firstItem.pos_x + 10,
          pos_y: firstItem.pos_y + 10,
          rotation: 90
        }
      );
      
      console.log(`   Update result: ${JSON.stringify(updateResult)}`);
      console.log(`   Success type: ${typeof updateResult.success}`);
      console.log(`   Success value: ${updateResult.success}`);
      console.log(`   Success === true: ${updateResult.success === true}`);
      console.log(`   Success == true: ${updateResult.success == true}`);
      
      // Test 3: Verify the update
      console.log('\n3️⃣ Testing data retrieval after update...');
      const updatedSpaceData = await SpaceService.getFullPrivateSpaceData(testUserId);
      const updatedItem = updatedSpaceData.homeItems.find(item => item.item_instance_id === firstItem.item_instance_id);
      
      if (updatedItem) {
        console.log(`   Updated item position: (${updatedItem.pos_x}, ${updatedItem.pos_y})`);
        console.log(`   Updated item rotation: ${updatedItem.rotation}`);
      } else {
        console.log('   ❌ Updated item not found!');
      }
    } else {
      console.log('   ⚠️  No home items found for testing');
    }
    
    console.log('\n✅ SpaceService test completed');
    
  } catch (error) {
    console.error('❌ SpaceService test failed:', error);
  }
}

testSpaceService();
