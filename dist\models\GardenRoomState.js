"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GardenRoomState = void 0;
const schema_1 = require("@colyseus/schema");
const PlayerState_1 = require("./PlayerState"); // Assuming PlayerState is in the same directory or adjust path
const GardenPlotState_1 = require("./GardenPlotState"); // Assuming GardenPlotState is in the same directory
class GardenRoomState extends schema_1.Schema {
    constructor(ownerUid, accessLevel = 'private', gardenBackgroundId = 'default_garden_background') {
        super();
        this.ownerUid = ''; // UID of the player who owns this garden
        this.gardenBackgroundId = 'default_garden_background'; // Identifier for the garden's background
        this.players = new schema_1.MapSchema();
        this.plots = new schema_1.MapSchema(); // Plots in the garden
        this.accessLevel = 'private'; // Who can access this garden
        this.ownerUid = ownerUid;
        this.accessLevel = accessLevel;
        this.gardenBackgroundId = gardenBackgroundId;
    }
}
exports.GardenRoomState = GardenRoomState;
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], GardenRoomState.prototype, "ownerUid", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], GardenRoomState.prototype, "gardenBackgroundId", void 0);
__decorate([
    (0, schema_1.type)({ map: PlayerState_1.PlayerState }),
    __metadata("design:type", Object)
], GardenRoomState.prototype, "players", void 0);
__decorate([
    (0, schema_1.type)({ map: GardenPlotState_1.GardenPlotState }),
    __metadata("design:type", Object)
], GardenRoomState.prototype, "plots", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], GardenRoomState.prototype, "accessLevel", void 0);
