# Shop System Documentation

## Overview

The Shop System is a comprehensive e-commerce solution for the 2D Virtual World game server. It supports multiple currencies, inventory management, wishlist functionality, and real-time synchronization between clients and the server.

## Features

- **Dual Currency System**: Gold coins (basic currency) and diamonds (premium currency)
- **Multi-Category Shop**: Organized shop with categories and subcategories
- **Inventory Management**: Persistent player inventory with item stacking
- **Wishlist System**: Players can save items for later purchase
- **Purchase History**: Complete transaction tracking
- **Limited Time Items**: Time-based availability for special items
- **Featured Items**: Highlighted promotional items
- **Gift System**: Players can purchase items for friends
- **Purchase Limits**: Per-user purchase restrictions for exclusive items
- **Stock Management**: Limited stock tracking for rare items
- **Search Functionality**: Find items by name, description, or tags

## Database Schema

### Core Tables

1. **user_currencies**: Player currency balances (gold coins, diamonds)
2. **shop_categories**: Shop category hierarchy
3. **shop_items**: Shop item listings with pricing and availability
4. **user_inventory**: Player inventory items
5. **purchase_history**: Transaction history
6. **user_purchase_limits**: Purchase limit tracking
7. **wishlist**: Player wishlist items
8. **promotions**: Sales and discount system (future enhancement)

### Setup

1. Run the main schema:
```sql
-- Execute the shop tables from virtualwrld_strict_schema_final.sql
```

2. Insert sample data:
```sql
-- Execute shop_sample_data.sql for example shop items
```

## API Endpoints

### Shop Information

- `GET /api/shop/categories` - Get all shop categories
- `GET /api/shop/categories/:categoryId/items` - Get items by category
- `GET /api/shop/featured` - Get featured items
- `GET /api/shop/limited-time` - Get limited time items
- `GET /api/shop/search?q=query` - Search shop items
- `GET /api/shop/items/:itemId` - Get specific shop item details

### Purchase System

- `GET /api/shop/items/:itemId/can-purchase/:userId` - Check purchase eligibility
- `POST /api/shop/purchase` - Purchase an item
- `GET /api/shop/users/:userId/purchases` - Get purchase history

### Currency & Inventory

- `GET /api/shop/users/:userId/currency` - Get user currency balance
- `GET /api/shop/users/:userId/inventory` - Get user inventory
- `GET /api/shop/users/:userId/inventory/stats` - Get inventory statistics

### Wishlist

- `GET /api/shop/users/:userId/wishlist` - Get user wishlist
- `POST /api/shop/users/:userId/wishlist` - Add item to wishlist
- `DELETE /api/shop/users/:userId/wishlist/:shopItemId` - Remove item from wishlist
- `GET /api/shop/users/:userId/wishlist/stats` - Get wishlist statistics
- `GET /api/shop/users/:userId/wishlist/expiring` - Get expiring wishlist items

### Admin

- `POST /api/shop/admin/refresh-cache` - Refresh shop cache

## Usage Examples

### Getting Shop Data

```javascript
// Get all categories
const categories = await fetch('/api/shop/categories').then(r => r.json());

// Get featured items
const featured = await fetch('/api/shop/featured').then(r => r.json());

// Search for items
const results = await fetch('/api/shop/search?q=dress').then(r => r.json());
```

### Currency Management

```javascript
// Get user currency
const currency = await fetch('/api/shop/users/player123/currency').then(r => r.json());
console.log(`Gold: ${currency.data.gold_coins}, Diamonds: ${currency.data.diamonds}`);
```

### Making Purchases

```javascript
// Check if user can purchase
const eligibility = await fetch('/api/shop/items/1/can-purchase/player123').then(r => r.json());

if (eligibility.data.canPurchase) {
  // Make purchase
  const purchase = await fetch('/api/shop/purchase', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      userId: 'player123',
      shopItemId: 1,
      quantity: 1
    })
  }).then(r => r.json());
  
  console.log('Purchase successful:', purchase.data);
}
```

### Inventory Management

```javascript
// Get user inventory
const inventory = await fetch('/api/shop/users/player123/inventory').then(r => r.json());

// Get inventory by type
const clothing = await fetch('/api/shop/users/player123/inventory?type=clothing').then(r => r.json());

// Get inventory statistics
const stats = await fetch('/api/shop/users/player123/inventory/stats').then(r => r.json());
```

### Wishlist Operations

```javascript
// Add item to wishlist
await fetch('/api/shop/users/player123/wishlist', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ shopItemId: 1 })
});

// Get wishlist
const wishlist = await fetch('/api/shop/users/player123/wishlist').then(r => r.json());

// Remove item from wishlist
await fetch('/api/shop/users/player123/wishlist/1', { method: 'DELETE' });
```

### Gift System

```javascript
// Purchase item as gift
const gift = await fetch('/api/shop/purchase', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: 'sender123',
    shopItemId: 1,
    quantity: 1,
    giftRecipientId: 'recipient456'
  })
}).then(r => r.json());
```

## Service Layer Architecture

### CurrencyService

Manages player currency operations:
- Get/set currency balances
- Deduct/add currency with transaction safety
- Transfer currency between players
- Batch currency operations

### InventoryService

Handles player inventory:
- Add/remove items with stacking support
- Check item availability
- Transfer items between players
- Inventory statistics and filtering

### ShopService

Core shop functionality:
- Shop item and category management
- Purchase eligibility checking
- Transaction processing
- Search and filtering

### WishlistService

Wishlist management:
- Add/remove wishlist items
- Track expiring items
- Wishlist statistics

## Error Handling

The shop system includes comprehensive error handling:

### Common Error Types

- **InsufficientCurrency**: Not enough gold/diamonds
- **ItemNotAvailable**: Item out of stock or expired
- **PurchaseLimitExceeded**: User reached purchase limit
- **ItemNotFound**: Invalid shop item ID
- **WishlistItemExists**: Item already in wishlist

### Error Response Format

```json
{
  "success": false,
  "error": {
    "name": "ShopError",
    "message": "Insufficient gold coins. Required: 1000, Available: 500",
    "code": 400,
    "details": {
      "currencyType": "gold coins",
      "required": 1000,
      "available": 500
    }
  }
}
```

## Shop Categories

The system supports hierarchical categories:

### Main Categories

1. **花之屋 (Flower House)** - Clothing and accessories
   - 上装 (Tops)
   - 下装 (Bottoms) 
   - 连衣裙 (Dresses)
   - 发型 (Hairstyles)
   - 鞋履 (Shoes)
   - 翅膀 (Wings)
   - 套装 (Outfits)

2. **家园居 (Home Garden)** - Furniture and decorations
   - 室内家具 (Indoor Furniture)
   - 园艺装饰 (Garden Decorations)
   - 墙纸地板 (Wallpaper & Flooring)
   - 主题系列 (Themed Sets)

3. **奇妙坊 (Wonder Workshop)** - Seeds and tools
   - 普通种子 (Common Seeds)
   - 稀有种子 (Rare Seeds)
   - 成长道具 (Growth Tools)

4. **惊喜阁 (Surprise Pavilion)** - Limited time and special items

## Testing

Run the shop system tests:

```bash
npm run test-shop
```

Run the client example:

```bash
npm install axios  # If not already installed
node examples/shop-client-example.js
```

## Performance Considerations

1. **Shop Cache**: Categories and items are cached in memory
2. **Database Optimization**: Indexes on frequently queried fields
3. **Transaction Safety**: All purchases use database transactions
4. **Batch Operations**: Support for bulk inventory/currency operations

## Future Enhancements

- **Promotion System**: Discounts and sales events
- **Auction House**: Player-to-player trading
- **Subscription Items**: Monthly/weekly item deliveries
- **Crafting Integration**: Use inventory items for crafting
- **Achievement Rewards**: Unlock items through achievements
- **Seasonal Events**: Time-limited themed shops
- **VIP System**: Special pricing for premium members
