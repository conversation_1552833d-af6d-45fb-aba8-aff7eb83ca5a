import pool from '../utils/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';
import { QuestError } from '../errors';

/**
 * Quest template interface
 */
export interface Quest extends RowDataPacket {
  id: number;
  title: string;
  accept_message_0?: string;
  accept_message_1?: string;
  accept_message_2?: string;
  decline_message_0?: string;
  reward_message_0_0?: string;
  reward_message_0_1?: string;
  created_at: Date;
  updated_at: Date;
}

/**
 * Quest step template interface
 */
export interface QuestStep extends RowDataPacket {
  id: number;
  quest_id: number;
  step_order: number;
  goal: string;
  summary?: string;
  intro_0?: string;
  intro_1?: string;
  intro_2?: string;
  intro_3?: string;
  intro_4?: string;
  created_at: Date;
  updated_at: Date;
}

/**
 * Quest step response interface
 */
export interface QuestStepResponse extends RowDataPacket {
  id: number;
  step_id: number;
  npc_id: number;
  response_type: 'finished' | 'unfinished';
  prompt?: string;
  message_0?: string;
  message_1?: string;
  message_2?: string;
  created_at: Date;
  updated_at: Date;
}

/**
 * Player quest progress interface
 */
export interface PlayerQuest extends RowDataPacket {
  player_id: string;
  quest_id: number;
  current_step_order: number;
  is_completed: boolean;
  step_progress?: any; // JSON data
  started_at: Date;
  completed_at?: Date;
  updated_at: Date;
}

/**
 * Quest service for managing quest system
 */
class QuestService {
  private static instance: QuestService;
  private questCache = new Map<number, Quest>();
  private questStepsCache = new Map<number, QuestStep[]>();
  private questResponsesCache = new Map<number, QuestStepResponse[]>();

  private constructor() {
    // Initialize cache
    this.loadQuestCache();
  }

  public static getInstance(): QuestService {
    if (!QuestService.instance) {
      QuestService.instance = new QuestService();
    }
    return QuestService.instance;
  }

  /**
   * Load quest templates into cache
   */
  private async loadQuestCache(): Promise<void> {
    try {
      // Load quests
      const [quests] = await pool.query<Quest[]>('SELECT * FROM quests');
      quests.forEach(quest => {
        this.questCache.set(quest.id, quest);
      });

      // Load quest steps
      const [steps] = await pool.query<QuestStep[]>(
        'SELECT * FROM quest_steps ORDER BY quest_id, step_order'
      );
      const stepsByQuest = new Map<number, QuestStep[]>();
      steps.forEach(step => {
        if (!stepsByQuest.has(step.quest_id)) {
          stepsByQuest.set(step.quest_id, []);
        }
        stepsByQuest.get(step.quest_id)!.push(step);
      });
      this.questStepsCache = stepsByQuest;

      // Load quest step responses
      const [responses] = await pool.query<QuestStepResponse[]>(
        'SELECT qsr.* FROM quest_step_responses qsr ' +
        'JOIN quest_steps qs ON qsr.step_id = qs.id ' +
        'ORDER BY qs.quest_id, qs.step_order, qsr.npc_id, qsr.response_type'
      );
      const responsesByQuest = new Map<number, QuestStepResponse[]>();
      responses.forEach(response => {
        // We need to get the quest_id from the step
        const step = steps.find(s => s.id === response.step_id);
        if (step) {
          if (!responsesByQuest.has(step.quest_id)) {
            responsesByQuest.set(step.quest_id, []);
          }
          responsesByQuest.get(step.quest_id)!.push(response);
        }
      });
      this.questResponsesCache = responsesByQuest;

      console.log(`Loaded ${quests.length} quests, ${steps.length} steps, ${responses.length} responses into cache`);
    } catch (error) {
      console.error('Failed to load quest cache:', error);
    }
  }

  /**
   * Get quest by ID
   */
  getQuest(questId: number): Quest | undefined {
    return this.questCache.get(questId);
  }

  /**
   * Get all quests
   */
  getAllQuests(): Quest[] {
    return Array.from(this.questCache.values());
  }

  /**
   * Get quest steps by quest ID
   */
  getQuestSteps(questId: number): QuestStep[] {
    return this.questStepsCache.get(questId) || [];
  }

  /**
   * Get quest step by quest ID and step order
   */
  getQuestStep(questId: number, stepOrder: number): QuestStep | undefined {
    const steps = this.getQuestSteps(questId);
    return steps.find(step => step.step_order === stepOrder);
  }

  /**
   * Get quest step responses by quest ID
   */
  getQuestStepResponses(questId: number): QuestStepResponse[] {
    return this.questResponsesCache.get(questId) || [];
  }

  /**
   * Get NPC response for a specific step
   */
  getNpcResponse(questId: number, stepOrder: number, npcId: number, responseType: 'finished' | 'unfinished'): QuestStepResponse | undefined {
    const responses = this.getQuestStepResponses(questId);
    const step = this.getQuestStep(questId, stepOrder);
    
    if (!step) return undefined;

    return responses.find(response => 
      response.step_id === step.id && 
      response.npc_id === npcId && 
      response.response_type === responseType
    );
  }

  /**
   * Get player quest progress
   */
  async getPlayerQuest(playerId: string, questId: number): Promise<PlayerQuest | null> {
    try {
      const [rows] = await pool.query<PlayerQuest[]>(
        'SELECT * FROM player_quests WHERE player_id = ? AND quest_id = ?',
        [playerId, questId]
      );
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('Failed to get player quest:', error);
      throw new QuestError('Failed to retrieve player quest progress');
    }
  }

  /**
   * Get all player quests
   */
  async getPlayerQuests(playerId: string): Promise<PlayerQuest[]> {
    try {
      const [rows] = await pool.query<PlayerQuest[]>(
        'SELECT * FROM player_quests WHERE player_id = ? ORDER BY started_at DESC',
        [playerId]
      );
      return rows;
    } catch (error) {
      console.error('Failed to get player quests:', error);
      throw new QuestError('Failed to retrieve player quests');
    }
  }

  /**
   * Start a quest for a player
   */
  async startQuest(playerId: string, questId: number): Promise<PlayerQuest> {
    // Validate quest exists
    const quest = this.getQuest(questId);
    if (!quest) {
      throw QuestError.questNotFound(questId);
    }

    // Check if quest already started
    const existingQuest = await this.getPlayerQuest(playerId, questId);
    if (existingQuest) {
      throw QuestError.questAlreadyStarted(questId);
    }

    try {
      const [result] = await pool.query<ResultSetHeader>(
        'INSERT INTO player_quests (player_id, quest_id, current_step_order, step_progress) VALUES (?, ?, 0, ?)',
        [playerId, questId, JSON.stringify({})]
      );

      // Return the created quest
      const newQuest = await this.getPlayerQuest(playerId, questId);
      if (!newQuest) {
        throw new QuestError('Failed to create player quest');
      }

      return newQuest;
    } catch (error) {
      console.error('Failed to start quest:', error);
      throw new QuestError('Failed to start quest');
    }
  }

  /**
   * Update quest progress
   */
  async updateQuestProgress(
    playerId: string, 
    questId: number, 
    stepOrder: number, 
    stepProgress: any = {},
    isStepCompleted: boolean = false
  ): Promise<PlayerQuest> {
    const playerQuest = await this.getPlayerQuest(playerId, questId);
    if (!playerQuest) {
      throw QuestError.questNotStarted(questId);
    }

    if (playerQuest.is_completed) {
      throw QuestError.questAlreadyCompleted(questId);
    }

    // Validate step exists
    const step = this.getQuestStep(questId, stepOrder);
    if (!step) {
      throw QuestError.questStepNotFound(questId, stepOrder);
    }

    try {
      // Parse existing step progress
      let currentStepProgress = {};
      try {
        currentStepProgress = JSON.parse(playerQuest.step_progress || '{}');
      } catch (e) {
        currentStepProgress = {};
      }

      // Update step progress
      (currentStepProgress as any)[`step_${stepOrder}`] = {
        ...stepProgress,
        isCompleted: isStepCompleted,
        updatedAt: Date.now()
      };

      // Update current step if this step is completed and it's the current step
      let newCurrentStep = playerQuest.current_step_order;
      if (isStepCompleted && stepOrder === playerQuest.current_step_order) {
        newCurrentStep = stepOrder + 1;
      }

      // Check if quest is completed (all steps completed)
      const questSteps = this.getQuestSteps(questId);
      const isQuestCompleted = questSteps.every(step => {
        const stepProgressData = (currentStepProgress as any)[`step_${step.step_order}`];
        return stepProgressData && stepProgressData.isCompleted;
      });

      const [result] = await pool.query<ResultSetHeader>(
        'UPDATE player_quests SET current_step_order = ?, step_progress = ?, is_completed = ?, completed_at = ? WHERE player_id = ? AND quest_id = ?',
        [
          newCurrentStep,
          JSON.stringify(currentStepProgress),
          isQuestCompleted,
          isQuestCompleted ? new Date() : null,
          playerId,
          questId
        ]
      );

      // Return updated quest
      const updatedQuest = await this.getPlayerQuest(playerId, questId);
      if (!updatedQuest) {
        throw new QuestError('Failed to retrieve updated quest');
      }

      return updatedQuest;
    } catch (error) {
      console.error('Failed to update quest progress:', error);
      if (error instanceof QuestError) {
        throw error;
      }
      throw new QuestError('Failed to update quest progress');
    }
  }

  /**
   * Refresh quest cache
   */
  async refreshCache(): Promise<void> {
    this.questCache.clear();
    this.questStepsCache.clear();
    this.questResponsesCache.clear();
    await this.loadQuestCache();
  }
}

export default QuestService.getInstance();
