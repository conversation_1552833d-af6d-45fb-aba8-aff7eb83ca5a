const mysql = require('mysql2/promise');
const fs = require('fs');
require('dotenv').config();

async function fixInventoryTable() {
  let connection;
  
  try {
    console.log('🔗 Connecting to database...');
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: Number(process.env.DB_PORT) || 3306,
    });

    console.log('📄 Reading SQL fix file...');
    const sqlContent = fs.readFileSync('scripts/fix-user-inventory-table.sql', 'utf8');
    
    // Split SQL statements by semicolon and execute them one by one
    const statements = sqlContent.split(';').filter(stmt => stmt.trim().length > 0);
    
    console.log(`🔧 Executing ${statements.length} SQL statements...`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          await connection.query(statement);
          console.log(`✅ Statement ${i + 1}/${statements.length} executed successfully`);
        } catch (error) {
          if (error.message.includes('Duplicate column name')) {
            console.log(`⚠️  Column already exists (statement ${i + 1})`);
          } else {
            console.log(`⚠️  Statement ${i + 1} failed:`, error.message);
          }
        }
      }
    }
    
    console.log('🎉 Inventory table fix completed!');
    
    // Verify the table structure
    console.log('🔍 Verifying user_inventory table structure...');
    const [columns] = await connection.query('DESCRIBE user_inventory');
    console.log('📊 Current user_inventory columns:');
    columns.forEach(col => {
      console.log(`  - ${col.Field} (${col.Type}) ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
    });
    
    // Check if required columns exist
    const columnNames = columns.map(col => col.Field);
    const requiredColumns = ['is_locked', 'is_new', 'is_equipped', 'custom_data'];
    
    console.log('\n🔍 Checking required columns:');
    requiredColumns.forEach(colName => {
      if (columnNames.includes(colName)) {
        console.log(`✅ ${colName} exists`);
      } else {
        console.log(`❌ ${colName} missing`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

fixInventoryTable();
