"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const db_1 = __importDefault(require("../utils/db"));
const errors_1 = require("../errors");
/**
 * Quest service for managing quest system
 */
class QuestService {
    constructor() {
        this.questCache = new Map();
        this.questStepsCache = new Map();
        this.questResponsesCache = new Map();
        // Initialize cache
        this.loadQuestCache();
    }
    static getInstance() {
        if (!QuestService.instance) {
            QuestService.instance = new QuestService();
        }
        return QuestService.instance;
    }
    /**
     * Load quest templates into cache
     */
    async loadQuestCache() {
        try {
            // Load quests
            const [quests] = await db_1.default.query('SELECT * FROM quests');
            quests.forEach(quest => {
                this.questCache.set(quest.id, quest);
            });
            // Load quest steps
            const [steps] = await db_1.default.query('SELECT * FROM quest_steps ORDER BY quest_id, step_order');
            const stepsByQuest = new Map();
            steps.forEach(step => {
                if (!stepsByQuest.has(step.quest_id)) {
                    stepsByQuest.set(step.quest_id, []);
                }
                stepsByQuest.get(step.quest_id).push(step);
            });
            this.questStepsCache = stepsByQuest;
            // Load quest step responses
            const [responses] = await db_1.default.query('SELECT qsr.* FROM quest_step_responses qsr ' +
                'JOIN quest_steps qs ON qsr.step_id = qs.id ' +
                'ORDER BY qs.quest_id, qs.step_order, qsr.npc_id, qsr.response_type');
            const responsesByQuest = new Map();
            responses.forEach(response => {
                // We need to get the quest_id from the step
                const step = steps.find(s => s.id === response.step_id);
                if (step) {
                    if (!responsesByQuest.has(step.quest_id)) {
                        responsesByQuest.set(step.quest_id, []);
                    }
                    responsesByQuest.get(step.quest_id).push(response);
                }
            });
            this.questResponsesCache = responsesByQuest;
            console.log(`Loaded ${quests.length} quests, ${steps.length} steps, ${responses.length} responses into cache`);
        }
        catch (error) {
            console.error('Failed to load quest cache:', error);
        }
    }
    /**
     * Get quest by ID
     */
    getQuest(questId) {
        return this.questCache.get(questId);
    }
    /**
     * Get all quests
     */
    getAllQuests() {
        return Array.from(this.questCache.values());
    }
    /**
     * Get quest steps by quest ID
     */
    getQuestSteps(questId) {
        return this.questStepsCache.get(questId) || [];
    }
    /**
     * Get quest step by quest ID and step order
     */
    getQuestStep(questId, stepOrder) {
        const steps = this.getQuestSteps(questId);
        return steps.find(step => step.step_order === stepOrder);
    }
    /**
     * Get quest step responses by quest ID
     */
    getQuestStepResponses(questId) {
        return this.questResponsesCache.get(questId) || [];
    }
    /**
     * Get NPC response for a specific step
     */
    getNpcResponse(questId, stepOrder, npcId, responseType) {
        const responses = this.getQuestStepResponses(questId);
        const step = this.getQuestStep(questId, stepOrder);
        if (!step)
            return undefined;
        return responses.find(response => response.step_id === step.id &&
            response.npc_id === npcId &&
            response.response_type === responseType);
    }
    /**
     * Get player quest progress
     */
    async getPlayerQuest(playerId, questId) {
        try {
            const [rows] = await db_1.default.query('SELECT * FROM player_quests WHERE player_id = ? AND quest_id = ?', [playerId, questId]);
            return rows.length > 0 ? rows[0] : null;
        }
        catch (error) {
            console.error('Failed to get player quest:', error);
            throw new errors_1.QuestError('Failed to retrieve player quest progress');
        }
    }
    /**
     * Get all player quests
     */
    async getPlayerQuests(playerId) {
        try {
            const [rows] = await db_1.default.query('SELECT * FROM player_quests WHERE player_id = ? ORDER BY started_at DESC', [playerId]);
            return rows;
        }
        catch (error) {
            console.error('Failed to get player quests:', error);
            throw new errors_1.QuestError('Failed to retrieve player quests');
        }
    }
    /**
     * Start a quest for a player
     */
    async startQuest(playerId, questId) {
        // Validate quest exists
        const quest = this.getQuest(questId);
        if (!quest) {
            throw errors_1.QuestError.questNotFound(questId);
        }
        // Check if quest already started
        const existingQuest = await this.getPlayerQuest(playerId, questId);
        if (existingQuest) {
            throw errors_1.QuestError.questAlreadyStarted(questId);
        }
        try {
            const [result] = await db_1.default.query('INSERT INTO player_quests (player_id, quest_id, current_step_order, step_progress) VALUES (?, ?, 0, ?)', [playerId, questId, JSON.stringify({})]);
            // Return the created quest
            const newQuest = await this.getPlayerQuest(playerId, questId);
            if (!newQuest) {
                throw new errors_1.QuestError('Failed to create player quest');
            }
            return newQuest;
        }
        catch (error) {
            console.error('Failed to start quest:', error);
            throw new errors_1.QuestError('Failed to start quest');
        }
    }
    /**
     * Update quest progress
     */
    async updateQuestProgress(playerId, questId, stepOrder, stepProgress = {}, isStepCompleted = false) {
        const playerQuest = await this.getPlayerQuest(playerId, questId);
        if (!playerQuest) {
            throw errors_1.QuestError.questNotStarted(questId);
        }
        if (playerQuest.is_completed) {
            throw errors_1.QuestError.questAlreadyCompleted(questId);
        }
        // Validate step exists
        const step = this.getQuestStep(questId, stepOrder);
        if (!step) {
            throw errors_1.QuestError.questStepNotFound(questId, stepOrder);
        }
        try {
            // Parse existing step progress
            let currentStepProgress = {};
            try {
                currentStepProgress = JSON.parse(playerQuest.step_progress || '{}');
            }
            catch (e) {
                currentStepProgress = {};
            }
            // Update step progress
            currentStepProgress[`step_${stepOrder}`] = {
                ...stepProgress,
                isCompleted: isStepCompleted,
                updatedAt: Date.now()
            };
            // Update current step if this step is completed and it's the current step
            let newCurrentStep = playerQuest.current_step_order;
            if (isStepCompleted && stepOrder === playerQuest.current_step_order) {
                newCurrentStep = stepOrder + 1;
            }
            // Check if quest is completed (all steps completed)
            const questSteps = this.getQuestSteps(questId);
            const isQuestCompleted = questSteps.every(step => {
                const stepProgressData = currentStepProgress[`step_${step.step_order}`];
                return stepProgressData && stepProgressData.isCompleted;
            });
            const [result] = await db_1.default.query('UPDATE player_quests SET current_step_order = ?, step_progress = ?, is_completed = ?, completed_at = ? WHERE player_id = ? AND quest_id = ?', [
                newCurrentStep,
                JSON.stringify(currentStepProgress),
                isQuestCompleted,
                isQuestCompleted ? new Date() : null,
                playerId,
                questId
            ]);
            // Return updated quest
            const updatedQuest = await this.getPlayerQuest(playerId, questId);
            if (!updatedQuest) {
                throw new errors_1.QuestError('Failed to retrieve updated quest');
            }
            return updatedQuest;
        }
        catch (error) {
            console.error('Failed to update quest progress:', error);
            if (error instanceof errors_1.QuestError) {
                throw error;
            }
            throw new errors_1.QuestError('Failed to update quest progress');
        }
    }
    /**
     * Refresh quest cache
     */
    async refreshCache() {
        this.questCache.clear();
        this.questStepsCache.clear();
        this.questResponsesCache.clear();
        await this.loadQuestCache();
    }
}
exports.default = QuestService.getInstance();
