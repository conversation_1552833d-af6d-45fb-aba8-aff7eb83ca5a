const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function setupQuestSystem() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: Number(process.env.DB_PORT) || 3306,
    });

    console.log('Connected to database successfully');

    // Read and execute the main schema file
    const schemaPath = path.join(__dirname, '../sql/virtualwrld_strict_schema_final.sql');
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    
    // Split by semicolon and execute each statement
    const statements = schemaSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    console.log('Creating database tables...');
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement);
        } catch (error) {
          // Ignore "table already exists" errors
          if (!error.message.includes('already exists')) {
            console.warn('Warning executing statement:', error.message);
          }
        }
      }
    }

    // Read and execute the sample data file
    const sampleDataPath = path.join(__dirname, '../sql/quest_sample_data.sql');
    const sampleDataSQL = fs.readFileSync(sampleDataPath, 'utf8');
    
    // Split by semicolon and execute each statement
    const dataStatements = sampleDataSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    console.log('Inserting sample quest data...');
    for (const statement of dataStatements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement);
        } catch (error) {
          // Ignore duplicate entry errors
          if (!error.message.includes('Duplicate entry')) {
            console.warn('Warning executing data statement:', error.message);
          }
        }
      }
    }

    // Verify the setup
    console.log('Verifying quest system setup...');
    
    const [quests] = await connection.execute('SELECT COUNT(*) as count FROM quests');
    console.log(`✓ Quests table: ${quests[0].count} quests loaded`);
    
    const [steps] = await connection.execute('SELECT COUNT(*) as count FROM quest_steps');
    console.log(`✓ Quest steps table: ${steps[0].count} steps loaded`);
    
    const [responses] = await connection.execute('SELECT COUNT(*) as count FROM quest_step_responses');
    console.log(`✓ Quest responses table: ${responses[0].count} responses loaded`);
    
    const [playerQuests] = await connection.execute('SELECT COUNT(*) as count FROM player_quests');
    console.log(`✓ Player quests table: ${playerQuests[0].count} player quests`);

    console.log('\n🎉 Quest system setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Start the server: npm start');
    console.log('2. Connect to a quest room using the client');
    console.log('3. Test quest functionality using the API endpoints');
    console.log('\nAPI endpoints available at:');
    console.log('- GET /api/quests/quests - List all quests');
    console.log('- POST /api/quests/players/{playerId}/quests/{questId}/start - Start a quest');
    console.log('- PUT /api/quests/players/{playerId}/quests/{questId}/progress - Update progress');

  } catch (error) {
    console.error('Error setting up quest system:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Check if required environment variables are set
const requiredEnvVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingEnvVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  console.error('\nPlease create a .env file with the required database configuration.');
  console.error('Example .env file:');
  console.error('DB_HOST=localhost');
  console.error('DB_USER=your_username');
  console.error('DB_PASSWORD=your_password');
  console.error('DB_NAME=your_database');
  console.error('DB_PORT=3306');
  process.exit(1);
}

// Run the setup
setupQuestSystem();
