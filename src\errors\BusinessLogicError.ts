import { BaseError } from './BaseError';

/**
 * Business logic related errors
 */
export class BusinessLogicError extends BaseError {
  constructor(message: string, httpCode: number = 400, details?: any) {
    super('BusinessLogicError', httpCode, message, true, details);
  }
}

/**
 * Resource not found error
 */
export class NotFoundError extends BaseError {
  constructor(resource: string, identifier?: string | number) {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;
    super('NotFoundError', 404, message, true, { resource, identifier });
  }
}

/**
 * Resource already exists error
 */
export class ConflictError extends BaseError {
  constructor(resource: string, field?: string, value?: any) {
    let message = `${resource} already exists`;
    if (field && value) {
      message += ` with ${field}: '${value}'`;
    }
    super('ConflictError', 409, message, true, { resource, field, value });
  }
}

/**
 * Permission denied error
 */
export class PermissionDeniedError extends BaseError {
  constructor(action: string, resource?: string) {
    const message = resource 
      ? `Permission denied: Cannot ${action} ${resource}`
      : `Permission denied: Cannot ${action}`;
    super('PermissionDeniedError', 403, message, true, { action, resource });
  }
}

/**
 * Rate limit exceeded error
 */
export class RateLimitError extends BaseError {
  constructor(limit: number, timeWindow: string) {
    const message = `Rate limit exceeded: ${limit} requests per ${timeWindow}`;
    super('RateLimitError', 429, message, true, { limit, timeWindow });
  }
}

/**
 * Insufficient resources error (e.g., not enough coins, items)
 */
export class InsufficientResourcesError extends BusinessLogicError {
  constructor(resource: string, required: number, available: number) {
    const message = `Insufficient ${resource}: required ${required}, available ${available}`;
    super(message, 400, { resource, required, available });
    this.name = 'InsufficientResourcesError';
  }
}

/**
 * Operation not allowed error
 */
export class OperationNotAllowedError extends BusinessLogicError {
  constructor(operation: string, reason: string) {
    const message = `Operation '${operation}' not allowed: ${reason}`;
    super(message, 400, { operation, reason });
    this.name = 'OperationNotAllowedError';
  }
}

/**
 * Invalid state error
 */
export class InvalidStateError extends BusinessLogicError {
  constructor(currentState: string, requiredState: string, resource?: string) {
    let message = `Invalid state: current '${currentState}', required '${requiredState}'`;
    if (resource) {
      message = `${resource} ${message}`;
    }
    super(message, 400, { currentState, requiredState, resource });
    this.name = 'InvalidStateError';
  }
}
