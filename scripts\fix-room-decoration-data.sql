-- Fix Room Decoration System Data
-- This script adds missing furniture and background items needed for the room decoration system

-- Add basic furniture items
INSERT IGNORE INTO items (item_id, item_name, item_type, rarity, price_gold, price_diamonds, data, created_at, updated_at) VALUES
('basic_chair', 'Basic Chair', 'furniture', 'common', 100, 0, '{"category":"furniture","subcategory":"seating","placement":{"width":1,"height":1},"description":"A simple wooden chair for your home"}', NOW(), NOW()),
('wooden_table', 'Wooden Table', 'furniture', 'common', 200, 0, '{"category":"furniture","subcategory":"table","placement":{"width":2,"height":1},"description":"A sturdy wooden table"}', NOW(), NOW()),
('flower_pot', 'Flower Pot', 'furniture', 'common', 50, 0, '{"category":"furniture","subcategory":"decoration","placement":{"width":1,"height":1},"description":"A decorative flower pot"}', NOW(), NOW()),
('modern_sofa', 'Modern Sofa', 'furniture', 'uncommon', 500, 0, '{"category":"furniture","subcategory":"seating","placement":{"width":3,"height":1},"description":"A comfortable modern sofa"}', NOW(), NOW()),
('bookshelf', 'Bookshelf', 'furniture', 'uncommon', 300, 0, '{"category":"furniture","subcategory":"storage","placement":{"width":1,"height":2},"description":"A tall bookshelf for storing books"}', NOW(), NOW()),
('coffee_table', 'Coffee Table', 'furniture', 'common', 150, 0, '{"category":"furniture","subcategory":"table","placement":{"width":2,"height":1},"description":"A low coffee table"}', NOW(), NOW()),
('plant_large', 'Large Plant', 'furniture', 'common', 80, 0, '{"category":"furniture","subcategory":"decoration","placement":{"width":1,"height":1},"description":"A large decorative plant"}', NOW(), NOW()),
('desk_lamp', 'Desk Lamp', 'furniture', 'common', 75, 0, '{"category":"furniture","subcategory":"lighting","placement":{"width":1,"height":1},"description":"A modern desk lamp"}', NOW(), NOW()),
('wardrobe', 'Wardrobe', 'furniture', 'uncommon', 400, 0, '{"category":"furniture","subcategory":"storage","placement":{"width":2,"height":2},"description":"A large wardrobe for clothes"}', NOW(), NOW()),
('dining_chair', 'Dining Chair', 'furniture', 'common', 120, 0, '{"category":"furniture","subcategory":"seating","placement":{"width":1,"height":1},"description":"A comfortable dining chair"}', NOW(), NOW());

-- Add background items
INSERT IGNORE INTO items (item_id, item_name, item_type, rarity, price_gold, price_diamonds, data, created_at, updated_at) VALUES
('default_home_bg', 'Default Home Background', 'background', 'common', 0, 0, '{"allowed_scenes":["home"],"description":"The default home background"}', NOW(), NOW()),
('modern_home_bg', 'Modern Home Background', 'background', 'uncommon', 500, 0, '{"allowed_scenes":["home"],"description":"A sleek modern home interior"}', NOW(), NOW()),
('cozy_home_bg', 'Cozy Home Background', 'background', 'uncommon', 300, 0, '{"allowed_scenes":["home"],"description":"A warm and cozy home interior"}', NOW(), NOW()),
('luxury_home_bg', 'Luxury Home Background', 'background', 'rare', 1000, 50, '{"allowed_scenes":["home"],"description":"An elegant luxury home interior"}', NOW(), NOW()),
('default_garden_bg', 'Default Garden Background', 'background', 'common', 0, 0, '{"allowed_scenes":["garden"],"description":"The default garden background"}', NOW(), NOW()),
('garden_spring_bg', 'Spring Garden Background', 'background', 'uncommon', 400, 0, '{"allowed_scenes":["garden"],"description":"A beautiful spring garden scene"}', NOW(), NOW()),
('garden_summer_bg', 'Summer Garden Background', 'background', 'uncommon', 400, 0, '{"allowed_scenes":["garden"],"description":"A vibrant summer garden scene"}', NOW(), NOW()),
('garden_autumn_bg', 'Autumn Garden Background', 'background', 'uncommon', 400, 0, '{"allowed_scenes":["garden"],"description":"A colorful autumn garden scene"}', NOW(), NOW()),
('garden_winter_bg', 'Winter Garden Background', 'background', 'uncommon', 400, 0, '{"allowed_scenes":["garden"],"description":"A peaceful winter garden scene"}', NOW(), NOW());

-- Add some starter furniture to test user's inventory
INSERT IGNORE INTO user_inventory (user_id, item_id, quantity, source, created_at, updated_at) VALUES
(2, 'basic_chair', 5, 'starter_pack', NOW(), NOW()),
(2, 'wooden_table', 3, 'starter_pack', NOW(), NOW()),
(2, 'flower_pot', 10, 'starter_pack', NOW(), NOW()),
(2, 'coffee_table', 2, 'starter_pack', NOW(), NOW()),
(2, 'plant_large', 3, 'starter_pack', NOW(), NOW()),
(2, 'desk_lamp', 2, 'starter_pack', NOW(), NOW());

-- Update user_private_spaces table to allow NULL background_id temporarily
ALTER TABLE user_private_spaces MODIFY COLUMN home_background_id VARCHAR(50) NULL;
ALTER TABLE user_private_spaces MODIFY COLUMN garden_background_id VARCHAR(50) NULL;

-- Set default backgrounds for existing users (if any)
UPDATE user_private_spaces SET 
  home_background_id = 'default_home_bg',
  garden_background_id = 'default_garden_bg'
WHERE home_background_id IS NULL OR garden_background_id IS NULL;

-- Re-add foreign key constraints with proper references
ALTER TABLE user_private_spaces 
DROP FOREIGN KEY IF EXISTS user_private_spaces_ibfk_2,
DROP FOREIGN KEY IF EXISTS user_private_spaces_ibfk_3;

ALTER TABLE user_private_spaces 
ADD CONSTRAINT user_private_spaces_home_bg_fk 
FOREIGN KEY (home_background_id) REFERENCES items(item_id) ON DELETE SET NULL,
ADD CONSTRAINT user_private_spaces_garden_bg_fk 
FOREIGN KEY (garden_background_id) REFERENCES items(item_id) ON DELETE SET NULL;

-- Ensure test user exists in users table
INSERT IGNORE INTO users (id, username, email, password_hash, created_at, updated_at) VALUES
(2, 'test_user', '<EMAIL>', 'dummy_hash', NOW(), NOW());

-- Create default space settings for test user
INSERT IGNORE INTO user_private_spaces (
  owner_user_id, 
  home_background_id, 
  home_access_level, 
  garden_background_id, 
  garden_access_level,
  created_at,
  updated_at
) VALUES (
  2, 
  'default_home_bg', 
  'friends_only', 
  'default_garden_bg', 
  'private',
  NOW(),
  NOW()
);

-- Add some sample home items for testing
INSERT IGNORE INTO home_items (
  item_instance_id,
  owner_user_id,
  item_template_id,
  pos_x,
  pos_y,
  rotation,
  is_flipped,
  created_at,
  updated_at
) VALUES 
(UUID(), 2, 'basic_chair', 100, 100, 0, FALSE, NOW(), NOW()),
(UUID(), 2, 'wooden_table', 200, 150, 0, FALSE, NOW(), NOW()),
(UUID(), 2, 'flower_pot', 50, 50, 0, FALSE, NOW(), NOW());

SELECT 'Room decoration system data has been successfully added!' as status;
