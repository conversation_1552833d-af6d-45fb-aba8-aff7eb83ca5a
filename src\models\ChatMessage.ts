import { Schema, type } from '@colyseus/schema';

export class ChatMessage extends Schema {
  @type('string') senderSessionId: string = ''; // Client's sessionId who sent the message
  @type('string') senderUid: string = ''; // User ID of the sender
  @type('string') message: string = '';
  @type('number') timestamp: number = Date.now(); // Unix timestamp
  @type('string') channel: string = 'global'; // e.g., 'global', 'private', 'trade'

  constructor(senderSessionId: string, senderUid: string, message: string, channel: string = 'global') {
    super();
    this.senderSessionId = senderSessionId;
    this.senderUid = senderUid;
    this.message = message;
    this.channel = channel;
    this.timestamp = Date.now();
  }
}
