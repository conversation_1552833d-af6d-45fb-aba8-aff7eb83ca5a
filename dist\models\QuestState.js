"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestSystemState = exports.PlayerQuestState = exports.QuestStepProgress = void 0;
const schema_1 = require("@colyseus/schema");
/**
 * Represents the progress of a single quest step
 */
class QuestStepProgress extends schema_1.Schema {
    constructor(stepOrder = 0, isCompleted = false, progressData = {}) {
        super();
        this.stepOrder = 0;
        this.isCompleted = false;
        this.progressData = '{}'; // JSON string for step-specific progress
        this.stepOrder = stepOrder;
        this.isCompleted = isCompleted;
        this.progressData = JSON.stringify(progressData);
    }
    /**
     * Get progress data as parsed object
     */
    getProgressData() {
        try {
            return JSON.parse(this.progressData);
        }
        catch (error) {
            console.error('Failed to parse quest step progress data:', error);
            return {};
        }
    }
    /**
     * Set progress data from object
     */
    setProgressData(data) {
        this.progressData = JSON.stringify(data);
    }
}
exports.QuestStepProgress = QuestStepProgress;
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], QuestStepProgress.prototype, "stepOrder", void 0);
__decorate([
    (0, schema_1.type)('boolean'),
    __metadata("design:type", Boolean)
], QuestStepProgress.prototype, "isCompleted", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], QuestStepProgress.prototype, "progressData", void 0);
/**
 * Represents a player's quest state
 */
class PlayerQuestState extends schema_1.Schema {
    constructor(questId = 0, currentStepOrder = 0) {
        super();
        this.questId = 0;
        this.currentStepOrder = 0;
        this.isCompleted = false;
        this.startedAt = 0; // Unix timestamp
        this.completedAt = 0; // Unix timestamp
        this.stepProgress = new schema_1.MapSchema();
        this.questId = questId;
        this.currentStepOrder = currentStepOrder;
        this.startedAt = Date.now();
    }
    /**
     * Get progress for a specific step
     */
    getStepProgress(stepOrder) {
        return this.stepProgress.get(stepOrder.toString());
    }
    /**
     * Set progress for a specific step
     */
    setStepProgress(stepOrder, isCompleted, progressData = {}) {
        const stepKey = stepOrder.toString();
        let stepProgress = this.stepProgress.get(stepKey);
        if (!stepProgress) {
            stepProgress = new QuestStepProgress(stepOrder, isCompleted, progressData);
            this.stepProgress.set(stepKey, stepProgress);
        }
        else {
            stepProgress.isCompleted = isCompleted;
            stepProgress.setProgressData(progressData);
        }
    }
    /**
     * Mark quest as completed
     */
    complete() {
        this.isCompleted = true;
        this.completedAt = Date.now();
    }
    /**
     * Check if a specific step is completed
     */
    isStepCompleted(stepOrder) {
        const stepProgress = this.getStepProgress(stepOrder);
        return stepProgress ? stepProgress.isCompleted : false;
    }
    /**
     * Get all completed steps
     */
    getCompletedSteps() {
        const completedSteps = [];
        this.stepProgress.forEach((progress, key) => {
            if (progress.isCompleted) {
                completedSteps.push(progress.stepOrder);
            }
        });
        return completedSteps.sort((a, b) => a - b);
    }
}
exports.PlayerQuestState = PlayerQuestState;
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], PlayerQuestState.prototype, "questId", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], PlayerQuestState.prototype, "currentStepOrder", void 0);
__decorate([
    (0, schema_1.type)('boolean'),
    __metadata("design:type", Boolean)
], PlayerQuestState.prototype, "isCompleted", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], PlayerQuestState.prototype, "startedAt", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], PlayerQuestState.prototype, "completedAt", void 0);
__decorate([
    (0, schema_1.type)({ map: QuestStepProgress }),
    __metadata("design:type", Object)
], PlayerQuestState.prototype, "stepProgress", void 0);
/**
 * Represents the quest system state for a room
 */
class QuestSystemState extends schema_1.Schema {
    constructor() {
        super(...arguments);
        this.playerQuests = new schema_1.MapSchema();
    }
    /**
     * Get all quests for a specific player
     */
    getPlayerQuests(playerId) {
        const playerQuests = new Map();
        this.playerQuests.forEach((questState, key) => {
            if (key.startsWith(`${playerId}_`)) {
                playerQuests.set(questState.questId, questState);
            }
        });
        return playerQuests;
    }
    /**
     * Get a specific quest for a player
     */
    getPlayerQuest(playerId, questId) {
        return this.playerQuests.get(`${playerId}_${questId}`);
    }
    /**
     * Set a quest for a player
     */
    setPlayerQuest(playerId, questState) {
        this.playerQuests.set(`${playerId}_${questState.questId}`, questState);
    }
    /**
     * Remove a quest for a player
     */
    removePlayerQuest(playerId, questId) {
        this.playerQuests.delete(`${playerId}_${questId}`);
    }
    /**
     * Check if player has started a quest
     */
    hasPlayerStartedQuest(playerId, questId) {
        return this.playerQuests.has(`${playerId}_${questId}`);
    }
    /**
     * Get all active (not completed) quests for a player
     */
    getActivePlayerQuests(playerId) {
        const activeQuests = [];
        this.playerQuests.forEach((questState, key) => {
            if (key.startsWith(`${playerId}_`) && !questState.isCompleted) {
                activeQuests.push(questState);
            }
        });
        return activeQuests;
    }
    /**
     * Get all completed quests for a player
     */
    getCompletedPlayerQuests(playerId) {
        const completedQuests = [];
        this.playerQuests.forEach((questState, key) => {
            if (key.startsWith(`${playerId}_`) && questState.isCompleted) {
                completedQuests.push(questState);
            }
        });
        return completedQuests;
    }
}
exports.QuestSystemState = QuestSystemState;
__decorate([
    (0, schema_1.type)({ map: PlayerQuestState }),
    __metadata("design:type", Object)
], QuestSystemState.prototype, "playerQuests", void 0);
