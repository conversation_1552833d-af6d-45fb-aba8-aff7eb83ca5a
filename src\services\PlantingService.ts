import pool from '../utils/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';
import { ShopError } from '../errors';
import CurrencyService from './CurrencyService';
import InventoryService from './InventoryService';

/**
 * Garden Plot Interface
 */
export interface GardenPlot extends RowDataPacket {
  id: number;
  user_id: number;
  plot_index: number;
  is_unlocked: boolean;
  unlock_level_required: number;
  unlock_cost_gold: number;
  unlock_cost_diamonds: number;
  unlocked_at: Date | null;
  created_at: Date;
  updated_at: Date;
}

/**
 * Flower Seed Template Interface
 */
export interface FlowerSeed extends RowDataPacket {
  seed_id: string;
  seed_name: string;
  description: string | null;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  growth_time_minutes: number;
  required_level: number;
  base_yield: number;
  max_yield: number;
  water_intervals: number;
  fertilizer_boost: number;
  rare_drop_chance: number;
  seed_data: any;
  is_available: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Planted Flower Interface
 */
export interface PlantedFlower extends RowDataPacket {
  id: number;
  user_id: number;
  plot_id: number;
  seed_id: string;
  plant_stage: 'planted' | 'sprouting' | 'growing' | 'blooming' | 'mature' | 'withered';
  planted_at: Date;
  last_watered: Date | null;
  last_fertilized: Date | null;
  water_count: number;
  fertilizer_count: number;
  growth_boost: number;
  yield_boost: number;
  expected_harvest_time: Date;
  is_ready_for_harvest: boolean;
  needs_water: boolean;
  needs_care: boolean;
  plant_data: any;
  created_at: Date;
  updated_at: Date;
  // Joined fields from seed template
  seed_name?: string;
  seed_rarity?: string;
  base_yield?: number;
  max_yield?: number;
}

/**
 * User Planting Level Interface
 */
export interface UserPlantingLevel extends RowDataPacket {
  user_id: number;
  current_level: number;
  current_exp: number;
  total_plants: number;
  total_harvests: number;
  rare_harvests: number;
  created_at: Date;
  updated_at: Date;
}

/**
 * Planting Operation Result Interface
 */
export interface PlantingOperationResult {
  success: boolean;
  message: string;
  data?: any;
  exp_gained?: number;
  items_received?: any[];
  level_up?: boolean;
  new_level?: number;
}

/**
 * PlantingService - Handles all planting system operations
 */
class PlantingService {

  /**
   * Initialize user's garden with default plots
   */
  async initializeUserGarden(userId: string): Promise<GardenPlot[]> {
    try {
      const userIdNum = parseInt(userId);

      // Check if garden already exists
      const [existingPlots] = await pool.query<GardenPlot[]>(
        'SELECT * FROM garden_plots WHERE user_id = ?',
        [userIdNum]
      );

      if (existingPlots.length > 0) {
        return existingPlots;
      }

      // Create initial plots (first 4 are unlocked by default)
      const initialPlots = [];
      for (let i = 0; i < 12; i++) {
        const isUnlocked = i < 4; // First 4 plots are free
        const unlockCostGold = i < 4 ? 0 : Math.pow(2, i - 3) * 100; // Exponential cost
        const unlockCostDiamonds = i < 8 ? 0 : Math.floor((i - 7) * 5); // Diamond cost for premium plots

        await pool.query<ResultSetHeader>(
          `INSERT INTO garden_plots (user_id, plot_index, is_unlocked, unlock_level_required, unlock_cost_gold, unlock_cost_diamonds, unlocked_at)
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [userIdNum, i, isUnlocked, Math.ceil(i / 2) + 1, unlockCostGold, unlockCostDiamonds, isUnlocked ? new Date() : null]
        );
      }

      // Initialize user planting level
      await pool.query<ResultSetHeader>(
        `INSERT IGNORE INTO user_planting_levels (user_id, current_level, current_exp)
         VALUES (?, 1, 0)`,
        [userIdNum]
      );

      // Return created plots
      const [plots] = await pool.query<GardenPlot[]>(
        'SELECT * FROM garden_plots WHERE user_id = ? ORDER BY plot_index',
        [userIdNum]
      );

      return plots;
    } catch (error) {
      console.error('Failed to initialize user garden:', error);
      throw ShopError.inventoryOperationFailed('initialize_garden', 'Database error');
    }
  }

  /**
   * Get user's garden plots
   */
  async getUserGarden(userId: string): Promise<GardenPlot[]> {
    try {
      const userIdNum = parseInt(userId);

      const [plots] = await pool.query<GardenPlot[]>(
        'SELECT * FROM garden_plots WHERE user_id = ? ORDER BY plot_index',
        [userIdNum]
      );

      if (plots.length === 0) {
        // Initialize garden if it doesn't exist
        return await this.initializeUserGarden(userId);
      }

      return plots;
    } catch (error) {
      console.error('Failed to get user garden:', error);
      throw ShopError.inventoryOperationFailed('get_garden', 'Database error');
    }
  }

  /**
   * Get user's planted flowers with details
   */
  async getUserPlantedFlowers(userId: string): Promise<PlantedFlower[]> {
    try {
      const userIdNum = parseInt(userId);

      const [flowers] = await pool.query<PlantedFlower[]>(
        `SELECT
          pf.*,
          fs.seed_name,
          fs.rarity as seed_rarity,
          fs.base_yield,
          fs.max_yield
         FROM planted_flowers pf
         JOIN flower_seeds fs ON pf.seed_id = fs.seed_id
         WHERE pf.user_id = ?
         ORDER BY pf.planted_at DESC`,
        [userIdNum]
      );

      return flowers;
    } catch (error) {
      console.error('Failed to get user planted flowers:', error);
      throw ShopError.inventoryOperationFailed('get_planted_flowers', 'Database error');
    }
  }

  /**
   * Get available flower seeds
   */
  async getAvailableSeeds(userLevel?: number): Promise<FlowerSeed[]> {
    try {
      let query = 'SELECT * FROM flower_seeds WHERE is_available = TRUE';
      const params: any[] = [];

      if (userLevel !== undefined) {
        query += ' AND required_level <= ?';
        params.push(userLevel);
      }

      query += ' ORDER BY required_level, rarity, seed_name';

      const [seeds] = await pool.query<FlowerSeed[]>(query, params);
      return seeds;
    } catch (error) {
      console.error('Failed to get available seeds:', error);
      throw ShopError.inventoryOperationFailed('get_seeds', 'Database error');
    }
  }

  /**
   * Get user's planting level and stats
   */
  async getUserPlantingLevel(userId: string): Promise<UserPlantingLevel> {
    try {
      const userIdNum = parseInt(userId);

      const [rows] = await pool.query<UserPlantingLevel[]>(
        'SELECT * FROM user_planting_levels WHERE user_id = ?',
        [userIdNum]
      );

      if (rows.length === 0) {
        // Initialize if doesn't exist
        await pool.query<ResultSetHeader>(
          `INSERT INTO user_planting_levels (user_id, current_level, current_exp)
           VALUES (?, 1, 0)`,
          [userIdNum]
        );

        const [newRows] = await pool.query<UserPlantingLevel[]>(
          'SELECT * FROM user_planting_levels WHERE user_id = ?',
          [userIdNum]
        );

        return newRows[0];
      }

      return rows[0];
    } catch (error) {
      console.error('Failed to get user planting level:', error);
      throw ShopError.inventoryOperationFailed('get_planting_level', 'Database error');
    }
  }

  /**
   * Calculate experience required for next level
   */
  calculateExpForLevel(level: number): number {
    // Exponential growth: level^2 * 100
    return Math.pow(level, 2) * 100;
  }

  /**
   * Calculate experience gained for different operations
   */
  calculateExpGain(operation: string, seedRarity?: string): number {
    const baseExp: { [key: string]: number } = {
      plant: 10,
      water: 5,
      fertilize: 8,
      harvest: 15,
      unlock_plot: 50
    };

    const rarityMultiplier: { [key: string]: number } = {
      common: 1.0,
      uncommon: 1.2,
      rare: 1.5,
      epic: 2.0,
      legendary: 3.0
    };

    const base = baseExp[operation] || 0;
    const multiplier = seedRarity ? (rarityMultiplier[seedRarity] || 1.0) : 1.0;

    return Math.floor(base * multiplier);
  }

  /**
   * Add experience and check for level up
   */
  async addExperience(userId: string, expGain: number, operation: string): Promise<{ levelUp: boolean; newLevel?: number }> {
    try {
      const userIdNum = parseInt(userId);

      // Get current level data
      const currentLevel = await this.getUserPlantingLevel(userId);
      const newExp = currentLevel.current_exp + expGain;

      // Check for level up
      let newLevel = currentLevel.current_level;
      let levelUp = false;

      while (newExp >= this.calculateExpForLevel(newLevel)) {
        newLevel++;
        levelUp = true;
      }

      // Update database
      await pool.query<ResultSetHeader>(
        `UPDATE user_planting_levels
         SET current_exp = ?, current_level = ?, updated_at = CURRENT_TIMESTAMP
         WHERE user_id = ?`,
        [newExp, newLevel, userIdNum]
      );

      // Log the operation
      await this.logPlantingOperation(userId, operation, null, null, null, expGain);

      return { levelUp, newLevel: levelUp ? newLevel : undefined };
    } catch (error) {
      console.error('Failed to add experience:', error);
      throw ShopError.inventoryOperationFailed('add_experience', 'Database error');
    }
  }

  /**
   * Unlock a garden plot
   */
  async unlockPlot(userId: string, plotIndex: number): Promise<PlantingOperationResult> {
    try {
      const userIdNum = parseInt(userId);

      // Get plot info
      const [plots] = await pool.query<GardenPlot[]>(
        'SELECT * FROM garden_plots WHERE user_id = ? AND plot_index = ?',
        [userIdNum, plotIndex]
      );

      if (plots.length === 0) {
        throw ShopError.itemNotFound(`Plot ${plotIndex} not found`);
      }

      const plot = plots[0];

      if (plot.is_unlocked) {
        return {
          success: false,
          message: 'Plot is already unlocked'
        };
      }

      // Check level requirement
      const userLevel = await this.getUserPlantingLevel(userId);
      if (userLevel.current_level < plot.unlock_level_required) {
        return {
          success: false,
          message: `Requires planting level ${plot.unlock_level_required}`
        };
      }

      // Check currency requirements
      const hasEnoughCurrency = await CurrencyService.hasSufficientCurrency(userId, plot.unlock_cost_gold, plot.unlock_cost_diamonds);
      if (!hasEnoughCurrency) {
        return {
          success: false,
          message: `Insufficient currency. Required: ${plot.unlock_cost_gold} gold, ${plot.unlock_cost_diamonds} diamonds`
        };
      }

      // Deduct currency
      if (plot.unlock_cost_gold > 0 || plot.unlock_cost_diamonds > 0) {
        await CurrencyService.deductCurrency(userId, plot.unlock_cost_gold, plot.unlock_cost_diamonds, 'Plot unlock');
      }

      // Unlock the plot
      await pool.query<ResultSetHeader>(
        `UPDATE garden_plots
         SET is_unlocked = TRUE, unlocked_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [plot.id]
      );

      // Add experience
      const expGain = this.calculateExpGain('unlock_plot');
      const levelResult = await this.addExperience(userId, expGain, 'unlock_plot');

      return {
        success: true,
        message: `Plot ${plotIndex} unlocked successfully`,
        exp_gained: expGain,
        level_up: levelResult.levelUp,
        new_level: levelResult.newLevel
      };

    } catch (error: any) {
      console.error('Failed to unlock plot:', error);
      throw ShopError.inventoryOperationFailed('unlock_plot', error.message);
    }
  }

  /**
   * Log planting operation
   */
  async logPlantingOperation(
    userId: string,
    operationType: string,
    plotId?: number | null,
    plantedFlowerId?: number | null,
    seedId?: string | null,
    expGained: number = 0,
    itemsReceived?: any[] | null
  ): Promise<void> {
    try {
      const userIdNum = parseInt(userId);

      await pool.query<ResultSetHeader>(
        `INSERT INTO planting_operations_log
         (user_id, operation_type, plot_id, planted_flower_id, seed_id, exp_gained, items_received)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [userIdNum, operationType, plotId, plantedFlowerId, seedId, expGained, itemsReceived ? JSON.stringify(itemsReceived) : null]
      );
    } catch (error) {
      console.error('Failed to log planting operation:', error);
      // Don't throw here as it's just logging
    }
  }
}

export default new PlantingService();
