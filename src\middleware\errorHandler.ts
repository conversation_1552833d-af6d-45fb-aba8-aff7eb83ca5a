import { Request, Response, NextFunction } from 'express';
import { BaseError } from '../errors/BaseError';

/**
 * Global error handler middleware for Express
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Log the error
  console.error('Error occurred:', {
    name: error.name,
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString()
  });

  // Handle custom errors
  if (error instanceof BaseError) {
    res.status(error.httpCode).json(error.toJSON());
    return;
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    res.status(401).json({
      success: false,
      error: {
        name: 'InvalidTokenError',
        message: 'Invalid token format',
        code: 401,
        timestamp: new Date().toISOString()
      }
    });
    return;
  }

  if (error.name === 'TokenExpiredError') {
    res.status(401).json({
      success: false,
      error: {
        name: 'TokenExpiredError',
        message: 'Token has expired',
        code: 401,
        timestamp: new Date().toISOString()
      }
    });
    return;
  }

  // Handle MySQL/Database errors
  if (error.message.includes('ER_DUP_ENTRY')) {
    res.status(409).json({
      success: false,
      error: {
        name: 'ConflictError',
        message: 'Duplicate entry detected',
        code: 409,
        timestamp: new Date().toISOString()
      }
    });
    return;
  }

  if (error.message.includes('ER_NO_SUCH_TABLE')) {
    res.status(500).json({
      success: false,
      error: {
        name: 'DatabaseError',
        message: 'Database table not found',
        code: 500,
        timestamp: new Date().toISOString()
      }
    });
    return;
  }

  // Handle validation errors from express-validator
  if (error.name === 'ValidationError') {
    res.status(400).json({
      success: false,
      error: {
        name: 'ValidationError',
        message: error.message,
        code: 400,
        timestamp: new Date().toISOString()
      }
    });
    return;
  }

  // Default error response
  res.status(500).json({
    success: false,
    error: {
      name: 'InternalServerError',
      message: 'An unexpected error occurred',
      code: 500,
      timestamp: new Date().toISOString()
    }
  });
};

/**
 * Async error wrapper for route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404 Not Found handler
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  res.status(404).json({
    success: false,
    error: {
      name: 'NotFoundError',
      message: `Route ${req.method} ${req.url} not found`,
      code: 404,
      timestamp: new Date().toISOString()
    }
  });
};

/**
 * Colyseus error handler utility
 */
export class ColyseusErrorHandler {
  /**
   * Handle errors in Colyseus rooms
   */
  static handleRoomError(client: any, error: Error): void {
    console.error('Room error occurred:', {
      sessionId: client.sessionId,
      name: error.name,
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    if (error instanceof BaseError) {
      client.error(error.httpCode, error.message, error.details);
      return;
    }

    // Handle specific error types
    if (error.name === 'ValidationError') {
      client.error(400, error.message);
      return;
    }

    if (error.message.includes('permission') || error.message.includes('access')) {
      client.error(403, 'Permission denied');
      return;
    }

    if (error.message.includes('not found')) {
      client.error(404, 'Resource not found');
      return;
    }

    // Default error
    client.error(500, 'An unexpected error occurred');
  }

  /**
   * Safe async operation wrapper for Colyseus
   */
  static async safeAsync<T>(
    client: any,
    operation: () => Promise<T>,
    errorMessage: string = 'Operation failed'
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      console.error(`Safe async operation failed: ${errorMessage}`, error);
      this.handleRoomError(client, error as Error);
      return null;
    }
  }
}
