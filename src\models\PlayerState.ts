import { Schema, type } from '@colyseus/schema';
import { OutfitState } from './OutfitState';

export class PlayerState extends Schema {
  @type('string') sessionId: string = ''; // The client's sessionId
  @type('string') uid: string = ''; // User ID from your authentication system
  @type('number') x: number = 0;
  @type('number') y: number = 0;
  @type('string') dir: string = 'down'; // e.g., 'up', 'down', 'left', 'right'
  @type('boolean') isFlipped: boolean = false; // For sprite flipping
  @type('boolean') isSitting: boolean = false;
  @type('string') currentAnimation: string = 'idle'; // e.g., 'idle', 'walk', 'sit'

  // Outfit state using nested Colyseus Schema for better synchronization
  @type(OutfitState) currentOutfit: OutfitState = new OutfitState();

  constructor(sessionId: string, uid: string, x: number = 0, y: number = 0) {
    super();
    this.sessionId = sessionId;
    this.uid = uid;
    this.x = x;
    this.y = y;
    this.currentOutfit = new OutfitState(`outfit_${uid}_${Date.now()}`, 'Default Outfit');
  }

  /**
   * Update player position
   */
  updatePosition(x: number, y: number): void {
    this.x = x;
    this.y = y;
  }

  /**
   * Update player direction and flip state
   */
  updateDirection(dir: string, isFlipped?: boolean): void {
    this.dir = dir;
    if (isFlipped !== undefined) {
      this.isFlipped = isFlipped;
    }
  }

  /**
   * Update player animation
   */
  updateAnimation(animation: string): void {
    this.currentAnimation = animation;
  }

  /**
   * Set sitting state
   */
  setSitting(isSitting: boolean): void {
    this.isSitting = isSitting;
    if (isSitting) {
      this.currentAnimation = 'sit';
    } else {
      this.currentAnimation = 'idle';
    }
  }

  /**
   * Equip an outfit item
   */
  equipOutfitItem(slot: string, itemTemplateId: string, options: {
    itemId?: string;
    color?: string;
    layer?: number;
    subCategory?: string;
    customData?: any;
  } = {}): void {
    this.currentOutfit.equipItem(slot, itemTemplateId, options);
  }

  /**
   * Unequip an outfit item
   */
  unequipOutfitItem(slot: string): boolean {
    return this.currentOutfit.unequipItem(slot);
  }

  /**
   * Update outfit item properties
   */
  updateOutfitItem(slot: string, updates: Partial<{
    itemTemplateId: string;
    color: string;
    layer: number;
    isVisible: boolean;
    customData: any;
  }>): boolean {
    return this.currentOutfit.updateItem(slot, updates);
  }

  /**
   * Load outfit from saved data
   */
  loadOutfit(outfitData: any): void {
    this.currentOutfit.fromPlainObject(outfitData);
  }

  /**
   * Get outfit as plain object for saving
   */
  getOutfitData(): any {
    return this.currentOutfit.toPlainObject();
  }

  /**
   * Reset outfit to default
   */
  resetOutfitToDefault(): void {
    this.currentOutfit.resetToDefault();
  }

  /**
   * Copy outfit from another player
   */
  copyOutfitFrom(otherPlayer: PlayerState): void {
    this.currentOutfit.copyFrom(otherPlayer.currentOutfit);
  }

  /**
   * Get all equipped outfit items sorted by layer
   */
  getOutfitItemsSortedByLayer(): any[] {
    return this.currentOutfit.getItemsSortedByLayer().map(item => ({
      slot: Array.from(this.currentOutfit.items.keys()).find(key =>
        this.currentOutfit.items.get(key) === item
      ),
      itemId: item.itemId,
      itemTemplateId: item.itemTemplateId,
      category: item.category,
      subCategory: item.subCategory,
      color: item.color,
      layer: item.layer,
      isVisible: item.isVisible,
      customData: item.getCustomData()
    }));
  }

  /**
   * Validate current outfit
   */
  validateOutfit(): { isValid: boolean; errors: string[] } {
    return this.currentOutfit.validate();
  }
}
