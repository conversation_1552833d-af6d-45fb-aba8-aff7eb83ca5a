"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvalidFormatError = exports.FieldLengthError = exports.InvalidFieldValueError = exports.InvalidFieldTypeError = exports.MissingFieldError = exports.ValidationError = void 0;
const BaseError_1 = require("./BaseError");
/**
 * Validation related errors
 */
class ValidationError extends BaseError_1.BaseError {
    constructor(message = 'Validation failed', details) {
        super('ValidationError', 400, message, true, details);
    }
}
exports.ValidationError = ValidationError;
/**
 * Missing required field error
 */
class MissingFieldError extends ValidationError {
    constructor(fieldName, message) {
        const errorMessage = message || `Required field '${fieldName}' is missing`;
        super(errorMessage, { field: fieldName });
        this.name = 'MissingFieldError';
    }
}
exports.MissingFieldError = MissingFieldError;
/**
 * Invalid field type error
 */
class InvalidFieldTypeError extends ValidationError {
    constructor(fieldName, expectedType, actualType) {
        const message = `Field '${fieldName}' must be of type '${expectedType}', got '${actualType}'`;
        super(message, { field: fieldName, expectedType, actualType });
        this.name = 'InvalidFieldTypeError';
    }
}
exports.InvalidFieldTypeError = InvalidFieldTypeError;
/**
 * Invalid field value error
 */
class InvalidFieldValueError extends ValidationError {
    constructor(fieldName, value, allowedValues) {
        let message = `Invalid value '${value}' for field '${fieldName}'`;
        if (allowedValues) {
            message += `. Allowed values: ${allowedValues.join(', ')}`;
        }
        super(message, { field: fieldName, value, allowedValues });
        this.name = 'InvalidFieldValueError';
    }
}
exports.InvalidFieldValueError = InvalidFieldValueError;
/**
 * Field length validation error
 */
class FieldLengthError extends ValidationError {
    constructor(fieldName, actualLength, minLength, maxLength) {
        let message = `Field '${fieldName}' length is ${actualLength}`;
        if (minLength !== undefined && maxLength !== undefined) {
            message += `, must be between ${minLength} and ${maxLength} characters`;
        }
        else if (minLength !== undefined) {
            message += `, must be at least ${minLength} characters`;
        }
        else if (maxLength !== undefined) {
            message += `, must be at most ${maxLength} characters`;
        }
        super(message, { field: fieldName, actualLength, minLength, maxLength });
        this.name = 'FieldLengthError';
    }
}
exports.FieldLengthError = FieldLengthError;
/**
 * Invalid format error (e.g., email, phone number)
 */
class InvalidFormatError extends ValidationError {
    constructor(fieldName, format, value) {
        const message = `Field '${fieldName}' has invalid format. Expected: ${format}`;
        super(message, { field: fieldName, format, value });
        this.name = 'InvalidFormatError';
    }
}
exports.InvalidFormatError = InvalidFormatError;
