import { Router, Request, Response, NextFunction } from 'express';

interface AuthenticatedRequest extends Request {
  user?: { uid: string };
}
import InventoryService from '../services/InventoryService';
import authService from '../services/authService';
import { ShopError } from '../errors';

/**
 * Middleware to verify authentication token
 */
const verifyAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authorization token is required'
        }
      });
    }

    const userPayload = await authService.verifyToken(token);
    if (!userPayload || !userPayload.userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Invalid or expired token'
        }
      });
    }

    req.user = { uid: userPayload.userId.toString() };
    next();
  } catch (error: any) {
    console.error('Authentication error:', error);
    res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: 'Authentication failed'
      }
    });
  }
};

const router = Router();

/**
 * Get user's inventory with filtering, sorting, and pagination
 * GET /api/inventory
 */
router.get('/', verifyAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.uid;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    const {
      itemType,
      category,
      isEquipped,
      isNew,
      limit,
      offset,
      sortBy,
      sortOrder
    } = req.query;

    const options = {
      itemType: itemType as string,
      category: category as string,
      isEquipped: isEquipped === 'true' ? true : isEquipped === 'false' ? false : undefined,
      isNew: isNew === 'true' ? true : isNew === 'false' ? false : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined,
      sortBy: sortBy as 'acquired_at' | 'name' | 'quantity' | 'rarity',
      sortOrder: sortOrder as 'ASC' | 'DESC'
    };

    const inventory = await InventoryService.getUserInventory(userId, options);

    res.json({
      success: true,
      data: {
        inventory,
        count: inventory.length
      }
    });

  } catch (error: any) {
    console.error('Get inventory error:', error);
    if (error instanceof ShopError) {
      res.status(error.statusCode).json({
        success: false,
        error: {
          name: error.name,
          message: error.message,
          details: error.details
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get inventory'
        }
      });
    }
  }
});

/**
 * Get recent items
 * GET /api/inventory/recent
 */
router.get('/recent', verifyAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.uid;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'User not authenticated' }
      });
    }

    const { limit = 10 } = req.query;
    const recentItems = await InventoryService.getUserInventory(userId, {
      limit: parseInt(limit as string),
      sortBy: 'acquired_at',
      sortOrder: 'DESC'
    });

    res.json({
      success: true,
      data: recentItems
    });
  } catch (error) {
    console.error('Error fetching recent items:', error);
    res.status(500).json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Failed to fetch recent items' }
    });
  }
});

/**
 * Search inventory
 * GET /api/inventory/search
 */
router.get('/search', verifyAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.uid;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'User not authenticated' }
      });
    }

    const { query, itemType, category, limit = 50, offset = 0 } = req.query;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({
        success: false,
        error: { code: 'MISSING_QUERY', message: 'Search query is required' }
      });
    }

    const searchResults = await InventoryService.searchInventory(userId, {
      query: query as string,
      itemType: itemType as string,
      category: category as string,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string)
    });

    res.json({
      success: true,
      data: searchResults
    });
  } catch (error) {
    console.error('Error searching inventory:', error);
    res.status(500).json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Failed to search inventory' }
    });
  }
});

/**
 * Mark items as seen/not new
 * POST /api/inventory/mark-seen
 */
router.post('/mark-seen', verifyAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.uid;
    const { itemIds } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'User not authenticated' }
      });
    }

    if (!itemIds || !Array.isArray(itemIds)) {
      return res.status(400).json({
        success: false,
        error: { code: 'INVALID_ITEM_IDS', message: 'Item IDs array is required' }
      });
    }

    const results = await Promise.all(
      itemIds.map(itemId =>
        InventoryService.updateItem(userId, itemId, { isNew: false })
      )
    );

    res.json({
      success: true,
      data: { updated: results.length }
    });
  } catch (error) {
    console.error('Error marking items as seen:', error);
    res.status(500).json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Failed to mark items as seen' }
    });
  }
});

/**
 * Lock/unlock item
 * POST /api/inventory/toggle-lock
 */
router.post('/toggle-lock', verifyAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.uid;
    const { itemId, isLocked } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'User not authenticated' }
      });
    }

    if (!itemId || typeof isLocked !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: { code: 'INVALID_DATA', message: 'Item ID and lock status are required' }
      });
    }

    const result = await InventoryService.updateItem(userId, itemId, { isLocked });

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error toggling item lock:', error);
    res.status(500).json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Failed to toggle item lock' }
    });
  }
});

/**
 * Get specific item from user's inventory
 * GET /api/inventory/item/:itemId
 */
router.get('/item/:itemId', verifyAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.uid;
    const { itemId } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    const item = await InventoryService.getInventoryItem(userId, itemId);

    if (!item) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'ITEM_NOT_FOUND',
          message: 'Item not found in inventory'
        }
      });
    }

    res.json({
      success: true,
      data: { item }
    });

  } catch (error: any) {
    console.error('Error getting inventory item:', error);

    if (error instanceof ShopError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          name: error.name,
          message: error.message,
          details: error.details
        }
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to get inventory item'
      }
    });
  }
});

/**
 * Add item to user's inventory (admin/testing endpoint)
 * POST /api/inventory/add
 */
router.post('/add', verifyAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.uid;
    const { itemId, quantity = 1, source = 'admin_add', customData } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    if (!itemId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_ITEM_ID',
          message: 'Item ID is required'
        }
      });
    }

    const result = await InventoryService.addItem(userId, itemId, quantity, source, customData);

    res.json({
      success: true,
      data: result
    });

  } catch (error: any) {
    console.error('Error adding item to inventory:', error);

    if (error instanceof ShopError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          name: error.name,
          message: error.message,
          details: error.details
        }
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to add item to inventory'
      }
    });
  }
});

/**
 * Remove item from user's inventory
 * POST /api/inventory/remove
 */
router.post('/remove', verifyAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.uid;
    const { itemId, quantity = 1, operationType = 'remove', source = 'manual_remove' } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    if (!itemId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_ITEM_ID',
          message: 'Item ID is required'
        }
      });
    }

    const result = await InventoryService.removeItem(userId, itemId, quantity, operationType, source);

    res.json({
      success: true,
      data: result
    });

  } catch (error: any) {
    console.error('Error removing item from inventory:', error);

    if (error instanceof ShopError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          name: error.name,
          message: error.message,
          details: error.details
        }
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to remove item from inventory'
      }
    });
  }
});

/**
 * Update item properties in user's inventory
 * PUT /api/inventory/item/:itemId
 */
router.put('/item/:itemId', verifyAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.uid;
    const { itemId } = req.params;
    const { isLocked, isNew, isEquipped, customData } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    const updates = {
      isLocked,
      isNew,
      isEquipped,
      customData
    };

    // Remove undefined values
    Object.keys(updates).forEach(key => {
      if (updates[key as keyof typeof updates] === undefined) {
        delete updates[key as keyof typeof updates];
      }
    });

    const result = await InventoryService.updateItem(userId, itemId, updates);

    res.json({
      success: true,
      data: result
    });

  } catch (error: any) {
    console.error('Error updating inventory item:', error);

    if (error instanceof ShopError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          name: error.name,
          message: error.message,
          details: error.details
        }
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to update inventory item'
      }
    });
  }
});

/**
 * Get user's inventory operation history
 * GET /api/inventory/history
 */
router.get('/history', verifyAuth, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.uid;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    const {
      itemId,
      operationType,
      limit,
      offset
    } = req.query;

    const options = {
      itemId: itemId as string,
      operationType: operationType as any,
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined
    };

    const history = await InventoryService.getInventoryHistory(userId, options);

    res.json({
      success: true,
      data: {
        history,
        count: history.length
      }
    });

  } catch (error: any) {
    console.error('Error getting inventory history:', error);

    if (error instanceof ShopError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          name: error.name,
          message: error.message,
          details: error.details
        }
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to get inventory history'
      }
    });
  }
});

export default router;