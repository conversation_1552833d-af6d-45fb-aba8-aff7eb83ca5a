import http from 'http';
import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import { Server, LobbyRoom } from '@colyseus/core';
import { WebSocketTransport } from '@colyseus/ws-transport';
import { monitor } from '@colyseus/monitor';
import dotenv from 'dotenv';
import crypto from 'crypto';

import pool from './utils/db'; // Database connection pool
import authService from './services/authService'; // AuthService for token management
import { PublicLobbyRoom } from './rooms/PublicLobbyRoom'; // Your custom public lobby
import { HomeRoom } from './rooms/HomeRoom'; // Import HomeRoom
import { GardenRoom } from './rooms/GardenRoom'; // Import GardenRoom
import { QuestRoom } from './rooms/QuestRoom'; // Import QuestRoom
import { HomeRoomDynamic } from './rooms/HomeRoomDynamic'; // Import Dynamic HomeRoom
import { GardenRoomDynamic } from './rooms/GardenRoomDynamic'; // Import Dynamic GardenRoom
import { NotificationRoom } from './rooms/NotificationRoom'; // Import Notification Room
import NotificationService from './services/NotificationService'; // Import Notification Service
import friendRoutes from './routes/friendRoutes'; // Import friend routes
import characterRoutes from './routes/characterRoutes'; // Import character routes
import questRoutes from './routes/questRoutes'; // Import quest routes
import shopRoutes from './routes/shopRoutes'; // Import shop routes
import inventoryRoutes from './routes/inventoryRoutes'; // Import inventory routes
import roomRoutes from './routes/roomRoutes'; // Import room routes
import notificationRoutes from './routes/notificationRoutes'; // Import notification routes
import outfitRoutes from './routes/outfitRoutes'; // Import outfit routes

// Import error handling middleware
import { errorHandler, notFoundHandler } from './middleware/errorHandler';
import { RowDataPacket, ResultSetHeader } from 'mysql2';

dotenv.config();

const app = express();
const port = Number(process.env.PORT) || 2567;

app.use(cors());
app.use(express.json());

// Colyseus Game Server
const gameServer = new Server({
  transport: new WebSocketTransport({
    server: http.createServer(app)
  })
});

// Define a simple User interface (adjust as needed)
interface User extends RowDataPacket {
  id: number;
  username: string;
  password_hash: string;
  email?: string; // Optional email
  created_at?: Date;
  last_login?: Date;
}

// --- Authentication Routes ---

/**
 * @route POST /auth/register
 * @group Authentication - Operations for user registration and login
 * @summary Registers a new user.
 * @param {string} username.body.required - The desired username.
 * @param {string} password.body.required - The user's password (min 6 characters).
 * @param {string} email.body - The user's email address (optional).
 * @returns {object} 201 - User registered successfully, returns userId and token.
 * @returns {Error}  400 - Invalid input (e.g., missing fields, short password/username).
 * @returns {Error}  409 - Username or email already exists.
 * @returns {Error}  500 - Internal server error.
 */
app.post('/auth/register', async (req: Request, res: Response) => {
  const { username, password, email } = req.body;

  if (!username || !password) {
    return res.status(400).json({ message: 'Username and password are required.' });
  }

  // Basic validation (extend as needed)
  if (password.length < 6) {
    return res.status(400).json({ message: 'Password must be at least 6 characters long.' });
  }
  if (username.length < 3) {
    return res.status(400).json({ message: 'Username must be at least 3 characters long.' });
  }

  try {
    // Check if username or email already exists
    const [existingUsers] = await pool.query<User[]>(
      'SELECT id FROM users WHERE username = ? OR (email IS NOT NULL AND email = ?)',
      [username, email || null] // Use null if email is not provided
    );

    if (existingUsers.length > 0) {
      return res.status(409).json({ message: 'Username or email already exists.' });
    }

    const passwordHash = crypto.createHash('sha256').update(password).digest('hex');

    const [result] = await pool.query<ResultSetHeader>(
      'INSERT INTO users (username, password_hash, email) VALUES (?, ?, ?)',
      [username, passwordHash, email || null]
    );

    const userId = result.insertId;
    const token = await authService.generateToken(userId, '7d'); // Generate a token (e.g., valid for 7 days)

    res.status(201).json({
      message: 'User registered successfully.',
      userId,
      token
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Internal server error during registration.' });
  }
});

/**
 * @route POST /auth/login
 * @group Authentication - Operations for user registration and login
 * @summary Logs in an existing user.
 * @param {string} username.body.required - The username.
 * @param {string} password.body.required - The user's password.
 * @returns {object} 200 - Login successful, returns userId and token.
 * @returns {Error}  400 - Missing username or password.
 * @returns {Error}  401 - Invalid username or password.
 * @returns {Error}  500 - Internal server error.
 */
app.post('/auth/login', async (req: Request, res: Response) => {
  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ message: 'Username and password are required.' });
  }

  try {
    const [users] = await pool.query<User[]>(
      'SELECT id, password_hash FROM users WHERE username = ?',
      [username]
    );

    if (users.length === 0) {
      return res.status(401).json({ message: 'Invalid username or password.' });
    }

    const user = users[0];
    const passwordHash = crypto.createHash('sha256').update(password).digest('hex');

    if (passwordHash !== user.password_hash) {
      return res.status(401).json({ message: 'Invalid username or password.' });
    }

    // Update last_login timestamp (optional)
    // await pool.query('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);

    const token = await authService.generateToken(user.id, '1h'); // Generate a token (e.g., valid for 1 hour)

    res.status(200).json({
      message: 'Login successful.',
      userId: user.id,
      token
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Internal server error during login.' });
  }
});

// --- Colyseus Room Definitions ---
// Define 'lobby' room (default Colyseus lobby)
gameServer.define('lobby', LobbyRoom);

// Define your custom 'public_lobby' room
gameServer.define('public_lobby', PublicLobbyRoom);
  // .enableRealtimeListing(); // Optional: if you want it to appear in room listings

// Legacy static rooms (for backward compatibility)
// Define 'home' room (clients will specify ownerUid in options)
gameServer.define('home', HomeRoom);

// Define 'garden' room (clients will specify ownerUid in options)
gameServer.define('garden', GardenRoom);

// Define 'quest' room for quest system
gameServer.define('quest', QuestRoom);

// Dynamic rooms with room ID matching
// These rooms support dynamic room IDs like "home_123", "garden_456"
gameServer.define('home_dynamic', HomeRoomDynamic)
  .filterBy(['roomId']);

gameServer.define('garden_dynamic', GardenRoomDynamic)
  .filterBy(['roomId']);

// Quest rooms can also be dynamic if needed
gameServer.define('quest_dynamic', QuestRoom)
  .filterBy(['roomId']);

// Global notification room for real-time notifications
gameServer.define('notification', NotificationRoom);

// --- Colyseus Monitor ---
// (Optional) Attach Colyseus monitor for debugging and administration
// Accessible at http://localhost:2567/colyseus
app.use('/colyseus', monitor());

// --- API Routes ---
// Mount Friend Routes
app.use('/api/friends', friendRoutes);
// Mount Character Routes
app.use('/api/characters', characterRoutes);
// Mount Quest Routes
app.use('/api/quests', questRoutes);
// Mount Shop Routes
app.use('/api/shop', shopRoutes);
// Mount Inventory Routes
app.use('/api/inventory', inventoryRoutes);
// Mount Room Routes
app.use('/api/rooms', roomRoutes);
// Mount Notification Routes
app.use('/api/notifications', notificationRoutes);
// Mount Outfit Routes
app.use('/api/outfits', outfitRoutes);

// --- Error Handling Middleware ---
// 404 handler (must be after all routes)
app.use(notFoundHandler);

// Global error handler (must be last)
app.use(errorHandler);

// --- Start Server ---
gameServer.listen(port)
  .then(() => {
    console.log(`Colyseus server listening on ws://localhost:${port}`);
    console.log(`Express server with API routes also running on http://localhost:${port}`);
    console.log(`Colyseus monitor available at http://localhost:${port}/colyseus`);

    // Initialize notification service with game server
    NotificationService.initialize(gameServer);
    console.log('Notification service initialized');
  })
  .catch(e => {
    console.error("Failed to start server:", e);
  });

// Enhanced Global Error Handling Middleware for Express
// This middleware catches any errors that occur in the route handlers,
// logs them, and sends a standardized JSON error response to the client.
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error("Unhandled Express error at:", req.path, err.stack);

  // If the error is one we threw intentionally from a service (e.g., validation, not found),
  // it might have a 'statusCode' property or we can infer it from the message.
  // For this example, we'll keep it simple. More sophisticated error handling
  // might involve custom error classes with statusCode properties.

  // Avoid sending sensitive error details to the client in production.
  const message = process.env.NODE_ENV === 'production'
                  ? 'An unexpected server error occurred.'
                  : err.message || 'An unexpected server error occurred.';

  // If headers have already been sent, delegate to the default Express error handler.
  if (res.headersSent) {
    return next(err);
  }

  res.status(500).json({
    success: false,
    message: message,
    // Optionally, include error code or type in development for easier debugging
    ...(process.env.NODE_ENV !== 'production' && { errorType: err.name })
  });
});

export { app, gameServer };
