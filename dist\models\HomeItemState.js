"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeItemState = void 0;
const schema_1 = require("@colyseus/schema");
class HomeItemState extends schema_1.Schema {
    constructor(instanceId, templateId, x = 0, y = 0, rotation = 0, isFlipped = false) {
        super();
        this.instanceId = ''; // Unique instance ID of this item in the home
        this.templateId = ''; // ID of the item template (from items table)
        this.x = 0;
        this.y = 0;
        this.rotation = 0; // e.g., 0, 90, 180, 270 degrees
        this.isFlipped = false; // Horizontal flip
        this.instanceId = instanceId;
        this.templateId = templateId;
        this.x = x;
        this.y = y;
        this.rotation = rotation;
        this.isFlipped = isFlipped;
    }
}
exports.HomeItemState = HomeItemState;
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], HomeItemState.prototype, "instanceId", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], HomeItemState.prototype, "templateId", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], HomeItemState.prototype, "x", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], HomeItemState.prototype, "y", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], HomeItemState.prototype, "rotation", void 0);
__decorate([
    (0, schema_1.type)('boolean'),
    __metadata("design:type", Boolean)
], HomeItemState.prototype, "isFlipped", void 0);
