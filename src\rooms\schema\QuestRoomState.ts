import { Schema, type, MapSchema } from '@colyseus/schema';
import { PlayerState } from '../../models/PlayerState';
import { QuestSystemState } from '../../models/QuestState';

/**
 * Enhanced room state with quest system integration
 */
export class QuestRoomState extends Schema {
  @type({ map: PlayerState }) players = new MapSchema<PlayerState>();
  @type(QuestSystemState) questSystem = new QuestSystemState();
  @type('string') roomType: string = 'quest_room';
  @type('number') maxPlayers: number = 10;
  @type('number') createdAt: number = Date.now();

  constructor() {
    super();
  }

  /**
   * Add a player to the room
   */
  addPlayer(sessionId: string, uid: string, x: number = 0, y: number = 0): PlayerState {
    const player = new PlayerState(sessionId, uid, x, y);
    this.players.set(sessionId, player);
    return player;
  }

  /**
   * Remove a player from the room
   */
  removePlayer(sessionId: string): void {
    const player = this.players.get(sessionId);
    if (player) {
      // Clean up any quest-related data for this player if needed
      this.players.delete(sessionId);
    }
  }

  /**
   * Get player by session ID
   */
  getPlayer(sessionId: string): PlayerState | undefined {
    return this.players.get(sessionId);
  }

  /**
   * Get player by user ID
   */
  getPlayerByUid(uid: string): PlayerState | undefined {
    for (const [sessionId, player] of this.players) {
      if (player.uid === uid) {
        return player;
      }
    }
    return undefined;
  }

  /**
   * Get all players as array
   */
  getPlayersArray(): PlayerState[] {
    return Array.from(this.players.values());
  }

  /**
   * Get current player count
   */
  getPlayerCount(): number {
    return this.players.size;
  }

  /**
   * Check if room is full
   */
  isFull(): boolean {
    return this.getPlayerCount() >= this.maxPlayers;
  }

  /**
   * Update player position
   */
  updatePlayerPosition(sessionId: string, x: number, y: number, dir?: string): void {
    const player = this.players.get(sessionId);
    if (player) {
      player.x = x;
      player.y = y;
      if (dir) {
        player.dir = dir;
      }
    }
  }

  /**
   * Update player animation
   */
  updatePlayerAnimation(sessionId: string, animation: string, isFlipped?: boolean): void {
    const player = this.players.get(sessionId);
    if (player) {
      player.currentAnimation = animation;
      if (typeof isFlipped === 'boolean') {
        player.isFlipped = isFlipped;
      }
    }
  }

  /**
   * Update player sitting state
   */
  updatePlayerSitting(sessionId: string, isSitting: boolean): void {
    const player = this.players.get(sessionId);
    if (player) {
      player.isSitting = isSitting;
    }
  }

  /**
   * Update player outfit
   */
  updatePlayerOutfit(sessionId: string, outfit: any): void {
    const player = this.players.get(sessionId);
    if (player) {
      player.currentOutfit = JSON.stringify(outfit);
    }
  }

  /**
   * Get room statistics
   */
  getRoomStats(): any {
    const players = this.getPlayersArray();
    const activeQuests = new Map<string, number>();
    
    // Count active quests per player
    players.forEach(player => {
      const playerActiveQuests = this.questSystem.getActivePlayerQuests(player.uid);
      activeQuests.set(player.uid, playerActiveQuests.length);
    });

    return {
      playerCount: this.getPlayerCount(),
      maxPlayers: this.maxPlayers,
      roomType: this.roomType,
      createdAt: this.createdAt,
      uptime: Date.now() - this.createdAt,
      activeQuests: Object.fromEntries(activeQuests)
    };
  }
}
