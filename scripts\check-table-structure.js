const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkTableStructure() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('📋 Checking table structures...');
    
    // Check items table structure
    try {
      const [itemsColumns] = await connection.query('DESCRIBE items');
      console.log('\n🗃️  items table columns:');
      itemsColumns.forEach(col => console.log(`  - ${col.Field} (${col.Type})`));
    } catch (error) {
      console.log('❌ items table error:', error.message);
    }
    
    // Check users table structure
    try {
      const [usersColumns] = await connection.query('DESCRIBE users');
      console.log('\n👤 users table columns:');
      usersColumns.forEach(col => console.log(`  - ${col.Field} (${col.Type})`));
    } catch (error) {
      console.log('❌ users table error:', error.message);
    }
    
    // Check user_inventory table structure
    try {
      const [inventoryColumns] = await connection.query('DESCRIBE user_inventory');
      console.log('\n🎒 user_inventory table columns:');
      inventoryColumns.forEach(col => console.log(`  - ${col.Field} (${col.Type})`));
    } catch (error) {
      console.log('❌ user_inventory table error:', error.message);
    }
    
    // Check user_private_spaces table structure
    try {
      const [spacesColumns] = await connection.query('DESCRIBE user_private_spaces');
      console.log('\n🏠 user_private_spaces table columns:');
      spacesColumns.forEach(col => console.log(`  - ${col.Field} (${col.Type})`));
    } catch (error) {
      console.log('❌ user_private_spaces table error:', error.message);
    }
    
    // Check home_items table structure
    try {
      const [homeItemsColumns] = await connection.query('DESCRIBE home_items');
      console.log('\n🪑 home_items table columns:');
      homeItemsColumns.forEach(col => console.log(`  - ${col.Field} (${col.Type})`));
    } catch (error) {
      console.log('❌ home_items table error:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error checking table structure:', error);
  } finally {
    await connection.end();
  }
}

checkTableStructure();
