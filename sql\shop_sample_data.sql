-- Sample shop data for the virtual world game
-- This script inserts sample shop categories, items, and initial user currencies

-- Insert shop categories
INSERT INTO shop_categories (id, name, display_name, description, icon, sort_order, is_active) VALUES
(1, 'flower_house', '花之屋', '各种美丽的服饰和装扮', 'flower_icon', 1, TRUE),
(2, 'home_garden', '家园居', '家具与装饰用品', 'home_icon', 2, TRUE),
(3, 'wonder_workshop', '奇妙坊', '种子与道具', 'workshop_icon', 3, TRUE),
(4, 'surprise_pavilion', '惊喜阁', '限时商品与抽奖', 'surprise_icon', 4, TRUE);

-- Insert subcategories for 花之屋 (Flower House)
INSERT INTO shop_categories (name, display_name, description, icon, sort_order, is_active, parent_category_id) VALUES
('clothing_tops', '上装', '各种上衣和外套', 'top_icon', 1, TRUE, 1),
('clothing_bottoms', '下装', '裤子、裙子等下装', 'bottom_icon', 2, TRUE, 1),
('clothing_dresses', '连衣裙', '各种款式的连衣裙', 'dress_icon', 3, TRUE, 1),
('hairstyles', '发型', '各种发型和头饰', 'hair_icon', 4, TRUE, 1),
('shoes', '鞋履', '各种鞋子和袜子', 'shoe_icon', 5, TRUE, 1),
('wings', '翅膀', '华丽的翅膀和光效', 'wing_icon', 6, TRUE, 1),
('outfits', '套装', '预先搭配好的主题套装', 'outfit_icon', 7, TRUE, 1);

-- Insert subcategories for 家园居 (Home Garden)
INSERT INTO shop_categories (name, display_name, description, icon, sort_order, is_active, parent_category_id) VALUES
('furniture_indoor', '室内家具', '床、桌椅、柜子等', 'furniture_icon', 1, TRUE, 2),
('garden_decorations', '园艺装饰', '花坛、雕塑、喷泉等', 'garden_icon', 2, TRUE, 2),
('wallpaper_flooring', '墙纸地板', '改变房屋风格的装饰', 'wallpaper_icon', 3, TRUE, 2),
('themed_sets', '主题系列', '特定主题的家具套装', 'theme_icon', 4, TRUE, 2);

-- Insert subcategories for 奇妙坊 (Wonder Workshop)
INSERT INTO shop_categories (name, display_name, description, icon, sort_order, is_active, parent_category_id) VALUES
('common_seeds', '普通种子', '种植普通花卉的种子', 'seed_icon', 1, TRUE, 3),
('rare_seeds', '稀有种子', '种植稀有花卉的种子', 'rare_seed_icon', 2, TRUE, 3),
('growth_tools', '成长道具', '加速植物生长的道具', 'tool_icon', 3, TRUE, 3);

-- Sample items for testing (assuming these item_ids exist in the items table)
-- You may need to adjust these based on your actual items table

-- Insert sample shop items for clothing
INSERT INTO shop_items (item_id, category_id, name, description, price_gold, price_diamonds, is_available, is_featured, sort_order, tags, metadata) VALUES
('fairy_dress_pink', (SELECT id FROM shop_categories WHERE name = 'clothing_dresses'), '粉色仙女裙', '优雅的粉色仙女连衣裙，让你如花仙子般美丽', 500, NULL, TRUE, TRUE, 1, '["dress", "pink", "fairy", "elegant"]', '{"rarity": "common", "style": "fairy"}'),
('fairy_dress_blue', (SELECT id FROM shop_categories WHERE name = 'clothing_dresses'), '蓝色仙女裙', '清新的蓝色仙女连衣裙，如天空般纯净', 500, NULL, TRUE, FALSE, 2, '["dress", "blue", "fairy", "fresh"]', '{"rarity": "common", "style": "fairy"}'),
('royal_gown_gold', (SELECT id FROM shop_categories WHERE name = 'clothing_dresses'), '金色皇家礼服', '华丽的金色皇家礼服，彰显高贵气质', NULL, 50, TRUE, TRUE, 3, '["dress", "gold", "royal", "luxury"]', '{"rarity": "rare", "style": "royal"}');

-- Insert sample shop items for wings
INSERT INTO shop_items (item_id, category_id, name, description, price_gold, price_diamonds, is_available, is_featured, sort_order, tags, metadata) VALUES
('butterfly_wings_rainbow', (SELECT id FROM shop_categories WHERE name = 'wings'), '彩虹蝴蝶翅膀', '绚丽多彩的蝴蝶翅膀，带有彩虹光效', 800, NULL, TRUE, TRUE, 1, '["wings", "butterfly", "rainbow", "colorful"]', '{"rarity": "uncommon", "effect": "rainbow_glow"}'),
('angel_wings_white', (SELECT id FROM shop_categories WHERE name = 'wings'), '纯白天使翅膀', '圣洁的白色天使翅膀，散发神圣光芒', NULL, 80, TRUE, FALSE, 2, '["wings", "angel", "white", "holy"]', '{"rarity": "rare", "effect": "holy_glow"}');

-- Insert sample shop items for furniture
INSERT INTO shop_items (item_id, category_id, name, description, price_gold, price_diamonds, is_available, is_featured, sort_order, tags, metadata) VALUES
('flower_bed_rose', (SELECT id FROM shop_categories WHERE name = 'furniture_indoor'), '玫瑰花床', '装饰着美丽玫瑰的舒适床铺', 1200, NULL, TRUE, FALSE, 1, '["furniture", "bed", "rose", "comfortable"]', '{"rarity": "common", "room": "bedroom"}'),
('crystal_table', (SELECT id FROM shop_categories WHERE name = 'furniture_indoor'), '水晶桌子', '闪闪发光的水晶制作的精美桌子', NULL, 100, TRUE, TRUE, 2, '["furniture", "table", "crystal", "shiny"]', '{"rarity": "rare", "room": "living"}');

-- Insert sample shop items for garden decorations
INSERT INTO shop_items (item_id, category_id, name, description, price_gold, price_diamonds, is_available, is_featured, sort_order, tags, metadata) VALUES
('fairy_fountain', (SELECT id FROM shop_categories WHERE name = 'garden_decorations'), '仙女喷泉', '美丽的仙女雕像喷泉，为花园增添魅力', 2000, NULL, TRUE, TRUE, 1, '["decoration", "fountain", "fairy", "garden"]', '{"rarity": "uncommon", "size": "large"}'),
('flower_arch', (SELECT id FROM shop_categories WHERE name = 'garden_decorations'), '花朵拱门', '由鲜花装饰的浪漫拱门', 800, NULL, TRUE, FALSE, 2, '["decoration", "arch", "flower", "romantic"]', '{"rarity": "common", "size": "medium"}');

-- Insert sample shop items for seeds
INSERT INTO shop_items (item_id, category_id, name, description, price_gold, price_diamonds, is_available, is_featured, sort_order, tags, metadata) VALUES
('rose_seed_red', (SELECT id FROM shop_categories WHERE name = 'common_seeds'), '红玫瑰种子', '种植美丽红玫瑰的种子', 50, NULL, TRUE, FALSE, 1, '["seed", "rose", "red", "flower"]', '{"rarity": "common", "growth_time": 120}'),
('tulip_seed_mixed', (SELECT id FROM shop_categories WHERE name = 'common_seeds'), '混合郁金香种子', '可以种植各种颜色郁金香的种子包', 80, NULL, TRUE, FALSE, 2, '["seed", "tulip", "mixed", "colorful"]', '{"rarity": "common", "growth_time": 90}'),
('rainbow_flower_seed', (SELECT id FROM shop_categories WHERE name = 'rare_seeds'), '彩虹花种子', '传说中的彩虹花种子，极其稀有', NULL, 30, TRUE, TRUE, 1, '["seed", "rainbow", "rare", "magical"]', '{"rarity": "legendary", "growth_time": 300}');

-- Insert sample shop items for tools
INSERT INTO shop_items (item_id, category_id, name, description, price_gold, price_diamonds, is_available, is_featured, sort_order, tags, metadata) VALUES
('growth_potion', (SELECT id FROM shop_categories WHERE name = 'growth_tools'), '生长药水', '加速植物生长的神奇药水', 200, NULL, TRUE, FALSE, 1, '["tool", "potion", "growth", "speed"]', '{"rarity": "common", "effect": "growth_boost"}'),
('golden_watering_can', (SELECT id FROM shop_categories WHERE name = 'growth_tools'), '黄金洒水壶', '提升植物品质的黄金洒水壶', NULL, 25, TRUE, TRUE, 2, '["tool", "watering_can", "golden", "quality"]', '{"rarity": "rare", "effect": "quality_boost"}');

-- Insert limited time items
INSERT INTO shop_items (item_id, category_id, name, description, price_gold, price_diamonds, is_available, is_featured, is_limited_time, available_from, available_until, sort_order, tags, metadata) VALUES
('valentine_dress', (SELECT id FROM shop_categories WHERE name = 'clothing_dresses'), '情人节特别礼服', '限时情人节主题的浪漫礼服', NULL, 60, TRUE, TRUE, TRUE, '2024-02-01 00:00:00', '2024-02-29 23:59:59', 1, '["dress", "valentine", "limited", "romantic"]', '{"rarity": "limited", "event": "valentine"}'),
('christmas_wings', (SELECT id FROM shop_categories WHERE name = 'wings'), '圣诞节翅膀', '限时圣诞节主题的特殊翅膀', 1000, NULL, TRUE, TRUE, TRUE, '2024-12-01 00:00:00', '2024-12-31 23:59:59', 1, '["wings", "christmas", "limited", "festive"]', '{"rarity": "limited", "event": "christmas"}');

-- Insert items with purchase limits
INSERT INTO shop_items (item_id, category_id, name, description, price_gold, price_diamonds, is_available, is_featured, max_purchases_per_user, total_stock, remaining_stock, sort_order, tags, metadata) VALUES
('exclusive_crown', (SELECT id FROM shop_categories WHERE name = 'hairstyles'), '专属皇冠', '限量版的专属皇冠，每人限购一个', NULL, 200, TRUE, TRUE, 1, 100, 100, 1, '["accessory", "crown", "exclusive", "limited"]', '{"rarity": "exclusive", "type": "crown"}');

-- Insert some initial user currencies for testing
-- Note: You may need to adjust user IDs based on your actual users table
INSERT INTO user_currencies (user_id, gold_coins, diamonds) VALUES
(1, 5000, 100),
(2, 3000, 50),
(3, 10000, 200)
ON DUPLICATE KEY UPDATE 
gold_coins = VALUES(gold_coins), 
diamonds = VALUES(diamonds);

-- Insert some sample inventory items for testing
-- Note: You may need to adjust user IDs and item IDs based on your actual data
INSERT INTO user_inventory (user_id, item_id, quantity) VALUES
(1, 'fairy_dress_pink', 1),
(1, 'rose_seed_red', 10),
(1, 'growth_potion', 5),
(2, 'butterfly_wings_rainbow', 1),
(2, 'flower_bed_rose', 1),
(3, 'royal_gown_gold', 1),
(3, 'angel_wings_white', 1),
(3, 'crystal_table', 1)
ON DUPLICATE KEY UPDATE 
quantity = quantity + VALUES(quantity);
