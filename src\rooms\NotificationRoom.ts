import { Room, Client } from '@colyseus/core';
import { Schema, MapSchema, type } from '@colyseus/schema';
import authService from '../services/authService';
import NotificationService, { NotificationData, RealtimeNotification } from '../services/NotificationService';

/**
 * User state in notification room
 */
export class NotificationUserState extends Schema {
  @type("string") userId: string = "";
  @type("string") username: string = "";
  @type("boolean") isOnline: boolean = true;
  @type("number") lastSeen: number = 0;
  @type("string") currentRoom: string = "";

  constructor(userId: string, username: string) {
    super();
    this.userId = userId;
    this.username = username;
    this.lastSeen = Date.now();
  }
}

/**
 * Notification room state
 */
export class NotificationRoomState extends Schema {
  @type({ map: NotificationUserState }) users = new MapSchema<NotificationUserState>();
  @type("number") onlineCount: number = 0;

  addUser(userId: string, username: string): NotificationUserState {
    const user = new NotificationUserState(userId, username);
    this.users.set(userId, user);
    this.onlineCount = this.users.size;
    return user;
  }

  removeUser(userId: string): void {
    this.users.delete(userId);
    this.onlineCount = this.users.size;
  }

  updateUserStatus(userId: string, currentRoom?: string): void {
    const user = this.users.get(userId);
    if (user) {
      user.lastSeen = Date.now();
      if (currentRoom) {
        user.currentRoom = currentRoom;
      }
    }
  }
}

/**
 * Global notification room for real-time notifications
 * Users join this room to receive real-time notifications
 */
export class NotificationRoom extends Room<NotificationRoomState> {
  maxClients = 1000; // Allow many users in notification room
  autoDispose = false; // Keep room alive

  async onCreate(options: any) {
    console.log('NotificationRoom created');
    this.setState(new NotificationRoomState());

    // Register message handlers
    this.registerMessageHandlers();

    // Set up periodic cleanup
    this.clock.setInterval(() => {
      this.cleanupInactiveUsers();
    }, 60000); // Every minute
  }

  /**
   * Register message handlers for notification room
   */
  private registerMessageHandlers(): void {
    // Mark notification as read
    this.onMessage('mark_notification_read', async (client, message: { notificationId: string }) => {
      try {
        const { notificationId } = message;
        if (!notificationId || typeof notificationId !== 'string') {
          return client.error(400, 'Invalid notification ID');
        }

        const success = await NotificationService.markNotificationAsRead(notificationId, client.auth.uid);
        if (success) {
          client.send('notification_marked_read', { notificationId });
        } else {
          client.error(404, 'Notification not found or already read');
        }
      } catch (error) {
        console.error('Error marking notification as read:', error);
        client.error(500, 'Failed to mark notification as read');
      }
    });

    // Mark all notifications as read
    this.onMessage('mark_all_notifications_read', async (client) => {
      try {
        const count = await NotificationService.markAllNotificationsAsRead(client.auth.uid);
        client.send('all_notifications_marked_read', { count });
      } catch (error) {
        console.error('Error marking all notifications as read:', error);
        client.error(500, 'Failed to mark all notifications as read');
      }
    });

    // Get user notifications
    this.onMessage('get_notifications', async (client, message: { limit?: number; offset?: number }) => {
      try {
        const { limit = 50, offset = 0 } = message;
        const notifications = await NotificationService.getUserNotifications(client.auth.uid, limit, offset);
        
        client.send('notifications_list', {
          notifications,
          hasMore: notifications.length === limit
        });
      } catch (error) {
        console.error('Error getting notifications:', error);
        client.error(500, 'Failed to get notifications');
      }
    });

    // Update user status
    this.onMessage('update_status', (client, message: { currentRoom?: string }) => {
      try {
        const { currentRoom } = message;
        this.state.updateUserStatus(client.auth.uid, currentRoom);
        
        // Broadcast status update to friends
        this.broadcast('user_status_updated', {
          userId: client.auth.uid,
          currentRoom,
          timestamp: Date.now()
        }, { except: client });
      } catch (error) {
        console.error('Error updating user status:', error);
      }
    });

    // Send direct message/notification to another user
    this.onMessage('send_direct_notification', async (client, message: {
      toUserId: string;
      type: string;
      title: string;
      message: string;
      data?: any;
    }) => {
      try {
        const { toUserId, type, title, message: notificationMessage, data } = message;
        
        if (!toUserId || !type || !title || !notificationMessage) {
          return client.error(400, 'Missing required fields');
        }

        // Check if users are friends (optional security check)
        // You can implement additional permission checks here

        await NotificationService.sendRealtimeNotification({
          type: type as any,
          fromUserId: client.auth.uid,
          toUserId,
          title,
          message: notificationMessage,
          data
        });

        client.send('notification_sent', { toUserId });
      } catch (error) {
        console.error('Error sending direct notification:', error);
        client.error(500, 'Failed to send notification');
      }
    });

    // Get online friends
    this.onMessage('get_online_friends', async (client) => {
      try {
        // Get user's friends and check their online status
        const friends = await this.getUserFriends(client.auth.uid);
        const onlineFriends = friends.filter(friendId => 
          NotificationService.isUserOnline(friendId)
        ).map(friendId => {
          const status = NotificationService.getUserOnlineStatus(friendId);
          const userState = this.state.users.get(friendId);
          return {
            userId: friendId,
            username: userState?.username || `User_${friendId}`,
            isOnline: true,
            lastSeen: status?.lastSeen,
            currentRoom: userState?.currentRoom
          };
        });

        client.send('online_friends_list', { friends: onlineFriends });
      } catch (error) {
        console.error('Error getting online friends:', error);
        client.error(500, 'Failed to get online friends');
      }
    });
  }

  /**
   * Authenticate user joining the notification room
   */
  async onAuth(client: Client, options: { token: string; uid: string }): Promise<any> {
    if (!options || !options.token || !options.uid) {
      throw new Error('Authentication failed: Token and UID are required');
    }

    // Verify token
    const userPayload = await authService.verifyToken(options.token);
    if (!userPayload || userPayload.userId.toString() !== options.uid) {
      throw new Error('Authentication failed: Invalid token or UID mismatch');
    }

    console.log(`NotificationRoom: User ${options.uid} authenticated`);
    return { uid: options.uid };
  }

  /**
   * Handle user joining the notification room
   */
  async onJoin(client: Client, options: any, auth: { uid: string }): Promise<void> {
    console.log(`User ${auth.uid} joined NotificationRoom`);

    // Get username
    const username = await this.getUsernameById(auth.uid);

    // Add user to room state
    this.state.addUser(auth.uid, username);

    // Register user session for notifications
    NotificationService.registerUserSession(auth.uid, client.sessionId);

    // Send initial data to user
    client.send('notification_room_joined', {
      userId: auth.uid,
      username,
      onlineCount: this.state.onlineCount
    });

    // Send recent notifications
    try {
      const notifications = await NotificationService.getUserNotifications(auth.uid, 10);
      client.send('recent_notifications', { notifications });
    } catch (error) {
      console.error('Error sending recent notifications:', error);
    }

    // Broadcast user online status to friends
    this.broadcastToFriends(auth.uid, 'friend_online', {
      userId: auth.uid,
      username,
      timestamp: Date.now()
    });
  }

  /**
   * Handle user leaving the notification room
   */
  onLeave(client: Client, consented: boolean): void {
    const auth = client.auth as { uid: string };
    if (auth?.uid) {
      console.log(`User ${auth.uid} left NotificationRoom`);

      // Remove user from room state
      this.state.removeUser(auth.uid);

      // Unregister user session
      NotificationService.unregisterUserSession(auth.uid, client.sessionId);

      // Broadcast user offline status to friends
      this.broadcastToFriends(auth.uid, 'friend_offline', {
        userId: auth.uid,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Broadcast message to user's friends
   */
  private async broadcastToFriends(userId: string, messageType: string, data: any): Promise<void> {
    try {
      const friends = await this.getUserFriends(userId);
      
      this.clients.forEach(client => {
        const clientAuth = client.auth as { uid: string };
        if (clientAuth?.uid && friends.includes(clientAuth.uid)) {
          client.send(messageType, data);
        }
      });
    } catch (error) {
      console.error('Error broadcasting to friends:', error);
    }
  }

  /**
   * Get user's friends list
   */
  private async getUserFriends(userId: string): Promise<string[]> {
    try {
      // This should use the same logic as NotificationService
      // For now, we'll implement a simple version
      const pool = require('../utils/db').default;
      const [rows] = await pool.query(
        `SELECT 
           CASE 
             WHEN user_one_id = ? THEN user_two_id 
             ELSE user_one_id 
           END as friend_id
         FROM friend_relationships 
         WHERE (user_one_id = ? OR user_two_id = ?) AND status = 'accepted'`,
        [userId, userId, userId]
      );

      return rows.map((row: any) => row.friend_id.toString());
    } catch (error) {
      console.error('Error getting user friends:', error);
      return [];
    }
  }

  /**
   * Get username by user ID
   */
  private async getUsernameById(userId: string): Promise<string> {
    try {
      const pool = require('../utils/db').default;
      const [rows] = await pool.query(
        'SELECT username FROM users WHERE id = ?',
        [userId]
      );

      return rows.length > 0 ? rows[0].username : `User_${userId}`;
    } catch (error) {
      console.error('Error getting username:', error);
      return `User_${userId}`;
    }
  }

  /**
   * Clean up inactive users
   */
  private cleanupInactiveUsers(): void {
    const now = Date.now();
    const inactiveThreshold = 5 * 60 * 1000; // 5 minutes

    this.state.users.forEach((user, userId) => {
      if (now - user.lastSeen > inactiveThreshold) {
        // Check if user still has active sessions
        if (!NotificationService.isUserOnline(userId)) {
          this.state.removeUser(userId);
        }
      }
    });
  }

  onDispose(): void {
    console.log('NotificationRoom disposed');
  }
}
