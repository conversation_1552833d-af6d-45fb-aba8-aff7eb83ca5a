import { Room, Client, Delayed } from '@colyseus/core';
import { GardenRoomState } from '../models/GardenRoomState';
import { PlayerState } from '../models/PlayerState';
import { GardenPlotState } from '../models/GardenPlotState';
import authService from '../services/authService';
import SpaceService, { GardenPlotRecord } from '../services/SpaceService'; // Singleton instance
import ItemService, { ItemDetails } from '../services/ItemService'; // Singleton instance
import PlantingService from '../services/PlantingService'; // New planting system
import PlantingOperationsService from '../services/PlantingOperationsService'; // New planting operations
import { v4 as uuidv4 } from 'uuid';

const PLANT_GROWTH_CHECK_INTERVAL = 60 * 1000; // Check every 60 seconds (example)

export class GardenRoom extends Room<GardenRoomState> {
  private ownerUid!: string;
  private spaceServiceInstance!: typeof SpaceService;
  private itemServiceInstance!: typeof ItemService;
  private growthCheckInterval!: Delayed;
  public autoDispose = true;


  async onCreate(options: { ownerUid: string }) {
    if (!options || !options.ownerUid) {
      console.error("GardenRoom creation failed: ownerUid is required in options.");
      this.disconnect();
      return;
    }
    this.ownerUid = options.ownerUid;
    console.log(`GardenRoom created for owner: ${this.ownerUid}`);

    this.spaceServiceInstance = SpaceService;
    this.itemServiceInstance = ItemService;

    this.setState(new GardenRoomState(this.ownerUid));

    try {
      const spaceData = await this.spaceServiceInstance.getFullPrivateSpaceData(this.ownerUid);
      if (!spaceData || !spaceData.settings) {
        console.error(`Failed to load space data for GardenRoom owner: ${this.ownerUid}. Disposing room.`);
        this.disconnect();
        return;
      }

      this.state.gardenBackgroundId = spaceData.settings.garden_background_id;
      this.state.accessLevel = spaceData.settings.garden_access_level;

      spaceData.gardenPlots.forEach((plotRecord: GardenPlotRecord) => {
        const gardenPlot = new GardenPlotState(
          plotRecord.plot_instance_id,
          plotRecord.plot_template_id,
          plotRecord.seed_item_id || undefined,
          plotRecord.plant_timestamp ? new Date(plotRecord.plant_timestamp).getTime() : 0,
          plotRecord.growth_stage,
          plotRecord.last_watered_timestamp ? new Date(plotRecord.last_watered_timestamp).getTime() : 0
        );
        this.state.plots.set(plotRecord.plot_instance_id, gardenPlot);
      });

      console.log(`GardenRoom for ${this.ownerUid} initialized with background ${this.state.gardenBackgroundId} and ${this.state.plots.size} plots.`);

    } catch (error) {
      console.error(`Critical error during GardenRoom ${this.ownerUid} onCreate:`, error);
      this.disconnect();
      return;
    }

    this.registerMessageHandlers();
    this.startGrowthSimulation();
  }

  registerMessageHandlers() {
    this.onMessage("plant_seed", async (client, message) => {
      if (client.auth.uid !== this.state.ownerUid) {
        client.error(403, "Action not allowed: Not room owner.");
        return;
      }
      const { plotId, seedItemId } = message;
      if (typeof plotId !== 'string' || typeof seedItemId !== 'string') {
        client.error(400, "Invalid parameters for plant_seed.");
        return;
      }
      try {
        const result = await this.spaceServiceInstance.plantSeed(this.ownerUid, plotId, seedItemId);
        if (result.success) {
          // Refetch plot data to update state, or update manually based on successful call
          const plotData = await this.spaceServiceInstance.getFullPrivateSpaceData(this.ownerUid); // Could optimize to get single plot
          const updatedPlotRecord = plotData?.gardenPlots.find((p: GardenPlotRecord) => p.plot_instance_id === plotId);
          if (updatedPlotRecord) {
            const plotState = this.state.plots.get(plotId);
            if (plotState) {
                plotState.seedId = updatedPlotRecord.seed_item_id || '';
                plotState.plantTimestamp = updatedPlotRecord.plant_timestamp ? new Date(updatedPlotRecord.plant_timestamp).getTime() : 0;
                plotState.growthStage = updatedPlotRecord.growth_stage;
                plotState.lastWateredTimestamp = updatedPlotRecord.last_watered_timestamp ? new Date(updatedPlotRecord.last_watered_timestamp).getTime() : 0;
            }
          }
           client.send("plant_seed_success", { plotId, seedItemId });
        } else {
          client.error(500, result.message || "Failed to plant seed.");
        }
      } catch (e: any) {
        console.error("Error planting seed:", e);
        client.error(500, e.message || "Server error planting seed.");
      }
    });

    this.onMessage("water_plant", async (client, message) => {
      // Allow anyone with access to water (or restrict to owner if desired)
      // For now, assuming any authenticated user in the room can water
      const { plotId } = message;
      if (typeof plotId !== 'string') {
        client.error(400, "Invalid parameters for water_plant.");
        return;
      }
      try {
        // Using client.auth.uid as the "actor" for watering, though spaceService uses ownerUid for DB ops
        const result = await this.spaceServiceInstance.waterPlant(this.ownerUid, plotId);
        if (result.success) {
          const plotState = this.state.plots.get(plotId);
          if (plotState) {
            plotState.lastWateredTimestamp = Date.now(); // Update immediately in state
          }
          client.send("water_plant_success", { plotId });
        } else {
          client.error(500, result.message || "Failed to water plant.");
        }
      } catch (e: any) {
        console.error("Error watering plant:", e);
        client.error(500, e.message || "Server error watering plant.");
      }
    });

    this.onMessage("harvest_plant", async (client, message) => {
      if (client.auth.uid !== this.state.ownerUid) {
        client.error(403, "Action not allowed: Not room owner.");
        return;
      }
      const { plotId } = message;
      if (typeof plotId !== 'string') {
        client.error(400, "Invalid parameters for harvest_plant.");
        return;
      }
      try {
        const result = await this.spaceServiceInstance.harvestPlant(this.ownerUid, plotId);
        if (result.success) {
          const plotState = this.state.plots.get(plotId);
          if (plotState) {
            plotState.seedId = '';
            plotState.plantTimestamp = 0;
            plotState.growthStage = 0;
            plotState.lastWateredTimestamp = 0;
          }
          client.send("harvest_success", { plotId, harvestedItems: result.harvestedItems });
        } else {
          client.error(500, result.message || "Failed to harvest plant.");
        }
      } catch (e: any) {
        console.error("Error harvesting plant:", e);
        client.error(500, e.message || "Server error harvesting plant.");
      }
    });

    this.onMessage("update_background", async (client, message) => {
        if (client.auth.uid !== this.state.ownerUid) {
            client.error(403, "Action not allowed: Not room owner.");
            return;
        }
        const { backgroundItemId } = message;
         if (typeof backgroundItemId !== 'string') {
            client.error(400, "Invalid parameters for update_background. backgroundItemId required.");
            return;
        }
        try {
            const itemDetails = await this.itemServiceInstance.getItemDetails(backgroundItemId);
            // Assuming 'background' or 'garden_background' is a valid type
            if (!itemDetails || (itemDetails.type !== 'furniture' && itemDetails.category !== 'garden_background')) {
                client.error(400, "Invalid background item ID or item is not a background.");
                return;
            }
            const result = await this.spaceServiceInstance.updateSpaceBackground(this.ownerUid, 'garden', backgroundItemId);
            if (result.success) {
                this.state.gardenBackgroundId = backgroundItemId;
            } else {
                client.error(500, result.message || "Failed to update background.");
            }
        } catch (e: any) {
            console.error("Error updating garden background:", e);
            client.error(500, e.message || "Server error updating garden background.");
        }
    });

    this.onMessage("update_access_level", async (client, message) => {
        if (client.auth.uid !== this.state.ownerUid) {
            client.error(403, "Action not allowed: Not room owner.");
            return;
        }
        const { accessLevel } = message;
        if (!['private', 'friends_only', 'public'].includes(accessLevel)) {
            client.error(400, "Invalid access level provided.");
            return;
        }
        try {
            const result = await this.spaceServiceInstance.updateSpaceAccessLevel(this.ownerUid, 'garden', accessLevel);
            if (result.success) {
                this.state.accessLevel = accessLevel;
            } else {
                client.error(500, result.message || "Failed to update access level.");
            }
        } catch (e: any) {
            console.error("Error updating garden access level:", e);
            client.error(500, e.message || "Server error updating garden access level.");
        }
    });

    // New Planting System Message Handlers
    this.onMessage("planting_plant_seed", async (client, message) => {
      if (client.auth.uid !== this.state.ownerUid) {
        client.error(403, "Action not allowed: Not room owner.");
        return;
      }
      const { seed_id, plot_index } = message;
      if (typeof seed_id !== 'string' || typeof plot_index !== 'number') {
        client.error(400, "Invalid parameters for planting_plant_seed.");
        return;
      }
      try {
        const result = await PlantingOperationsService.plantSeed(this.ownerUid, seed_id, plot_index);
        if (result.success) {
          client.send("planting_plant_success", result);
          // Broadcast to all clients in the room
          this.broadcast("planting_garden_updated", {
            action: 'plant',
            plot_index,
            seed_id,
            planted_flower_id: result.data?.planted_flower_id
          });
        } else {
          client.error(400, result.message || "Failed to plant seed.");
        }
      } catch (e: any) {
        console.error("Error planting seed:", e);
        client.error(500, e.message || "Server error planting seed.");
      }
    });

    this.onMessage("planting_water_flower", async (client, message) => {
      const { planted_flower_id } = message;
      if (typeof planted_flower_id !== 'number') {
        client.error(400, "Invalid parameters for planting_water_flower.");
        return;
      }
      try {
        const result = await PlantingOperationsService.waterFlower(client.auth.uid, planted_flower_id);
        if (result.success) {
          client.send("planting_water_success", result);
          // Broadcast to all clients in the room
          this.broadcast("planting_garden_updated", {
            action: 'water',
            planted_flower_id,
            data: result.data
          });
        } else {
          client.error(400, result.message || "Failed to water flower.");
        }
      } catch (e: any) {
        console.error("Error watering flower:", e);
        client.error(500, e.message || "Server error watering flower.");
      }
    });

    this.onMessage("planting_fertilize_flower", async (client, message) => {
      const { planted_flower_id, fertilizer_id } = message;
      if (typeof planted_flower_id !== 'number' || typeof fertilizer_id !== 'string') {
        client.error(400, "Invalid parameters for planting_fertilize_flower.");
        return;
      }
      try {
        const result = await PlantingOperationsService.fertilizeFlower(client.auth.uid, planted_flower_id, fertilizer_id);
        if (result.success) {
          client.send("planting_fertilize_success", result);
          // Broadcast to all clients in the room
          this.broadcast("planting_garden_updated", {
            action: 'fertilize',
            planted_flower_id,
            fertilizer_id,
            data: result.data
          });
        } else {
          client.error(400, result.message || "Failed to fertilize flower.");
        }
      } catch (e: any) {
        console.error("Error fertilizing flower:", e);
        client.error(500, e.message || "Server error fertilizing flower.");
      }
    });

    this.onMessage("planting_harvest_flower", async (client, message) => {
      if (client.auth.uid !== this.state.ownerUid) {
        client.error(403, "Action not allowed: Not room owner.");
        return;
      }
      const { planted_flower_id } = message;
      if (typeof planted_flower_id !== 'number') {
        client.error(400, "Invalid parameters for planting_harvest_flower.");
        return;
      }
      try {
        const result = await PlantingOperationsService.harvestFlower(this.ownerUid, planted_flower_id);
        if (result.success) {
          client.send("planting_harvest_success", result);
          // Broadcast to all clients in the room
          this.broadcast("planting_garden_updated", {
            action: 'harvest',
            planted_flower_id,
            items_received: result.items_received
          });
        } else {
          client.error(400, result.message || "Failed to harvest flower.");
        }
      } catch (e: any) {
        console.error("Error harvesting flower:", e);
        client.error(500, e.message || "Server error harvesting flower.");
      }
    });

    this.onMessage("planting_unlock_plot", async (client, message) => {
      if (client.auth.uid !== this.state.ownerUid) {
        client.error(403, "Action not allowed: Not room owner.");
        return;
      }
      const { plot_index } = message;
      if (typeof plot_index !== 'number') {
        client.error(400, "Invalid parameters for planting_unlock_plot.");
        return;
      }
      try {
        const result = await PlantingService.unlockPlot(this.ownerUid, plot_index);
        if (result.success) {
          client.send("planting_unlock_success", result);
          // Broadcast to all clients in the room
          this.broadcast("planting_garden_updated", {
            action: 'unlock_plot',
            plot_index
          });
        } else {
          client.error(400, result.message || "Failed to unlock plot.");
        }
      } catch (e: any) {
        console.error("Error unlocking plot:", e);
        client.error(500, e.message || "Server error unlocking plot.");
      }
    });

    this.onMessage("planting_get_garden_status", async (client, message) => {
      try {
        // Update flower states first
        await PlantingOperationsService.updateFlowerStates(this.ownerUid);

        // Get current garden data
        const garden = await PlantingService.getUserGarden(this.ownerUid);
        const flowers = await PlantingService.getUserPlantedFlowers(this.ownerUid);
        const level = await PlantingService.getUserPlantingLevel(this.ownerUid);

        client.send("planting_garden_status", {
          garden,
          flowers,
          level
        });
      } catch (e: any) {
        console.error("Error getting garden status:", e);
        client.error(500, e.message || "Server error getting garden status.");
      }
    });
  }

  startGrowthSimulation() {
    this.growthCheckInterval = this.clock.setInterval(async () => {
      if (!this.state || !this.state.plots) return;

      for (const [plotId, plot] of this.state.plots.entries()) {
        if (plot.seedId && plot.plantTimestamp > 0 && plot.growthStage < 4) { // Assuming 4 is max growth before harvest
          const seedDetails = await this.itemServiceInstance.getItemDetails(plot.seedId);
          if (!seedDetails || !seedDetails.data) continue;

          try {
            const seedData = JSON.parse(seedDetails.data);
            const growthDurationPerStage = (seedData.growthTimeSeconds || 300) / 3; // Total growth time / (num stages - 1 initial)
            const timeSincePlanting = (Date.now() - plot.plantTimestamp) / 1000; // in seconds

            // Basic time-based growth. More complex logic could involve watering status.
            let expectedStage = 1 + Math.floor(timeSincePlanting / growthDurationPerStage);
            expectedStage = Math.min(expectedStage, 4); // Cap at max stage (harvestable)

            if (plot.growthStage < expectedStage) {
              // Call SpaceService to persist this (optional, or do it less frequently)
              // For now, just update room state. Persist on harvest or other interactions.
              // Or, call spaceService.updatePlantGrowthStage but this is a simplified approach.
              console.log(`Plot ${plotId} (seed: ${plot.seedId}) grew from ${plot.growthStage} to ${expectedStage}.`);
              plot.growthStage = expectedStage;
              // No need to call spaceService.updatePlantGrowthStage if we persist on other actions
              // or if state is primarily driven by room and saved periodically/on demand by SpaceService.
              // For this task, direct state update is fine.
            }
          } catch (e) {
            console.error(`Error processing growth for plot ${plotId} with seed ${plot.seedId}:`, e);
          }
        }
      }
    }, PLANT_GROWTH_CHECK_INTERVAL);
  }


  async onAuth(client: Client, options: { token: string, uid: string }, request: any) {
     if (!this.ownerUid) {
        console.error("GardenRoom onAuth called before ownerUid was set or after creation failure.");
        throw new Error("Room not initialized.");
    }
    if (!options || !options.token || !options.uid) {
      console.error(`GardenRoom Auth failed for client ${client.sessionId}: Missing token or uid.`);
      throw new Error('Authentication failed: Token and UID are required.');
    }

    const userPayload = await authService.verifyToken(options.token);
    if (!userPayload || userPayload.userId.toString() !== options.uid.toString()) {
        console.error(`GardenRoom Auth failed for client ${client.sessionId}: Invalid token or UID mismatch.`);
        throw new Error('Authentication failed: Invalid token or UID mismatch.');
    }

    const hasAccess = await this.spaceServiceInstance.checkAccess(this.ownerUid, options.uid, 'garden');
    if (!hasAccess) {
      console.error(`GardenRoom Auth failed for client ${client.sessionId} (UID: ${options.uid}): No access to garden of ${this.ownerUid}.`);
      throw new Error('Access denied to this garden.');
    }

    console.log(`GardenRoom Auth successful for client ${client.sessionId} (UID: ${options.uid}) to garden of ${this.ownerUid}.`);
    return { uid: options.uid.toString() };
  }

  onJoin(client: Client, options: any, auth: { uid: string }) {
    if (!auth || !auth.uid) {
        console.error(`GardenRoom Join rejected for client ${client.sessionId}: Missing auth data.`);
        client.leave(1001, "Authentication data missing.");
        return;
    }
    console.log(`${client.sessionId} (UID: ${auth.uid}) joined GardenRoom for owner ${this.ownerUid}.`);

    const player = new PlayerState(
        client.sessionId,
        auth.uid,
        Math.floor(Math.random() * 500), // Default spawn X for garden
        Math.floor(Math.random() * 400)  // Default spawn Y for garden
    );
    this.state.players.set(client.sessionId, player);
  }

  onLeave(client: Client, consented: boolean) {
    const player = this.state.players.get(client.sessionId);
    const uid = player ? player.uid : 'Unknown';
    console.log(`${client.sessionId} (UID: ${uid}) left GardenRoom for owner ${this.ownerUid}. Consented: ${consented}`);
    if (player) {
      this.state.players.delete(client.sessionId);
    }
  }

  onDispose() {
    console.log(`GardenRoom for owner ${this.ownerUid} disposed.`);
    if (this.growthCheckInterval) {
      this.growthCheckInterval.clear();
    }
  }
}
