import { Router, Request, Response } from 'express';
import ShopService from '../services/ShopService';
import CurrencyService from '../services/CurrencyService';
import InventoryService from '../services/InventoryService';
import WishlistService from '../services/WishlistService';
import { ShopError } from '../errors';

const router = Router();

/**
 * Get all shop categories
 */
router.get('/categories', async (req: Request, res: Response) => {
  try {
    const categories = await ShopService.getCategories();
    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Error fetching shop categories:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Get shop items by category
 */
router.get('/categories/:categoryId/items', async (req: Request, res: Response) => {
  try {
    const categoryId = parseInt(req.params.categoryId);
    if (isNaN(categoryId)) {
      throw ShopError.invalidShopData('Invalid category ID');
    }

    const items = await ShopService.getItemsByCategory(categoryId);
    res.json({
      success: true,
      data: items
    });
  } catch (error) {
    console.error('Error fetching shop items by category:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Get featured items
 */
router.get('/featured', async (req: Request, res: Response) => {
  try {
    const items = await ShopService.getFeaturedItems();
    res.json({
      success: true,
      data: items
    });
  } catch (error) {
    console.error('Error fetching featured items:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Get limited time items
 */
router.get('/limited-time', async (req: Request, res: Response) => {
  try {
    const items = await ShopService.getLimitedTimeItems();
    res.json({
      success: true,
      data: items
    });
  } catch (error) {
    console.error('Error fetching limited time items:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Search shop items
 */
router.get('/search', async (req: Request, res: Response) => {
  try {
    const query = req.query.q as string;
    const categoryId = req.query.categoryId ? parseInt(req.query.categoryId as string) : undefined;

    if (!query || query.trim().length === 0) {
      throw ShopError.invalidShopData('Search query is required');
    }

    const items = await ShopService.searchItems(query.trim(), categoryId);
    res.json({
      success: true,
      data: items
    });
  } catch (error) {
    console.error('Error searching shop items:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Get specific shop item
 */
router.get('/items/:itemId', async (req: Request, res: Response) => {
  try {
    const itemId = parseInt(req.params.itemId);
    if (isNaN(itemId)) {
      throw ShopError.invalidShopData('Invalid item ID');
    }

    const item = await ShopService.getShopItem(itemId);
    if (!item) {
      throw ShopError.itemNotFound(itemId);
    }

    res.json({
      success: true,
      data: item
    });
  } catch (error) {
    console.error('Error fetching shop item:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Check if user can purchase item
 */
router.get('/items/:itemId/can-purchase/:userId', async (req: Request, res: Response) => {
  try {
    const itemId = parseInt(req.params.itemId);
    const userId = req.params.userId;
    const quantity = parseInt(req.query.quantity as string) || 1;

    if (isNaN(itemId)) {
      throw ShopError.invalidShopData('Invalid item ID');
    }

    const eligibility = await ShopService.canPurchaseItem(userId, itemId, quantity);
    res.json({
      success: true,
      data: eligibility
    });
  } catch (error) {
    console.error('Error checking purchase eligibility:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Purchase item
 */
router.post('/purchase', async (req: Request, res: Response) => {
  try {
    const { userId, shopItemId, quantity = 1, giftRecipientId } = req.body;

    if (!userId || !shopItemId) {
      throw ShopError.invalidShopData('User ID and shop item ID are required');
    }

    if (quantity <= 0) {
      throw ShopError.invalidQuantity(quantity, 1);
    }

    const result = await ShopService.purchaseItem({
      userId,
      shopItemId: parseInt(shopItemId),
      quantity: parseInt(quantity),
      giftRecipientId
    });

    res.status(201).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error purchasing item:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Get user's currency
 */
router.get('/users/:userId/currency', async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    const currency = await CurrencyService.getUserCurrency(userId);
    res.json({
      success: true,
      data: currency
    });
  } catch (error) {
    console.error('Error fetching user currency:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Get user's inventory
 */
router.get('/users/:userId/inventory', async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    const type = req.query.type as string;
    const category = req.query.category as string;

    let inventory;
    if (type) {
      inventory = await InventoryService.getInventoryByType(userId, type);
    } else if (category) {
      inventory = await InventoryService.getInventoryByCategory(userId, category);
    } else {
      inventory = await InventoryService.getUserInventory(userId);
    }

    res.json({
      success: true,
      data: inventory
    });
  } catch (error) {
    console.error('Error fetching user inventory:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Get user's inventory statistics
 */
router.get('/users/:userId/inventory/stats', async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    const stats = await InventoryService.getInventoryStats(userId);
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching inventory stats:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Get user's purchase history
 */
router.get('/users/:userId/purchases', async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    const limit = parseInt(req.query.limit as string) || 50;
    const history = await ShopService.getPurchaseHistory(userId, limit);
    res.json({
      success: true,
      data: history
    });
  } catch (error) {
    console.error('Error fetching purchase history:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Get user's wishlist
 */
router.get('/users/:userId/wishlist', async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    const availableOnly = req.query.availableOnly === 'true';

    let wishlist;
    if (availableOnly) {
      wishlist = await WishlistService.getAvailableWishlistItems(userId);
    } else {
      wishlist = await WishlistService.getUserWishlist(userId);
    }

    res.json({
      success: true,
      data: wishlist
    });
  } catch (error) {
    console.error('Error fetching user wishlist:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Add item to wishlist
 */
router.post('/users/:userId/wishlist', async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    const { shopItemId } = req.body;

    if (!shopItemId) {
      throw ShopError.invalidShopData('Shop item ID is required');
    }

    const result = await WishlistService.addToWishlist(userId, parseInt(shopItemId));
    res.status(201).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error adding item to wishlist:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Remove item from wishlist
 */
router.delete('/users/:userId/wishlist/:shopItemId', async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    const shopItemId = parseInt(req.params.shopItemId);

    if (isNaN(shopItemId)) {
      throw ShopError.invalidShopData('Invalid shop item ID');
    }

    await WishlistService.removeFromWishlist(userId, shopItemId);
    res.json({
      success: true,
      message: 'Item removed from wishlist'
    });
  } catch (error) {
    console.error('Error removing item from wishlist:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Check if item is in wishlist
 */
router.get('/users/:userId/wishlist/:shopItemId/status', async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    const shopItemId = parseInt(req.params.shopItemId);

    if (isNaN(shopItemId)) {
      throw ShopError.invalidShopData('Invalid shop item ID');
    }

    const isInWishlist = await WishlistService.isInWishlist(userId, shopItemId);
    res.json({
      success: true,
      data: { isInWishlist }
    });
  } catch (error) {
    console.error('Error checking wishlist status:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Get wishlist statistics
 */
router.get('/users/:userId/wishlist/stats', async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    const stats = await WishlistService.getWishlistStats(userId);
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching wishlist stats:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Get expiring wishlist items
 */
router.get('/users/:userId/wishlist/expiring', async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    const hoursAhead = parseInt(req.query.hours as string) || 24;
    const items = await WishlistService.getExpiringWishlistItems(userId, hoursAhead);
    res.json({
      success: true,
      data: items
    });
  } catch (error) {
    console.error('Error fetching expiring wishlist items:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Clear entire wishlist
 */
router.delete('/users/:userId/wishlist', async (req: Request, res: Response) => {
  try {
    const userId = req.params.userId;
    await WishlistService.clearWishlist(userId);
    res.json({
      success: true,
      message: 'Wishlist cleared'
    });
  } catch (error) {
    console.error('Error clearing wishlist:', error);
    if (error instanceof ShopError) {
      return res.status(error.httpCode).json(error.toJSON());
    }
    res.status(500).json({
      success: false,
      error: { message: 'Internal server error' }
    });
  }
});

/**
 * Refresh shop cache (admin endpoint)
 */
router.post('/admin/refresh-cache', async (req: Request, res: Response) => {
  try {
    await ShopService.refreshCache();
    res.json({
      success: true,
      message: 'Shop cache refreshed successfully'
    });
  } catch (error) {
    console.error('Error refreshing shop cache:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to refresh shop cache' }
    });
  }
});

export default router;
