import { Schema, type } from '@colyseus/schema';

export class GardenPlotState extends Schema {
  @type('string') plotId: string = ''; // Unique ID for this plot instance in the garden
  @type('string') plotTemplateId: string = 'default_plot'; // e.g., 'small_plot', 'large_plot', 'hydroponic'
  @type('string') seedId: string = ''; // ID of the seed planted, empty if no seed
  @type('number') plantTimestamp: number = 0; // Unix timestamp when seed was planted
  @type('number') growthStage: number = 0; // e.g., 0: empty, 1: seeded, 2: sprouting, 3: growing, 4: mature/harvestable
  @type('number') lastWateredTimestamp: number = 0; // Unix timestamp
  // Future: Could add 'health', 'fertilizer_effect', etc.

  constructor(
    plotId: string,
    plotTemplateId: string = 'default_plot',
    seedId: string = '',
    plantTimestamp: number = 0,
    growthStage: number = 0,
    lastWateredTimestamp: number = 0
  ) {
    super();
    this.plotId = plotId;
    this.plotTemplateId = plotTemplateId;
    this.seedId = seedId;
    this.plantTimestamp = plantTimestamp;
    this.growthStage = growthStage;
    this.lastWateredTimestamp = lastWateredTimestamp;
  }
}
