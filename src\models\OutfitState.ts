import { Schema, type, MapSchema } from '@colyseus/schema';

/**
 * Individual outfit item state
 */
export class OutfitItemState extends Schema {
  @type('string') itemId: string = '';
  @type('string') itemTemplateId: string = '';
  @type('string') category: string = ''; // 'hair', 'top', 'bottom', 'shoes', 'accessory', etc.
  @type('string') subCategory: string = ''; // 'hat', 'glasses', 'necklace', etc.
  @type('string') color: string = '#FFFFFF'; // Hex color for customization
  @type('number') layer: number = 0; // Rendering layer order
  @type('boolean') isVisible: boolean = true;
  @type('string') customData: string = '{}'; // JSON string for additional customization

  constructor(
    itemId: string = '',
    itemTemplateId: string = '',
    category: string = '',
    subCategory: string = '',
    color: string = '#FFFFFF',
    layer: number = 0
  ) {
    super();
    this.itemId = itemId;
    this.itemTemplateId = itemTemplateId;
    this.category = category;
    this.subCategory = subCategory;
    this.color = color;
    this.layer = layer;
  }

  /**
   * Update item properties
   */
  updateItem(updates: Partial<{
    itemTemplateId: string;
    color: string;
    layer: number;
    isVisible: boolean;
    customData: any;
  }>): void {
    if (updates.itemTemplateId !== undefined) this.itemTemplateId = updates.itemTemplateId;
    if (updates.color !== undefined) this.color = updates.color;
    if (updates.layer !== undefined) this.layer = updates.layer;
    if (updates.isVisible !== undefined) this.isVisible = updates.isVisible;
    if (updates.customData !== undefined) this.customData = JSON.stringify(updates.customData);
  }

  /**
   * Get custom data as object
   */
  getCustomData(): any {
    try {
      return JSON.parse(this.customData);
    } catch {
      return {};
    }
  }

  /**
   * Clone this outfit item
   */
  clone(): this {
    const cloned = new OutfitItemState(
      this.itemId,
      this.itemTemplateId,
      this.category,
      this.subCategory,
      this.color,
      this.layer
    );
    cloned.isVisible = this.isVisible;
    cloned.customData = this.customData;
    return cloned as this;
  }
}

/**
 * Complete outfit state containing all equipped items
 */
export class OutfitState extends Schema {
  @type({ map: OutfitItemState }) items = new MapSchema<OutfitItemState>();
  @type('string') outfitName: string = 'Default Outfit';
  @type('string') outfitId: string = '';
  @type('boolean') isCustom: boolean = false;
  @type('number') lastUpdated: number = 0;

  constructor(outfitId: string = '', outfitName: string = 'Default Outfit') {
    super();
    this.outfitId = outfitId;
    this.outfitName = outfitName;
    this.lastUpdated = Date.now();
    this.initializeDefaultOutfit();
  }

  /**
   * Initialize with default outfit items
   */
  private initializeDefaultOutfit(): void {
    // Default body parts
    this.setItem('body', new OutfitItemState('body_default', 'body_default', 'body', 'base', '#FFDBAC', 0));
    this.setItem('hair', new OutfitItemState('hair_default', 'hair_default', 'hair', 'base', '#8B4513', 10));
    this.setItem('top', new OutfitItemState('top_default', 'top_default', 'clothing', 'top', '#4169E1', 20));
    this.setItem('bottom', new OutfitItemState('bottom_default', 'bottom_default', 'clothing', 'bottom', '#2F4F4F', 15));
    this.setItem('shoes', new OutfitItemState('shoes_default', 'shoes_default', 'clothing', 'shoes', '#8B4513', 5));
  }

  /**
   * Set an outfit item by slot
   */
  setItem(slot: string, item: OutfitItemState): void {
    this.items.set(slot, item);
    this.lastUpdated = Date.now();
  }

  /**
   * Get an outfit item by slot
   */
  getItem(slot: string): OutfitItemState | undefined {
    return this.items.get(slot);
  }

  /**
   * Remove an outfit item by slot
   */
  removeItem(slot: string): boolean {
    const removed = this.items.delete(slot);
    if (removed) {
      this.lastUpdated = Date.now();
    }
    return removed;
  }

  /**
   * Update an existing item in a slot
   */
  updateItem(slot: string, updates: Partial<{
    itemTemplateId: string;
    color: string;
    layer: number;
    isVisible: boolean;
    customData: any;
  }>): boolean {
    const item = this.items.get(slot);
    if (item) {
      item.updateItem(updates);
      this.lastUpdated = Date.now();
      return true;
    }
    return false;
  }

  /**
   * Equip a new item to a specific slot
   */
  equipItem(slot: string, itemTemplateId: string, options: {
    itemId?: string;
    color?: string;
    layer?: number;
    subCategory?: string;
    customData?: any;
  } = {}): void {
    const {
      itemId = `${slot}_${Date.now()}`,
      color = '#FFFFFF',
      layer = this.getDefaultLayerForSlot(slot),
      subCategory = slot,
      customData = {}
    } = options;

    const category = this.getCategoryForSlot(slot);
    const newItem = new OutfitItemState(itemId, itemTemplateId, category, subCategory, color, layer);
    newItem.customData = JSON.stringify(customData);

    this.setItem(slot, newItem);
  }

  /**
   * Unequip an item from a slot (remove it)
   */
  unequipItem(slot: string): boolean {
    return this.removeItem(slot);
  }

  /**
   * Get all equipped items as an array
   */
  getAllItems(): OutfitItemState[] {
    return Array.from(this.items.values());
  }

  /**
   * Get items by category
   */
  getItemsByCategory(category: string): OutfitItemState[] {
    return this.getAllItems().filter(item => item.category === category);
  }

  /**
   * Get items sorted by layer (for rendering order)
   */
  getItemsSortedByLayer(): OutfitItemState[] {
    return this.getAllItems().sort((a, b) => a.layer - b.layer);
  }

  /**
   * Check if a specific slot is equipped
   */
  hasItem(slot: string): boolean {
    return this.items.has(slot);
  }

  /**
   * Get the number of equipped items
   */
  getItemCount(): number {
    return this.items.size;
  }

  /**
   * Clear all items (reset to naked)
   */
  clearAllItems(): void {
    this.items.clear();
    this.lastUpdated = Date.now();
  }

  /**
   * Reset to default outfit
   */
  resetToDefault(): void {
    this.clearAllItems();
    this.initializeDefaultOutfit();
    this.outfitName = 'Default Outfit';
    this.isCustom = false;
  }

  /**
   * Copy outfit from another OutfitState
   */
  copyFrom(other: OutfitState): void {
    this.clearAllItems();
    other.items.forEach((item, slot) => {
      this.setItem(slot, item.clone());
    });
    this.outfitName = other.outfitName;
    this.isCustom = other.isCustom;
  }

  /**
   * Export outfit as a plain object (for saving to database)
   */
  toPlainObject(): any {
    const items: any = {};
    this.items.forEach((item, slot) => {
      items[slot] = {
        itemId: item.itemId,
        itemTemplateId: item.itemTemplateId,
        category: item.category,
        subCategory: item.subCategory,
        color: item.color,
        layer: item.layer,
        isVisible: item.isVisible,
        customData: item.getCustomData()
      };
    });

    return {
      outfitId: this.outfitId,
      outfitName: this.outfitName,
      isCustom: this.isCustom,
      lastUpdated: this.lastUpdated,
      items
    };
  }

  /**
   * Load outfit from a plain object (from database)
   */
  fromPlainObject(data: any): void {
    this.clearAllItems();

    this.outfitId = data.outfitId || '';
    this.outfitName = data.outfitName || 'Loaded Outfit';
    this.isCustom = data.isCustom || false;
    this.lastUpdated = data.lastUpdated || Date.now();

    if (data.items) {
      Object.entries(data.items).forEach(([slot, itemData]: [string, any]) => {
        const item = new OutfitItemState(
          itemData.itemId,
          itemData.itemTemplateId,
          itemData.category,
          itemData.subCategory,
          itemData.color,
          itemData.layer
        );
        item.isVisible = itemData.isVisible !== undefined ? itemData.isVisible : true;
        item.customData = JSON.stringify(itemData.customData || {});
        this.setItem(slot, item);
      });
    }
  }

  /**
   * Get default layer for a slot
   */
  private getDefaultLayerForSlot(slot: string): number {
    const layerMap: { [key: string]: number } = {
      'body': 0,
      'underwear': 1,
      'socks': 2,
      'shoes': 5,
      'bottom': 15,
      'top': 20,
      'jacket': 25,
      'necklace': 30,
      'hair': 10,
      'hat': 35,
      'glasses': 40,
      'mask': 45,
      'wings': 50,
      'tail': 3,
      'gloves': 22
    };
    return layerMap[slot] || 100;
  }

  /**
   * Get category for a slot
   */
  private getCategoryForSlot(slot: string): string {
    const categoryMap: { [key: string]: string } = {
      'body': 'body',
      'hair': 'hair',
      'top': 'clothing',
      'bottom': 'clothing',
      'shoes': 'clothing',
      'hat': 'accessory',
      'glasses': 'accessory',
      'necklace': 'accessory',
      'gloves': 'accessory',
      'wings': 'accessory',
      'tail': 'accessory',
      'mask': 'accessory',
      'jacket': 'clothing',
      'underwear': 'clothing',
      'socks': 'clothing'
    };
    return categoryMap[slot] || 'accessory';
  }

  /**
   * Validate outfit (check for conflicts, missing required items, etc.)
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for required items
    if (!this.hasItem('body')) {
      errors.push('Body item is required');
    }

    // Check for layer conflicts
    const layers = this.getAllItems().map(item => item.layer);
    const duplicateLayers = layers.filter((layer, index) => layers.indexOf(layer) !== index);
    if (duplicateLayers.length > 0) {
      errors.push(`Duplicate layers found: ${duplicateLayers.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
