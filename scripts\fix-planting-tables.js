const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixPlantingTables() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🔧 Fixing Planting System Tables...');
    
    // Drop the old garden_plots table that has wrong structure
    console.log('🗑️  Dropping old garden_plots table...');
    await connection.query('DROP TABLE IF EXISTS garden_plots');
    console.log('✅ Old garden_plots table dropped');
    
    // Recreate the correct garden_plots table
    console.log('📊 Creating new garden_plots table...');
    await connection.query(`
      CREATE TABLE \`garden_plots\` (
        \`id\` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique plot ID',
        \`user_id\` BIGINT NOT NULL COMMENT 'Garden owner user ID',
        \`plot_index\` INT NOT NULL COMMENT 'Plot position index in garden (0-based)',
        \`is_unlocked\` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether this plot is unlocked',
        \`unlock_level_required\` INT NOT NULL DEFAULT 1 COMMENT 'Required planting level to unlock',
        \`unlock_cost_gold\` INT NOT NULL DEFAULT 0 COMMENT 'Gold cost to unlock',
        \`unlock_cost_diamonds\` INT NOT NULL DEFAULT 0 COMMENT 'Diamond cost to unlock',
        \`unlocked_at\` TIMESTAMP NULL COMMENT 'When plot was unlocked',
        \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY \`unique_user_plot\` (\`user_id\`, \`plot_index\`),
        INDEX \`idx_user_unlocked\` (\`user_id\`, \`is_unlocked\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Garden plots for each user'
    `);
    console.log('✅ New garden_plots table created');
    
    // Verify the table structure
    const [columns] = await connection.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'garden_plots'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME]);
    
    console.log('\n🌱 New garden_plots table structure:');
    columns.forEach(col => {
      console.log(`  - ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });
    
    // Check if user_id column exists
    const userIdColumn = columns.find(col => col.COLUMN_NAME === 'user_id');
    if (userIdColumn) {
      console.log('✅ user_id column exists in garden_plots table');
    } else {
      console.log('❌ user_id column missing in garden_plots table');
    }
    
    console.log('\n🎉 Planting tables fixed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing planting tables:', error);
  } finally {
    await connection.end();
  }
}

fixPlantingTables();
