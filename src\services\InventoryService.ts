import pool from '../utils/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';
import { ShopError } from '../errors';
import ItemService from './ItemService';

/**
 * Enhanced user inventory item interface
 */
export interface InventoryItem extends RowDataPacket {
  id: number;
  user_id: string;
  item_id: string;
  quantity: number;
  is_locked: boolean;
  is_new: boolean;
  is_equipped: boolean;
  custom_data: any;
  acquired_at: Date;
  updated_at: Date;
}

/**
 * Inventory item with details interface
 */
export interface InventoryItemWithDetails extends InventoryItem {
  item_name: string;
  item_description: string | null;
  item_type: string;
  item_category: string | null;
  is_stackable: boolean;
  max_stack_size: number;
  item_data: string | null;
}

/**
 * Inventory operation types for logging
 */
export type InventoryOperationType =
  | 'add'
  | 'remove'
  | 'use'
  | 'sell'
  | 'gift_send'
  | 'gift_receive'
  | 'equip'
  | 'unequip'
  | 'lock'
  | 'unlock'
  | 'destroy';

/**
 * Inventory operation result
 */
export interface InventoryOperationResult {
  success: boolean;
  message: string;
  item?: InventoryItem;
  quantityBefore?: number;
  quantityAfter?: number;
}

/**
 * Inventory service for managing player inventories
 */
class InventoryService {
  private static instance: InventoryService;

  private constructor() {}

  public static getInstance(): InventoryService {
    if (!InventoryService.instance) {
      InventoryService.instance = new InventoryService();
    }
    return InventoryService.instance;
  }

  /**
   * Get user's inventory with enhanced filtering and sorting
   */
  async getUserInventory(userId: string, options: {
    itemType?: string;
    category?: string;
    isEquipped?: boolean;
    isNew?: boolean;
    limit?: number;
    offset?: number;
    sortBy?: 'acquired_at' | 'name' | 'quantity' | 'rarity';
    sortOrder?: 'ASC' | 'DESC';
  } = {}): Promise<InventoryItemWithDetails[]> {
    if (!userId || typeof userId !== 'string') {
      throw new Error('InventoryService: getUserInventory - userId must be a non-empty string.');
    }

    try {
      let query = `
        SELECT
          ui.id, ui.user_id, ui.item_id, ui.quantity, ui.is_locked, ui.is_new, ui.is_equipped, ui.custom_data, ui.acquired_at, ui.updated_at,
          i.item_name, i.description as item_description, i.item_type, i.category as item_category,
          i.is_stackable, i.max_stack_size, i.data as item_data
        FROM user_inventory ui
        JOIN items i ON ui.item_id = i.item_id
        WHERE ui.user_id = ?
      `;
      const params: any[] = [userId];

      // Add filters
      if (options.itemType) {
        query += ' AND i.type = ?';
        params.push(options.itemType);
      }

      if (options.category) {
        query += ' AND i.category = ?';
        params.push(options.category);
      }

      if (options.isEquipped !== undefined) {
        query += ' AND ui.is_equipped = ?';
        params.push(options.isEquipped);
      }

      if (options.isNew !== undefined) {
        query += ' AND ui.is_new = ?';
        params.push(options.isNew);
      }

      // Add sorting
      const sortBy = options.sortBy || 'acquired_at';
      const sortOrder = options.sortOrder || 'DESC';

      switch (sortBy) {
        case 'name':
          query += ` ORDER BY i.item_name ${sortOrder}`;
          break;
        case 'quantity':
          query += ` ORDER BY ui.quantity ${sortOrder}`;
          break;
        case 'rarity':
          query += ` ORDER BY FIELD(JSON_UNQUOTE(JSON_EXTRACT(i.data, '$.rarity')), 'common', 'uncommon', 'rare', 'epic', 'legendary') ${sortOrder}`;
          break;
        default:
          query += ` ORDER BY ui.${sortBy} ${sortOrder}`;
      }

      // Add pagination
      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);

        if (options.offset) {
          query += ' OFFSET ?';
          params.push(options.offset);
        }
      }

      const [rows] = await pool.query<InventoryItemWithDetails[]>(query, params);
      return rows;
    } catch (error: any) {
      console.error(`InventoryService: Error getting user inventory for ${userId}:`, error);
      throw ShopError.inventoryOperationFailed('get_inventory', `Database error: ${error.message}`);
    }
  }

  /**
   * Get specific item quantity in user's inventory
   */
  async getItemQuantity(userId: string, itemId: string): Promise<number> {
    try {
      const [rows] = await pool.query<InventoryItem[]>(
        'SELECT quantity FROM user_inventory WHERE user_id = ? AND item_id = ?',
        [userId, itemId]
      );

      return rows.length > 0 ? rows[0].quantity : 0;
    } catch (error) {
      console.error('Failed to get item quantity:', error);
      throw ShopError.inventoryOperationFailed('get_item_quantity', 'Database error');
    }
  }

  /**
   * Check if user has sufficient quantity of an item
   */
  async hasItem(userId: string, itemId: string, requiredQuantity: number = 1): Promise<boolean> {
    try {
      const currentQuantity = await this.getItemQuantity(userId, itemId);
      return currentQuantity >= requiredQuantity;
    } catch (error) {
      console.error('Failed to check item availability:', error);
      return false;
    }
  }

  /**
   * Add item to user's inventory with enhanced tracking
   */
  async addItem(
    userId: string,
    itemId: string,
    quantity: number = 1,
    source: string = 'unknown',
    customData?: any
  ): Promise<InventoryOperationResult> {
    if (!userId || !itemId || quantity <= 0) {
      throw ShopError.invalidQuantity(quantity, 1);
    }

    // Validate item exists
    const itemDetails = await ItemService.getItemDetails(itemId);
    if (!itemDetails) {
      throw ShopError.itemNotFound(itemId);
    }

    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Check if item already exists in inventory
      const [existingRows] = await connection.query<InventoryItem[]>(
        'SELECT * FROM user_inventory WHERE user_id = ? AND item_id = ? FOR UPDATE',
        [userId, itemId]
      );

      let quantityBefore = 0;
      let quantityAfter = quantity;
      let result: InventoryItem;

      if (existingRows.length > 0 && itemDetails.is_stackable) {
        // Update existing stackable item
        const existing = existingRows[0];
        quantityBefore = existing.quantity;
        quantityAfter = quantityBefore + quantity;

        // Check stack limit
        if (itemDetails.max_stack_size > 0 && quantityAfter > itemDetails.max_stack_size) {
          throw ShopError.invalidQuantity(
            quantityAfter,
            1,
            itemDetails.max_stack_size
          );
        }

        await connection.query<ResultSetHeader>(
          'UPDATE user_inventory SET quantity = ?, is_new = TRUE, updated_at = NOW() WHERE id = ?',
          [quantityAfter, existing.id]
        );

        result = { ...existing, quantity: quantityAfter, is_new: true };
      } else {
        // Add new inventory entry
        const [insertResult] = await connection.query<ResultSetHeader>(
          `INSERT INTO user_inventory (user_id, item_id, quantity, custom_data, is_new)
           VALUES (?, ?, ?, ?, TRUE)`,
          [userId, itemId, quantity, customData ? JSON.stringify(customData) : null]
        );

        if (insertResult.affectedRows === 0) {
          throw ShopError.inventoryOperationFailed('add_item', 'Failed to insert item');
        }

        // Get the inserted item
        const [newRows] = await connection.query<InventoryItem[]>(
          'SELECT * FROM user_inventory WHERE id = ?',
          [insertResult.insertId]
        );

        result = newRows[0];
      }

      // Log the operation
      await this.logInventoryOperation(
        connection,
        userId,
        itemId,
        'add',
        quantity,
        quantityBefore,
        quantityAfter,
        source
      );

      await connection.commit();

      return {
        success: true,
        message: `Added ${quantity} ${itemDetails.name} to inventory.`,
        item: result,
        quantityBefore,
        quantityAfter
      };

    } catch (error) {
      await connection.rollback();
      console.error('Failed to add item to inventory:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('add_item', 'Transaction failed');
    } finally {
      connection.release();
    }
  }

  /**
   * Remove item from user's inventory
   */
  async removeItem(
    userId: string,
    itemId: string,
    quantity: number = 1,
    operationType: InventoryOperationType = 'remove',
    source: string = 'unknown'
  ): Promise<InventoryOperationResult> {
    if (quantity <= 0) {
      throw ShopError.invalidQuantity(quantity, 1);
    }

    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Get current inventory item
      const [existingRows] = await connection.query<InventoryItem[]>(
        'SELECT * FROM user_inventory WHERE user_id = ? AND item_id = ? FOR UPDATE',
        [userId, itemId]
      );

      if (existingRows.length === 0) {
        throw ShopError.inventoryOperationFailed('remove_item', 'Item not found in inventory');
      }

      const existing = existingRows[0];

      // Check if item is locked
      if (existing.is_locked && operationType !== 'unlock') {
        throw ShopError.inventoryOperationFailed('remove_item', 'Cannot modify locked item');
      }

      if (existing.quantity < quantity) {
        throw ShopError.inventoryOperationFailed(
          'remove_item',
          `Insufficient quantity. Available: ${existing.quantity}, Required: ${quantity}`
        );
      }

      const quantityBefore = existing.quantity;
      const quantityAfter = quantityBefore - quantity;
      let result: InventoryItem;

      if (quantityAfter === 0) {
        // Remove item completely
        await connection.query<ResultSetHeader>(
          'DELETE FROM user_inventory WHERE id = ?',
          [existing.id]
        );
        result = { ...existing, quantity: 0 };
      } else {
        // Update quantity
        await connection.query<ResultSetHeader>(
          'UPDATE user_inventory SET quantity = ?, updated_at = NOW() WHERE id = ?',
          [quantityAfter, existing.id]
        );
        result = { ...existing, quantity: quantityAfter };
      }

      // Log the operation
      await this.logInventoryOperation(
        connection,
        userId,
        itemId,
        operationType,
        -quantity,
        quantityBefore,
        quantityAfter,
        source
      );

      await connection.commit();

      // Get item details for the message
      const itemDetails = await ItemService.getItemDetails(itemId);
      return {
        success: true,
        message: `Removed ${quantity} ${itemDetails?.name || itemId} from inventory.`,
        item: result,
        quantityBefore,
        quantityAfter
      };

    } catch (error) {
      await connection.rollback();
      console.error('Failed to remove item from inventory:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('remove_item', 'Transaction failed');
    } finally {
      connection.release();
    }
  }

  /**
   * Transfer item between users (for gifting)
   */
  async transferItem(
    fromUserId: string,
    toUserId: string,
    itemId: string,
    quantity: number = 1
  ): Promise<{ fromItem: InventoryOperationResult; toItem: InventoryOperationResult }> {
    if (fromUserId === toUserId) {
      throw ShopError.cannotGiftToSelf();
    }

    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Remove from sender
      const fromItem = await this.removeItem(fromUserId, itemId, quantity, 'gift_send', 'gift_transfer');

      // Add to recipient
      const toItem = await this.addItem(toUserId, itemId, quantity, 'gift_transfer');

      await connection.commit();

      return { fromItem, toItem };

    } catch (error) {
      await connection.rollback();
      console.error('Failed to transfer item:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('transfer_item', 'Transaction failed');
    } finally {
      connection.release();
    }
  }

  /**
   * Get inventory items by category
   */
  async getInventoryByCategory(userId: string, category: string): Promise<InventoryItemWithDetails[]> {
    try {
      const [rows] = await pool.query<InventoryItemWithDetails[]>(
        `SELECT
          ui.id, ui.user_id, ui.item_id, ui.quantity, ui.acquired_at, ui.updated_at,
          i.item_name, i.description as item_description, i.item_type, i.category as item_category,
          i.is_stackable, i.max_stack_size, i.data as item_data
         FROM user_inventory ui
         JOIN items i ON ui.item_id = i.item_id
         WHERE ui.user_id = ? AND i.category = ?
         ORDER BY ui.acquired_at DESC`,
        [userId, category]
      );

      return rows;
    } catch (error) {
      console.error('Failed to get inventory by category:', error);
      throw ShopError.inventoryOperationFailed('get_inventory_by_category', 'Database error');
    }
  }

  /**
   * Get inventory items by type
   */
  async getInventoryByType(userId: string, type: string): Promise<InventoryItemWithDetails[]> {
    try {
      const [rows] = await pool.query<InventoryItemWithDetails[]>(
        `SELECT
          ui.id, ui.user_id, ui.item_id, ui.quantity, ui.acquired_at, ui.updated_at,
          i.item_name, i.description as item_description, i.item_type, i.category as item_category,
          i.is_stackable, i.max_stack_size, i.data as item_data
         FROM user_inventory ui
         JOIN items i ON ui.item_id = i.item_id
         WHERE ui.user_id = ? AND i.item_type = ?
         ORDER BY ui.acquired_at DESC`,
        [userId, type]
      );

      return rows;
    } catch (error) {
      console.error('Failed to get inventory by type:', error);
      throw ShopError.inventoryOperationFailed('get_inventory_by_type', 'Database error');
    }
  }

  /**
   * Batch add multiple items to inventory
   */
  async batchAddItems(userId: string, items: Array<{ itemId: string; quantity: number }>): Promise<InventoryOperationResult[]> {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      const results: InventoryOperationResult[] = [];

      for (const { itemId, quantity } of items) {
        const result = await this.addItem(userId, itemId, quantity, 'batch_add');
        results.push(result);
      }

      await connection.commit();
      return results;

    } catch (error) {
      await connection.rollback();
      console.error('Failed to batch add items:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('batch_add_items', 'Transaction failed');
    } finally {
      connection.release();
    }
  }

  /**
   * Get inventory statistics
   */
  async getInventoryStats(userId: string): Promise<{
    totalItems: number;
    totalUniqueItems: number;
    itemsByType: Record<string, number>;
    itemsByCategory: Record<string, number>;
  }> {
    try {
      const [totalRows] = await pool.query<RowDataPacket[]>(
        `SELECT
          SUM(ui.quantity) as total_items,
          COUNT(DISTINCT ui.item_id) as unique_items
         FROM user_inventory ui
         WHERE ui.user_id = ?`,
        [userId]
      );

      const [typeRows] = await pool.query<RowDataPacket[]>(
        `SELECT
          i.item_type,
          SUM(ui.quantity) as count
         FROM user_inventory ui
         JOIN items i ON ui.item_id = i.item_id
         WHERE ui.user_id = ?
         GROUP BY i.item_type`,
        [userId]
      );

      const [categoryRows] = await pool.query<RowDataPacket[]>(
        `SELECT
          i.category,
          SUM(ui.quantity) as count
         FROM user_inventory ui
         JOIN items i ON ui.item_id = i.item_id
         WHERE ui.user_id = ? AND i.category IS NOT NULL
         GROUP BY i.category`,
        [userId]
      );

      const itemsByType: Record<string, number> = {};
      typeRows.forEach(row => {
        itemsByType[row.item_type] = row.count;
      });

      const itemsByCategory: Record<string, number> = {};
      categoryRows.forEach(row => {
        if (row.category) {
          itemsByCategory[row.category] = row.count;
        }
      });

      return {
        totalItems: totalRows[0]?.total_items || 0,
        totalUniqueItems: totalRows[0]?.unique_items || 0,
        itemsByType,
        itemsByCategory
      };

    } catch (error) {
      console.error('Failed to get inventory stats:', error);
      throw ShopError.inventoryOperationFailed('get_inventory_stats', 'Database error');
    }
  }
  /**
   * Search inventory items by name or description
   */
  async searchInventory(userId: string, options: {
    query: string;
    itemType?: string;
    category?: string;
    limit?: number;
    offset?: number;
  }): Promise<InventoryItemWithDetails[]> {
    if (!userId || !options.query) {
      throw new Error('InventoryService: searchInventory - userId and query are required.');
    }

    try {
      let query = `
        SELECT
          ui.id, ui.user_id, ui.item_id, ui.quantity, ui.is_locked, ui.is_new, ui.is_equipped, ui.custom_data, ui.acquired_at, ui.updated_at,
          i.item_name, i.description as item_description, i.item_type, i.category as item_category,
          i.is_stackable, i.max_stack_size, i.data as item_data
        FROM user_inventory ui
        JOIN items i ON ui.item_id = i.item_id
        WHERE ui.user_id = ? AND (i.item_name LIKE ? OR i.description LIKE ?)
      `;
      const params: any[] = [userId, `%${options.query}%`, `%${options.query}%`];

      // Add additional filters
      if (options.itemType) {
        query += ' AND i.type = ?';
        params.push(options.itemType);
      }

      if (options.category) {
        query += ' AND i.category = ?';
        params.push(options.category);
      }

      // Add ordering and pagination
      query += ' ORDER BY ui.acquired_at DESC';

      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);

        if (options.offset) {
          query += ' OFFSET ?';
          params.push(options.offset);
        }
      }

      const [rows] = await pool.query<InventoryItemWithDetails[]>(query, params);
      return rows;
    } catch (error) {
      console.error('Failed to search inventory:', error);
      throw ShopError.inventoryOperationFailed('search_inventory', 'Database error');
    }
  }

  /**
   * Check and use item (atomic operation)
   */
  async checkAndUseItem(
    userId: string,
    itemId: string,
    quantity: number = 1,
    source: string = 'unknown'
  ): Promise<InventoryOperationResult> {
    return await this.removeItem(userId, itemId, quantity, 'use', source);
  }

  /**
   * Update item properties
   */
  async updateItem(
    userId: string,
    itemId: string,
    updates: {
      isLocked?: boolean;
      isNew?: boolean;
      isEquipped?: boolean;
      customData?: any;
    }
  ): Promise<InventoryOperationResult> {
    if (!userId || !itemId) {
      throw new Error('InventoryService: updateItem - userId and itemId are required.');
    }

    try {
      const setClauses: string[] = [];
      const params: any[] = [];

      if (updates.isLocked !== undefined) {
        setClauses.push('is_locked = ?');
        params.push(updates.isLocked);
      }

      if (updates.isNew !== undefined) {
        setClauses.push('is_new = ?');
        params.push(updates.isNew);
      }

      if (updates.isEquipped !== undefined) {
        setClauses.push('is_equipped = ?');
        params.push(updates.isEquipped);
      }

      if (updates.customData !== undefined) {
        setClauses.push('custom_data = ?');
        params.push(JSON.stringify(updates.customData));
      }

      if (setClauses.length === 0) {
        throw new Error('No valid updates provided.');
      }

      setClauses.push('updated_at = NOW()');
      params.push(userId, itemId);

      const [result] = await pool.query<ResultSetHeader>(
        `UPDATE user_inventory SET ${setClauses.join(', ')} WHERE user_id = ? AND item_id = ?`,
        params
      );

      if (result.affectedRows === 0) {
        throw ShopError.inventoryOperationFailed('update_item', 'Item not found in inventory');
      }

      // Log the operation
      const operationType: InventoryOperationType = updates.isLocked !== undefined
        ? (updates.isLocked ? 'lock' : 'unlock')
        : updates.isEquipped !== undefined
        ? (updates.isEquipped ? 'equip' : 'unequip')
        : 'remove'; // Generic update

      await this.logInventoryOperation(
        pool,
        userId,
        itemId,
        operationType,
        0,
        0,
        0,
        'item_update'
      );

      const updatedItem = await this.getInventoryItem(userId, itemId);

      return {
        success: true,
        message: 'Item updated successfully.',
        item: updatedItem!
      };

    } catch (error: any) {
      console.error(`InventoryService: Error updating item ${itemId} for user ${userId}:`, error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('update_item', `Failed to update item: ${error.message}`);
    }
  }

  /**
   * Get specific inventory item
   */
  async getInventoryItem(userId: string, itemId: string): Promise<InventoryItem | null> {
    if (!userId || !itemId) {
      throw new Error('InventoryService: getInventoryItem - userId and itemId are required.');
    }

    try {
      const [rows] = await pool.query<InventoryItem[]>(
        `SELECT ui.*, i.item_name, i.item_type, i.category, i.is_stackable, i.max_stack_size, i.data as item_data
         FROM user_inventory ui
         JOIN items i ON ui.item_id = i.item_id
         WHERE ui.user_id = ? AND ui.item_id = ?`,
        [userId, itemId]
      );

      return rows.length > 0 ? rows[0] : null;
    } catch (error: any) {
      console.error(`InventoryService: Error getting inventory item ${itemId} for user ${userId}:`, error);
      throw ShopError.inventoryOperationFailed('get_inventory_item', `Database error: ${error.message}`);
    }
  }

  /**
   * Log inventory operation
   */
  private async logInventoryOperation(
    connection: any,
    userId: string,
    itemId: string,
    operationType: InventoryOperationType,
    quantityChange: number,
    quantityBefore: number,
    quantityAfter: number,
    source: string,
    metadata?: any
  ): Promise<void> {
    try {
      await connection.query(
        `INSERT INTO inventory_operations_log
         (user_id, item_id, operation_type, quantity_change, quantity_before, quantity_after, operation_source, metadata)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          userId,
          itemId,
          operationType,
          quantityChange,
          quantityBefore,
          quantityAfter,
          source,
          metadata ? JSON.stringify(metadata) : null
        ]
      );
    } catch (error) {
      console.error('InventoryService: Error logging inventory operation:', error);
      // Don't throw here as it's just logging
    }
  }

  /**
   * Get inventory operation history
   */
  async getInventoryHistory(
    userId: string,
    options: {
      itemId?: string;
      operationType?: InventoryOperationType;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<RowDataPacket[]> {
    try {
      let query = `
        SELECT iol.*, i.item_name
        FROM inventory_operations_log iol
        JOIN items i ON iol.item_id = i.item_id
        WHERE iol.user_id = ?
      `;
      const params: any[] = [userId];

      if (options.itemId) {
        query += ' AND iol.item_id = ?';
        params.push(options.itemId);
      }

      if (options.operationType) {
        query += ' AND iol.operation_type = ?';
        params.push(options.operationType);
      }

      query += ' ORDER BY iol.created_at DESC';

      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);

        if (options.offset) {
          query += ' OFFSET ?';
          params.push(options.offset);
        }
      }

      const [rows] = await pool.query<RowDataPacket[]>(query, params);
      return rows;
    } catch (error: any) {
      console.error(`InventoryService: Error getting inventory history for user ${userId}:`, error);
      throw ShopError.inventoryOperationFailed('get_inventory_history', `Database error: ${error.message}`);
    }
  }
}

export default InventoryService.getInstance();
