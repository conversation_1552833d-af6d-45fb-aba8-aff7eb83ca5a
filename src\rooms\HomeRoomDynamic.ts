import { Client } from '@colyseus/core';
import { HomeRoomState } from '../models/HomeRoomState';
import { PlayerState } from '../models/PlayerState';
import { HomeItemState } from '../models/HomeItemState';
import { BasePrivateRoom, PrivateRoomOptions } from './BasePrivateRoom';
import SpaceService, { HomeItemRecord } from '../services/SpaceService';
import ItemService from '../services/ItemService';
import { ColyseusErrorHandler } from '../middleware/errorHandler';
import {
  ValidationError,
  PermissionDeniedError,
  NotFoundError,
  InvalidFieldTypeError,
  MissingFieldError
} from '../errors';

/**
 * Enhanced HomeRoom with dynamic room ID support
 * Extends BasePrivateRoom for automatic room management
 */
export class HomeRoomDynamic extends BasePrivateRoom<HomeRoomState> {
  private spaceServiceInstance!: typeof SpaceService;
  private itemServiceInstance!: typeof ItemService;

  /**
   * Called when the room is created with dynamic room support
   */
  async onCreate(options: PrivateRoomOptions) {
    try {
      // Initialize the room with dynamic ID support
      await this.initializeRoom(options, 'home');

      // Initialize service instances
      this.spaceServiceInstance = SpaceService;
      this.itemServiceInstance = ItemService;

      // Initialize room state
      await this.initializeRoomState();

      // Register message handlers
      this.registerMessageHandlers();

      console.log(`HomeRoomDynamic created successfully: ${this.getDynamicRoomId()}`);
    } catch (error) {
      console.error('HomeRoomDynamic creation failed:', error);
      throw error;
    }
  }

  /**
   * Initialize the room state with owner's home data
   */
  protected async initializeRoomState(): Promise<void> {
    // Set the initial state of the room
    this.setState(new HomeRoomState(this.getOwnerUid()));

    try {
      // Load the persisted home data for the owner
      const spaceData = await this.spaceServiceInstance.getFullPrivateSpaceData(this.getOwnerUid());

      this.state.homeBackgroundId = spaceData.settings.home_background_id;
      this.state.accessLevel = spaceData.settings.home_access_level;

      // Populate furniture from persisted data
      spaceData.homeItems.forEach((itemRecord: HomeItemRecord) => {
        const homeItem = new HomeItemState(
          itemRecord.item_instance_id,
          itemRecord.item_template_id,
          itemRecord.pos_x,
          itemRecord.pos_y,
          itemRecord.rotation,
          itemRecord.is_flipped
        );
        this.state.furniture.set(itemRecord.item_instance_id, homeItem);
      });

      console.log(`HomeRoomDynamic state initialized: Background: ${this.state.homeBackgroundId}, Access: ${this.state.accessLevel}, Items: ${this.state.furniture.size}`);
    } catch (error) {
      console.error('Failed to initialize home room state:', error);
      throw error;
    }
  }

  /**
   * Register message handlers for home-specific actions
   */
  protected registerMessageHandlers(): void {
    // Place item handler
    this.onMessage("place_item", async (client, message: { templateId: string; x: number; y: number; rotation?: number; isFlipped?: boolean }) => {
      const result = await ColyseusErrorHandler.safeAsync(client, async () => {
        // Permission check
        if (!this.isOwner(client.auth.uid)) {
          throw new PermissionDeniedError("place items", "home");
        }

        const { templateId, x, y, rotation, isFlipped } = message;

        // Validation
        if (!templateId) throw new MissingFieldError('templateId');
        if (typeof templateId !== 'string') throw new InvalidFieldTypeError('templateId', 'string', typeof templateId);
        if (typeof x !== 'number') throw new InvalidFieldTypeError('x', 'number', typeof x);
        if (typeof y !== 'number') throw new InvalidFieldTypeError('y', 'number', typeof y);
        if (rotation !== undefined && typeof rotation !== 'number') throw new InvalidFieldTypeError('rotation', 'number', typeof rotation);
        if (isFlipped !== undefined && typeof isFlipped !== 'boolean') throw new InvalidFieldTypeError('isFlipped', 'boolean', typeof isFlipped);

        // Call SpaceService to handle the logic
        const serviceResult = await this.spaceServiceInstance.placeHomeItem(
          this.getOwnerUid(), templateId, x, y, rotation, isFlipped
        );

        if (serviceResult.success) {
          const newItemState = new HomeItemState(
            serviceResult.itemInstance.item_instance_id,
            serviceResult.itemInstance.item_template_id,
            serviceResult.itemInstance.pos_x,
            serviceResult.itemInstance.pos_y,
            serviceResult.itemInstance.rotation,
            serviceResult.itemInstance.is_flipped
          );
          this.state.furniture.set(newItemState.instanceId, newItemState);
          return newItemState;
        } else {
          throw new Error(serviceResult.message || "Failed to place item.");
        }
      }, "Failed to place item");
    });

    // Update item details handler
    this.onMessage("update_item_details", async (client, message: {instanceId: string, x?: number, y?: number, rotation?: number, isFlipped?: boolean}) => {
      if (!this.isOwner(client.auth.uid)) {
        return client.error(403, "Permission denied: Only the room owner can update items.");
      }

      const { instanceId, x, y, rotation, isFlipped } = message;
      if (typeof instanceId !== 'string' || (x === undefined && y === undefined && rotation === undefined && isFlipped === undefined)) {
        return client.error(400, "Invalid parameters: 'instanceId' and at least one update field are required.");
      }

      try {
        const updates: Partial<Pick<HomeItemState, 'x' | 'y' | 'rotation' | 'isFlipped'>> = {};
        if (x !== undefined) updates.x = x;
        if (y !== undefined) updates.y = y;
        if (rotation !== undefined) updates.rotation = rotation;
        if (isFlipped !== undefined) updates.isFlipped = isFlipped;

        await this.spaceServiceInstance.updateHomeItemDetails(this.getOwnerUid(), instanceId, updates);

        // Update room state
        const itemToUpdate = this.state.furniture.get(instanceId);
        if (itemToUpdate) {
          if (updates.x !== undefined) itemToUpdate.x = updates.x;
          if (updates.y !== undefined) itemToUpdate.y = updates.y;
          if (updates.rotation !== undefined) itemToUpdate.rotation = updates.rotation;
          if (updates.isFlipped !== undefined) itemToUpdate.isFlipped = updates.isFlipped;
        }
      } catch (e: any) {
        console.error(`Error updating item details:`, e);
        client.error(500, e.message || "Server error: Could not update item details.");
      }
    });

    // Remove item handler
    this.onMessage("remove_item", async (client, message: { instanceId: string }) => {
      if (!this.isOwner(client.auth.uid)) {
        return client.error(403, "Permission denied: Only the room owner can remove items.");
      }

      const { instanceId } = message;
      if (typeof instanceId !== 'string') {
        return client.error(400, "Invalid parameters: 'instanceId' is required.");
      }

      try {
        const result = await this.spaceServiceInstance.removeHomeItem(this.getOwnerUid(), instanceId);

        if (result.success) {
          this.state.furniture.delete(instanceId);
          client.send("item_returned_to_inventory", {
            itemId: result.returnedItemId,
            message: `Item ${result.returnedItemId} (instance ${instanceId}) removed and returned to your inventory.`
          });
        } else {
          client.error(500, result.message || "Failed to remove item.");
        }
      } catch (e: any) {
        console.error(`Error removing item:`, e);
        client.error(500, e.message || "Server error: Could not remove item.");
      }
    });

    // Update background handler
    this.onMessage("update_background", async (client, message: { backgroundItemId: string }) => {
      if (!this.isOwner(client.auth.uid)) {
        return client.error(403, "Permission denied: Only the room owner can change the background.");
      }

      const { backgroundItemId } = message;
      if (typeof backgroundItemId !== 'string') {
        return client.error(400, "Invalid parameters: 'backgroundItemId' is required.");
      }

      try {
        await this.spaceServiceInstance.updateSpaceBackground(this.getOwnerUid(), 'home', backgroundItemId);
        this.state.homeBackgroundId = backgroundItemId;
      } catch (e: any) {
        console.error(`Error updating background:`, e);
        client.error(500, e.message || "Server error: Could not update background.");
      }
    });

    // Update access level handler
    this.onMessage("update_access_level", async (client, message: { accessLevel: 'private' | 'friends_only' | 'public' }) => {
      if (!this.isOwner(client.auth.uid)) {
        return client.error(403, "Permission denied: Only the room owner can change access level.");
      }

      const { accessLevel } = message;
      if (!['private', 'friends_only', 'public'].includes(accessLevel)) {
        return client.error(400, "Invalid access level provided.");
      }

      try {
        await this.spaceServiceInstance.updateSpaceAccessLevel(this.getOwnerUid(), 'home', accessLevel);
        this.state.accessLevel = accessLevel;
        
        // Update in room registry
        await this.updateAccessLevel(accessLevel);
      } catch (e: any) {
        console.error(`Error updating access level:`, e);
        client.error(500, e.message || "Server error: Could not update access level.");
      }
    });

    // Player state update handler
    this.onMessage('updatePlayerState', (client, data) => {
      const player = this.state.players.get(client.sessionId);
      if (player) {
        if (data.x !== undefined) player.x = Number(data.x);
        if (data.y !== undefined) player.y = Number(data.y);
        if (data.dir !== undefined) player.dir = String(data.dir);
        if (data.isFlipped !== undefined) player.isFlipped = Boolean(data.isFlipped);
        if (data.isSitting !== undefined) player.isSitting = Boolean(data.isSitting);
        if (data.currentAnimation !== undefined) player.currentAnimation = String(data.currentAnimation);

        this.broadcast('player_state_updated', { sessionId: client.sessionId, ...data }, { except: client });
      }
    });
  }

  /**
   * Handle client joining the room
   */
  onJoin(client: Client, options: any, auth: { uid: string }): void {
    if (!auth || !auth.uid) {
      console.error(`HomeRoomDynamic.onJoin: Join rejected - Missing auth data.`);
      client.leave(1001, "Authentication data missing.");
      return;
    }

    console.log(`Client ${client.sessionId} (UID: ${auth.uid}) joined HomeRoom ${this.getDynamicRoomId()}`);

    // Create player state
    const player = new PlayerState(
      client.sessionId,
      auth.uid,
      Math.floor(Math.random() * 800), // Random spawn X
      Math.floor(Math.random() * 600)  // Random spawn Y
    );

    this.state.players.set(client.sessionId, player);
  }

  /**
   * Handle client leaving the room
   */
  onLeave(client: Client, consented: boolean): void {
    const player = this.state.players.get(client.sessionId);
    const uid = player ? player.uid : 'Unknown';

    if (player) {
      this.state.players.delete(client.sessionId);
      console.log(`Client ${client.sessionId} (UID: ${uid}) left HomeRoom ${this.getDynamicRoomId()}. Consented: ${consented}`);
    }
  }
}
