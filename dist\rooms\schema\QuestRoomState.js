"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestRoomState = void 0;
const schema_1 = require("@colyseus/schema");
const PlayerState_1 = require("../../models/PlayerState");
const QuestState_1 = require("../../models/QuestState");
/**
 * Enhanced room state with quest system integration
 */
class QuestRoomState extends schema_1.Schema {
    constructor() {
        super();
        this.players = new schema_1.MapSchema();
        this.questSystem = new QuestState_1.QuestSystemState();
        this.roomType = 'quest_room';
        this.maxPlayers = 10;
        this.createdAt = Date.now();
    }
    /**
     * Add a player to the room
     */
    addPlayer(sessionId, uid, x = 0, y = 0) {
        const player = new PlayerState_1.PlayerState(sessionId, uid, x, y);
        this.players.set(sessionId, player);
        return player;
    }
    /**
     * Remove a player from the room
     */
    removePlayer(sessionId) {
        const player = this.players.get(sessionId);
        if (player) {
            // Clean up any quest-related data for this player if needed
            this.players.delete(sessionId);
        }
    }
    /**
     * Get player by session ID
     */
    getPlayer(sessionId) {
        return this.players.get(sessionId);
    }
    /**
     * Get player by user ID
     */
    getPlayerByUid(uid) {
        for (const [sessionId, player] of this.players) {
            if (player.uid === uid) {
                return player;
            }
        }
        return undefined;
    }
    /**
     * Get all players as array
     */
    getPlayersArray() {
        return Array.from(this.players.values());
    }
    /**
     * Get current player count
     */
    getPlayerCount() {
        return this.players.size;
    }
    /**
     * Check if room is full
     */
    isFull() {
        return this.getPlayerCount() >= this.maxPlayers;
    }
    /**
     * Update player position
     */
    updatePlayerPosition(sessionId, x, y, dir) {
        const player = this.players.get(sessionId);
        if (player) {
            player.x = x;
            player.y = y;
            if (dir) {
                player.dir = dir;
            }
        }
    }
    /**
     * Update player animation
     */
    updatePlayerAnimation(sessionId, animation, isFlipped) {
        const player = this.players.get(sessionId);
        if (player) {
            player.currentAnimation = animation;
            if (typeof isFlipped === 'boolean') {
                player.isFlipped = isFlipped;
            }
        }
    }
    /**
     * Update player sitting state
     */
    updatePlayerSitting(sessionId, isSitting) {
        const player = this.players.get(sessionId);
        if (player) {
            player.isSitting = isSitting;
        }
    }
    /**
     * Update player outfit
     */
    updatePlayerOutfit(sessionId, outfit) {
        const player = this.players.get(sessionId);
        if (player) {
            player.currentOutfit = JSON.stringify(outfit);
        }
    }
    /**
     * Get room statistics
     */
    getRoomStats() {
        const players = this.getPlayersArray();
        const activeQuests = new Map();
        // Count active quests per player
        players.forEach(player => {
            const playerActiveQuests = this.questSystem.getActivePlayerQuests(player.uid);
            activeQuests.set(player.uid, playerActiveQuests.length);
        });
        return {
            playerCount: this.getPlayerCount(),
            maxPlayers: this.maxPlayers,
            roomType: this.roomType,
            createdAt: this.createdAt,
            uptime: Date.now() - this.createdAt,
            activeQuests: Object.fromEntries(activeQuests)
        };
    }
}
exports.QuestRoomState = QuestRoomState;
__decorate([
    (0, schema_1.type)({ map: PlayerState_1.PlayerState }),
    __metadata("design:type", Object)
], QuestRoomState.prototype, "players", void 0);
__decorate([
    (0, schema_1.type)(QuestState_1.QuestSystemState),
    __metadata("design:type", Object)
], QuestRoomState.prototype, "questSystem", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], QuestRoomState.prototype, "roomType", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], QuestRoomState.prototype, "maxPlayers", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], QuestRoomState.prototype, "createdAt", void 0);
