import { Schema, type, MapSchema } from '@colyseus/schema';

/**
 * Represents the progress of a single quest step
 */
export class QuestStepProgress extends Schema {
  @type('number') stepOrder: number = 0;
  @type('boolean') isCompleted: boolean = false;
  @type('string') progressData: string = '{}'; // JSON string for step-specific progress

  constructor(stepOrder: number = 0, isCompleted: boolean = false, progressData: any = {}) {
    super();
    this.stepOrder = stepOrder;
    this.isCompleted = isCompleted;
    this.progressData = JSON.stringify(progressData);
  }

  /**
   * Get progress data as parsed object
   */
  getProgressData(): any {
    try {
      return JSON.parse(this.progressData);
    } catch (error) {
      console.error('Failed to parse quest step progress data:', error);
      return {};
    }
  }

  /**
   * Set progress data from object
   */
  setProgressData(data: any): void {
    this.progressData = JSON.stringify(data);
  }
}

/**
 * Represents a player's quest state
 */
export class PlayerQuestState extends Schema {
  @type('number') questId: number = 0;
  @type('number') currentStepOrder: number = 0;
  @type('boolean') isCompleted: boolean = false;
  @type('number') startedAt: number = 0; // Unix timestamp
  @type('number') completedAt: number = 0; // Unix timestamp
  @type({ map: QuestStepProgress }) stepProgress = new MapSchema<QuestStepProgress>();

  constructor(questId: number = 0, currentStepOrder: number = 0) {
    super();
    this.questId = questId;
    this.currentStepOrder = currentStepOrder;
    this.startedAt = Date.now();
  }

  /**
   * Get progress for a specific step
   */
  getStepProgress(stepOrder: number): QuestStepProgress | undefined {
    return this.stepProgress.get(stepOrder.toString());
  }

  /**
   * Set progress for a specific step
   */
  setStepProgress(stepOrder: number, isCompleted: boolean, progressData: any = {}): void {
    const stepKey = stepOrder.toString();
    let stepProgress = this.stepProgress.get(stepKey);
    
    if (!stepProgress) {
      stepProgress = new QuestStepProgress(stepOrder, isCompleted, progressData);
      this.stepProgress.set(stepKey, stepProgress);
    } else {
      stepProgress.isCompleted = isCompleted;
      stepProgress.setProgressData(progressData);
    }
  }

  /**
   * Mark quest as completed
   */
  complete(): void {
    this.isCompleted = true;
    this.completedAt = Date.now();
  }

  /**
   * Check if a specific step is completed
   */
  isStepCompleted(stepOrder: number): boolean {
    const stepProgress = this.getStepProgress(stepOrder);
    return stepProgress ? stepProgress.isCompleted : false;
  }

  /**
   * Get all completed steps
   */
  getCompletedSteps(): number[] {
    const completedSteps: number[] = [];
    this.stepProgress.forEach((progress, key) => {
      if (progress.isCompleted) {
        completedSteps.push(progress.stepOrder);
      }
    });
    return completedSteps.sort((a, b) => a - b);
  }
}

/**
 * Represents the quest system state for a room
 */
export class QuestSystemState extends Schema {
  @type({ map: PlayerQuestState }) playerQuests = new MapSchema<PlayerQuestState>();

  /**
   * Get all quests for a specific player
   */
  getPlayerQuests(playerId: string): Map<number, PlayerQuestState> {
    const playerQuests = new Map<number, PlayerQuestState>();
    this.playerQuests.forEach((questState, key) => {
      if (key.startsWith(`${playerId}_`)) {
        playerQuests.set(questState.questId, questState);
      }
    });
    return playerQuests;
  }

  /**
   * Get a specific quest for a player
   */
  getPlayerQuest(playerId: string, questId: number): PlayerQuestState | undefined {
    return this.playerQuests.get(`${playerId}_${questId}`);
  }

  /**
   * Set a quest for a player
   */
  setPlayerQuest(playerId: string, questState: PlayerQuestState): void {
    this.playerQuests.set(`${playerId}_${questState.questId}`, questState);
  }

  /**
   * Remove a quest for a player
   */
  removePlayerQuest(playerId: string, questId: number): void {
    this.playerQuests.delete(`${playerId}_${questId}`);
  }

  /**
   * Check if player has started a quest
   */
  hasPlayerStartedQuest(playerId: string, questId: number): boolean {
    return this.playerQuests.has(`${playerId}_${questId}`);
  }

  /**
   * Get all active (not completed) quests for a player
   */
  getActivePlayerQuests(playerId: string): PlayerQuestState[] {
    const activeQuests: PlayerQuestState[] = [];
    this.playerQuests.forEach((questState, key) => {
      if (key.startsWith(`${playerId}_`) && !questState.isCompleted) {
        activeQuests.push(questState);
      }
    });
    return activeQuests;
  }

  /**
   * Get all completed quests for a player
   */
  getCompletedPlayerQuests(playerId: string): PlayerQuestState[] {
    const completedQuests: PlayerQuestState[] = [];
    this.playerQuests.forEach((questState, key) => {
      if (key.startsWith(`${playerId}_`) && questState.isCompleted) {
        completedQuests.push(questState);
      }
    });
    return completedQuests;
  }
}
