// Base error
export { BaseError } from './BaseError';

// Authentication errors
export {
  AuthenticationError,
  InvalidCredentialsError,
  TokenExpiredError,
  InvalidTokenError,
  MissingTokenError,
  TokenRevokedError
} from './AuthenticationError';

// Validation errors
export {
  ValidationError,
  MissingFieldError,
  InvalidFieldTypeError,
  InvalidFieldValueError,
  FieldLengthError,
  InvalidFormatError
} from './ValidationError';

// Business logic errors
export {
  BusinessLogicError,
  NotFoundError,
  ConflictError,
  PermissionDeniedError,
  RateLimitError,
  InsufficientResourcesError,
  OperationNotAllowedError,
  InvalidStateError
} from './BusinessLogicError';

// Database errors
export {
  DatabaseError,
  DatabaseConnectionError,
  DatabaseQueryError,
  DatabaseTransactionError,
  DatabaseConstraintError,
  DatabaseTimeoutError
} from './DatabaseError';

// Game specific errors
export {
  GameError,
  RoomError,
  RoomNotFoundError,
  RoomFullError,
  RoomAccessDeniedError,
  InvalidRoomStateError,
  PlayerError,
  PlayerNotFoundError,
  InvalidPlayerActionError,
  ItemError,
  ItemNotFoundError,
  InvalidItemPlacementError,
  CharacterError,
  CharacterNotFoundError,
  CharacterNameExistsError
} from './GameError';

// Quest specific errors
export { QuestError } from './QuestError';

// Shop specific errors
export { ShopError } from './ShopError';

// Import classes for use in ErrorFactory
import {
  AuthenticationError as AuthError,
  InvalidCredentialsError as InvalidCreds,
  TokenExpiredError as TokenExpired,
  InvalidTokenError as InvalidToken,
  MissingTokenError as MissingToken,
  TokenRevokedError as TokenRevoked
} from './AuthenticationError';

import {
  MissingFieldError as MissingField,
  InvalidFieldTypeError as InvalidFieldType
} from './ValidationError';

import {
  NotFoundError as NotFound,
  ConflictError as Conflict,
  PermissionDeniedError as PermissionDenied
} from './BusinessLogicError';

import {
  DatabaseError as DbError,
  DatabaseConnectionError as DbConnection,
  DatabaseQueryError as DbQuery,
  DatabaseTransactionError as DbTransaction,
  DatabaseConstraintError as DbConstraint,
  DatabaseTimeoutError as DbTimeout
} from './DatabaseError';

/**
 * Error factory for creating specific error types
 */
export class ErrorFactory {
  /**
   * Create authentication error based on error type
   */
  static createAuthError(type: 'invalid_credentials' | 'token_expired' | 'invalid_token' | 'missing_token' | 'token_revoked', message?: string) {
    switch (type) {
      case 'invalid_credentials':
        return new InvalidCreds(message);
      case 'token_expired':
        return new TokenExpired(message);
      case 'invalid_token':
        return new InvalidToken(message);
      case 'missing_token':
        return new MissingToken(message);
      case 'token_revoked':
        return new TokenRevoked(message);
      default:
        return new AuthError(message);
    }
  }

  /**
   * Create validation error for missing field
   */
  static createMissingFieldError(fieldName: string, message?: string) {
    return new MissingField(fieldName, message);
  }

  /**
   * Create validation error for invalid field type
   */
  static createInvalidTypeError(fieldName: string, expectedType: string, actualType: string) {
    return new InvalidFieldType(fieldName, expectedType, actualType);
  }

  /**
   * Create not found error
   */
  static createNotFoundError(resource: string, identifier?: string | number) {
    return new NotFound(resource, identifier);
  }

  /**
   * Create conflict error
   */
  static createConflictError(resource: string, field?: string, value?: any) {
    return new Conflict(resource, field, value);
  }

  /**
   * Create permission denied error
   */
  static createPermissionError(action: string, resource?: string) {
    return new PermissionDenied(action, resource);
  }

  /**
   * Create database error
   */
  static createDatabaseError(type: 'connection' | 'query' | 'transaction' | 'constraint' | 'timeout', details?: any) {
    switch (type) {
      case 'connection':
        return new DbConnection(details?.message);
      case 'query':
        return new DbQuery(details?.query, details?.originalError);
      case 'transaction':
        return new DbTransaction(details?.message, details);
      case 'constraint':
        return new DbConstraint(details?.constraint, details?.table);
      case 'timeout':
        return new DbTimeout(details?.timeout);
      default:
        return new DbError(details?.message, details);
    }
  }
}
