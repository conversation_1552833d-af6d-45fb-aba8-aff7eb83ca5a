/**
 * Outfit System Client Example
 * 
 * This example demonstrates how to use the enhanced outfit system
 * with nested Colyseus Schema types for better synchronization.
 * 
 * To run this example:
 * 1. Make sure the server is running (npm start)
 * 2. Make sure you have registered users in the database
 * 3. Run: node examples/outfit-system-client-example.js
 */

const axios = require('axios');
const { Client } = require('colyseus.js');

class OutfitSystemClientExample {
  constructor() {
    this.baseURL = 'http://localhost:2567';
    this.wsURL = 'ws://localhost:2567';
    this.client = new Client(this.wsURL);
    
    // Test user credentials
    this.testUser = {
      username: 'outfituser1',
      password: 'password123',
      token: null,
      uid: null
    };
  }

  async runExample() {
    console.log('👗 Enhanced Outfit System Example');
    console.log('=================================\n');

    try {
      // Step 1: Authenticate user
      console.log('1️⃣ Authenticating test user...');
      await this.authenticateUser(this.testUser);
      console.log(`✅ User authenticated: ${this.testUser.uid} (${this.testUser.username})`);

      // Step 2: Get current outfit
      console.log('\n2️⃣ Getting current outfit...');
      const currentOutfit = await this.getCurrentOutfit();
      console.log('Current outfit:', JSON.stringify(currentOutfit, null, 2));

      // Step 3: Connect to lobby room to see real-time outfit sync
      console.log('\n3️⃣ Connecting to lobby room...');
      const lobbyRoom = await this.connectToLobbyRoom();
      console.log('✅ Connected to lobby room');

      // Set up outfit change listeners
      this.setupOutfitListeners(lobbyRoom);

      // Step 4: Test outfit modifications
      console.log('\n4️⃣ Testing outfit modifications...');
      
      // Change hair color
      await this.updateOutfitInRoom(lobbyRoom, {
        hair: {
          color: '#FF6B6B' // Red hair
        }
      });
      
      await this.wait(1000);

      // Change top to a different item
      await this.updateOutfitInRoom(lobbyRoom, {
        top: {
          itemTemplateId: 'fancy_shirt',
          color: '#4ECDC4' // Teal color
        }
      });

      await this.wait(1000);

      // Add accessories
      await this.updateOutfitInRoom(lobbyRoom, {
        hat: {
          itemTemplateId: 'cool_hat',
          color: '#45B7D1',
          layer: 35
        },
        glasses: {
          itemTemplateId: 'sunglasses',
          color: '#2C3E50',
          layer: 40
        }
      });

      await this.wait(2000);

      // Step 5: Save current outfit as a named outfit
      console.log('\n5️⃣ Saving current outfit...');
      const savedOutfit = await this.saveOutfit('Cool Summer Look', false);
      console.log(`✅ Outfit saved with ID: ${savedOutfit.outfitId}`);

      // Step 6: Create and apply a different outfit
      console.log('\n6️⃣ Creating a formal outfit...');
      await this.updateOutfitInRoom(lobbyRoom, {
        top: {
          itemTemplateId: 'formal_suit',
          color: '#2C3E50'
        },
        bottom: {
          itemTemplateId: 'formal_pants',
          color: '#2C3E50'
        },
        shoes: {
          itemTemplateId: 'dress_shoes',
          color: '#1A1A1A'
        },
        hat: null, // Remove hat
        glasses: null // Remove glasses
      });

      await this.wait(2000);

      // Save formal outfit
      const formalOutfit = await this.saveOutfit('Formal Business', true); // Set as current
      console.log(`✅ Formal outfit saved and set as current: ${formalOutfit.outfitId}`);

      // Step 7: Get all saved outfits
      console.log('\n7️⃣ Getting all saved outfits...');
      const allOutfits = await this.getAllOutfits();
      console.log(`User has ${allOutfits.length} saved outfits:`);
      allOutfits.forEach(outfit => {
        console.log(`  - ${outfit.name} (${outfit.isCurrent ? 'CURRENT' : 'saved'})`);
      });

      // Step 8: Test outfit templates
      console.log('\n8️⃣ Testing outfit templates...');
      const templates = await this.getOutfitTemplates();
      console.log(`Found ${templates.length} outfit templates`);
      
      if (templates.length > 0) {
        const template = templates[0];
        console.log(`Applying template: ${template.name}`);
        await this.applyOutfitTemplate(template.id);
        await this.wait(2000);
      }

      // Step 9: Switch back to saved outfit
      console.log('\n9️⃣ Switching back to saved outfit...');
      if (allOutfits.length > 0) {
        const outfitToLoad = allOutfits.find(o => o.name === 'Cool Summer Look');
        if (outfitToLoad) {
          await this.setCurrentOutfit(outfitToLoad.id);
          console.log(`✅ Switched to outfit: ${outfitToLoad.name}`);
          await this.wait(2000);
        }
      }

      // Step 10: Test outfit validation
      console.log('\n🔟 Testing outfit validation...');
      
      // Try to create an invalid outfit (missing body)
      try {
        await this.saveCustomOutfit('Invalid Outfit', {
          outfitId: 'invalid_test',
          outfitName: 'Invalid Outfit',
          items: {
            // Missing body item - should fail validation
            hair: {
              itemTemplateId: 'hair_default',
              category: 'hair',
              color: '#8B4513'
            }
          }
        });
      } catch (error) {
        console.log('✅ Validation correctly rejected invalid outfit:', error.response?.data?.error?.details || error.message);
      }

      // Step 11: Test real-time outfit synchronization
      console.log('\n1️⃣1️⃣ Testing real-time synchronization...');
      console.log('Making rapid outfit changes to test sync...');
      
      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
      for (let i = 0; i < colors.length; i++) {
        await this.updateOutfitInRoom(lobbyRoom, {
          top: { color: colors[i] }
        });
        await this.wait(500);
      }

      // Step 12: Final outfit state
      console.log('\n1️⃣2️⃣ Final outfit state...');
      const finalOutfit = await this.getCurrentOutfit();
      console.log('Final outfit items:');
      if (finalOutfit && finalOutfit.items) {
        Object.entries(finalOutfit.items).forEach(([slot, item]) => {
          console.log(`  ${slot}: ${item.itemTemplateId} (${item.color})`);
        });
      }

      // Wait a bit to see final state
      await this.wait(3000);

      // Step 13: Cleanup
      console.log('\n1️⃣3️⃣ Cleaning up...');
      await lobbyRoom.leave();
      console.log('✅ Disconnected from lobby room');

      console.log('\n🎉 Outfit system demo completed successfully!');
      console.log('\nKey features demonstrated:');
      console.log('- Real-time outfit synchronization using Colyseus Schema');
      console.log('- Individual outfit item management');
      console.log('- Outfit saving and loading');
      console.log('- Outfit templates');
      console.log('- Outfit validation');
      console.log('- Layer-based rendering order');
      console.log('- Color customization');
      console.log('- Database persistence');

    } catch (error) {
      console.error('❌ Demo failed:', error.response?.data || error.message);
    }
  }

  async authenticateUser(user) {
    try {
      // Try to login first
      const response = await axios.post(`${this.baseURL}/auth/login`, {
        username: user.username,
        password: user.password
      });
      
      user.token = response.data.token;
      user.uid = response.data.userId.toString();
    } catch (error) {
      if (error.response?.status === 401) {
        // User doesn't exist, try to register
        console.log(`User ${user.username} not found, registering...`);
        const registerResponse = await axios.post(`${this.baseURL}/auth/register`, {
          username: user.username,
          password: user.password
        });
        
        user.token = registerResponse.data.token;
        user.uid = registerResponse.data.userId.toString();
      } else {
        throw error;
      }
    }
  }

  async getCurrentOutfit() {
    try {
      const response = await axios.get(`${this.baseURL}/api/outfits/current`, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`
        }
      });
      return response.data.data.outfit;
    } catch (error) {
      console.error('Failed to get current outfit:', error.response?.data || error.message);
      return null;
    }
  }

  async connectToLobbyRoom() {
    const room = await this.client.joinOrCreate('public_lobby', {
      token: this.testUser.token,
      uid: this.testUser.uid
    });

    room.onError((code, message) => {
      console.error(`Lobby room error: ${code} - ${message}`);
    });

    room.onLeave((code) => {
      console.log(`Left lobby room with code: ${code}`);
    });

    return room;
  }

  setupOutfitListeners(room) {
    // Listen for player state updates (including outfit changes)
    room.onMessage('player_state_updated', (data) => {
      if (data.currentOutfit) {
        console.log(`👗 Player ${data.sessionId} outfit updated:`, data.currentOutfit);
      }
    });

    // Listen for new players joining with their outfits
    room.onMessage('player_joined', (data) => {
      console.log(`👋 Player ${data.uid} joined with outfit:`, data.currentOutfit);
    });

    // Listen for welcome message with initial players
    room.onMessage('welcome', (data) => {
      console.log(`🏠 Joined lobby with ${data.initialPlayers.length} players`);
      data.initialPlayers.forEach(player => {
        if (player.uid !== this.testUser.uid) {
          console.log(`  Player ${player.uid} outfit:`, player.currentOutfit);
        }
      });
    });
  }

  async updateOutfitInRoom(room, outfitUpdates) {
    console.log(`Updating outfit:`, outfitUpdates);
    room.send('updatePlayerState', {
      outfitUpdates
    });
  }

  async saveOutfit(outfitName, setCurrent = false) {
    try {
      const currentOutfit = await this.getCurrentOutfit();
      const response = await axios.post(`${this.baseURL}/api/outfits`, {
        outfitName,
        outfitData: currentOutfit,
        setCurrent
      }, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`
        }
      });
      return response.data.data;
    } catch (error) {
      console.error('Failed to save outfit:', error.response?.data || error.message);
      throw error;
    }
  }

  async saveCustomOutfit(outfitName, outfitData) {
    try {
      const response = await axios.post(`${this.baseURL}/api/outfits`, {
        outfitName,
        outfitData
      }, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`
        }
      });
      return response.data.data;
    } catch (error) {
      console.error('Failed to save custom outfit:', error.response?.data || error.message);
      throw error;
    }
  }

  async getAllOutfits() {
    try {
      const response = await axios.get(`${this.baseURL}/api/outfits`, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`
        }
      });
      return response.data.data.outfits;
    } catch (error) {
      console.error('Failed to get outfits:', error.response?.data || error.message);
      return [];
    }
  }

  async setCurrentOutfit(outfitId) {
    try {
      const response = await axios.put(`${this.baseURL}/api/outfits/${outfitId}/current`, {}, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`
        }
      });
      console.log('✅', response.data.message);
    } catch (error) {
      console.error('Failed to set current outfit:', error.response?.data || error.message);
    }
  }

  async getOutfitTemplates() {
    try {
      const response = await axios.get(`${this.baseURL}/api/outfits/templates/list`);
      return response.data.data.templates;
    } catch (error) {
      console.error('Failed to get outfit templates:', error.response?.data || error.message);
      return [];
    }
  }

  async applyOutfitTemplate(templateId) {
    try {
      const response = await axios.post(`${this.baseURL}/api/outfits/templates/${templateId}/apply`, {}, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`
        }
      });
      console.log('✅', response.data.message);
    } catch (error) {
      console.error('Failed to apply outfit template:', error.response?.data || error.message);
    }
  }

  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Check if required packages are available
try {
  require('axios');
  require('colyseus.js');
} catch (error) {
  console.error('❌ This example requires axios and colyseus.js. Please install them with:');
  console.error('npm install axios colyseus.js');
  process.exit(1);
}

// Run the example
const example = new OutfitSystemClientExample();
example.runExample().catch(error => {
  console.error('❌ Example failed:', error.message);
  process.exit(1);
});
