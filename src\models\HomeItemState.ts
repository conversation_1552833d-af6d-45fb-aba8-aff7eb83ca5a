import { Schema, type } from '@colyseus/schema';

export class HomeItemState extends Schema {
  @type('string') instanceId: string = ''; // Unique instance ID of this item in the home
  @type('string') templateId: string = ''; // ID of the item template (from items table)
  @type('number') x: number = 0;
  @type('number') y: number = 0;
  @type('number') rotation: number = 0; // e.g., 0, 90, 180, 270 degrees
  @type('boolean') isFlipped: boolean = false; // Horizontal flip

  constructor(
    instanceId: string,
    templateId: string,
    x: number = 0,
    y: number = 0,
    rotation: number = 0,
    isFlipped: boolean = false
  ) {
    super();
    this.instanceId = instanceId;
    this.templateId = templateId;
    this.x = x;
    this.y = y;
    this.rotation = rotation;
    this.isFlipped = isFlipped;
  }
}
