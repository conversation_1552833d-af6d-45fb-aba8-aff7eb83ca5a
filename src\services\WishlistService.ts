import pool from '../utils/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';
import { ShopError } from '../errors';

/**
 * Wishlist item interface
 */
export interface WishlistItem extends RowDataPacket {
  id: number;
  user_id: string;
  shop_item_id: number;
  added_at: Date;
}

/**
 * Wishlist item with details interface
 */
export interface WishlistItemWithDetails extends WishlistItem {
  shop_item_name: string;
  shop_item_description?: string;
  price_gold?: number;
  price_diamonds?: number;
  is_available: boolean;
  is_limited_time: boolean;
  available_until?: Date;
  item_id: string;
  item_name: string;
  item_type: string;
  category_name: string;
  category_display_name: string;
}

/**
 * Wishlist service for managing user wishlists
 */
class WishlistService {
  private static instance: WishlistService;

  private constructor() {}

  public static getInstance(): WishlistService {
    if (!WishlistService.instance) {
      WishlistService.instance = new WishlistService();
    }
    return WishlistService.instance;
  }

  /**
   * Get user's wishlist
   */
  async getUserWishlist(userId: string): Promise<WishlistItemWithDetails[]> {
    try {
      const [rows] = await pool.query<WishlistItemWithDetails[]>(
        `SELECT
          w.id, w.user_id, w.shop_item_id, w.added_at,
          si.name as shop_item_name, si.description as shop_item_description,
          si.price_gold, si.price_diamonds, si.is_available, si.is_limited_time, si.available_until,
          si.item_id, i.item_name, i.item_type,
          sc.name as category_name, sc.display_name as category_display_name
         FROM wishlist w
         JOIN shop_items si ON w.shop_item_id = si.id
         JOIN items i ON si.item_id = i.item_id
         JOIN shop_categories sc ON si.category_id = sc.id
         WHERE w.user_id = ?
         ORDER BY w.added_at DESC`,
        [userId]
      );
      return rows;
    } catch (error) {
      console.error('Failed to get user wishlist:', error);
      throw ShopError.invalidShopData('Failed to retrieve wishlist');
    }
  }

  /**
   * Add item to wishlist
   */
  async addToWishlist(userId: string, shopItemId: number): Promise<WishlistItem> {
    try {
      // Check if item already exists in wishlist
      const [existingRows] = await pool.query<WishlistItem[]>(
        'SELECT * FROM wishlist WHERE user_id = ? AND shop_item_id = ?',
        [userId, shopItemId]
      );

      if (existingRows.length > 0) {
        throw ShopError.wishlistItemExists(shopItemId);
      }

      // Verify shop item exists
      const [shopItemRows] = await pool.query<RowDataPacket[]>(
        'SELECT id FROM shop_items WHERE id = ?',
        [shopItemId]
      );

      if (shopItemRows.length === 0) {
        throw ShopError.itemNotFound(shopItemId);
      }

      // Add to wishlist
      const [result] = await pool.query<ResultSetHeader>(
        'INSERT INTO wishlist (user_id, shop_item_id) VALUES (?, ?)',
        [userId, shopItemId]
      );

      // Return the new wishlist item
      const [newRows] = await pool.query<WishlistItem[]>(
        'SELECT * FROM wishlist WHERE id = ?',
        [result.insertId]
      );

      return newRows[0];

    } catch (error) {
      console.error('Failed to add item to wishlist:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.invalidShopData('Failed to add item to wishlist');
    }
  }

  /**
   * Remove item from wishlist
   */
  async removeFromWishlist(userId: string, shopItemId: number): Promise<void> {
    try {
      const [result] = await pool.query<ResultSetHeader>(
        'DELETE FROM wishlist WHERE user_id = ? AND shop_item_id = ?',
        [userId, shopItemId]
      );

      if (result.affectedRows === 0) {
        throw ShopError.wishlistItemNotFound(shopItemId);
      }

    } catch (error) {
      console.error('Failed to remove item from wishlist:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.invalidShopData('Failed to remove item from wishlist');
    }
  }

  /**
   * Check if item is in user's wishlist
   */
  async isInWishlist(userId: string, shopItemId: number): Promise<boolean> {
    try {
      const [rows] = await pool.query<RowDataPacket[]>(
        'SELECT COUNT(*) as count FROM wishlist WHERE user_id = ? AND shop_item_id = ?',
        [userId, shopItemId]
      );

      return rows[0].count > 0;

    } catch (error) {
      console.error('Failed to check wishlist status:', error);
      return false;
    }
  }

  /**
   * Get wishlist statistics
   */
  async getWishlistStats(userId: string): Promise<{
    totalItems: number;
    availableItems: number;
    limitedTimeItems: number;
    itemsByCategory: Record<string, number>;
  }> {
    try {
      const [totalRows] = await pool.query<RowDataPacket[]>(
        `SELECT
          COUNT(*) as total_items,
          SUM(CASE WHEN si.is_available = TRUE THEN 1 ELSE 0 END) as available_items,
          SUM(CASE WHEN si.is_limited_time = TRUE AND si.is_available = TRUE THEN 1 ELSE 0 END) as limited_time_items
         FROM wishlist w
         JOIN shop_items si ON w.shop_item_id = si.id
         WHERE w.user_id = ?`,
        [userId]
      );

      const [categoryRows] = await pool.query<RowDataPacket[]>(
        `SELECT
          sc.name as category_name,
          COUNT(*) as count
         FROM wishlist w
         JOIN shop_items si ON w.shop_item_id = si.id
         JOIN shop_categories sc ON si.category_id = sc.id
         WHERE w.user_id = ?
         GROUP BY sc.id, sc.name`,
        [userId]
      );

      const itemsByCategory: Record<string, number> = {};
      categoryRows.forEach(row => {
        itemsByCategory[row.category_name] = row.count;
      });

      return {
        totalItems: totalRows[0]?.total_items || 0,
        availableItems: totalRows[0]?.available_items || 0,
        limitedTimeItems: totalRows[0]?.limited_time_items || 0,
        itemsByCategory
      };

    } catch (error) {
      console.error('Failed to get wishlist stats:', error);
      throw ShopError.invalidShopData('Failed to retrieve wishlist statistics');
    }
  }

  /**
   * Get available items from wishlist (items that can be purchased)
   */
  async getAvailableWishlistItems(userId: string): Promise<WishlistItemWithDetails[]> {
    try {
      const [rows] = await pool.query<WishlistItemWithDetails[]>(
        `SELECT
          w.id, w.user_id, w.shop_item_id, w.added_at,
          si.name as shop_item_name, si.description as shop_item_description,
          si.price_gold, si.price_diamonds, si.is_available, si.is_limited_time, si.available_until,
          si.item_id, i.item_name, i.item_type,
          sc.name as category_name, sc.display_name as category_display_name
         FROM wishlist w
         JOIN shop_items si ON w.shop_item_id = si.id
         JOIN items i ON si.item_id = i.item_id
         JOIN shop_categories sc ON si.category_id = sc.id
         WHERE w.user_id = ?
         AND si.is_available = TRUE
         AND (si.available_from IS NULL OR si.available_from <= NOW())
         AND (si.available_until IS NULL OR si.available_until > NOW())
         ORDER BY w.added_at DESC`,
        [userId]
      );
      return rows;
    } catch (error) {
      console.error('Failed to get available wishlist items:', error);
      throw ShopError.invalidShopData('Failed to retrieve available wishlist items');
    }
  }

  /**
   * Get expiring wishlist items (limited time items that will expire soon)
   */
  async getExpiringWishlistItems(userId: string, hoursAhead: number = 24): Promise<WishlistItemWithDetails[]> {
    try {
      const [rows] = await pool.query<WishlistItemWithDetails[]>(
        `SELECT
          w.id, w.user_id, w.shop_item_id, w.added_at,
          si.name as shop_item_name, si.description as shop_item_description,
          si.price_gold, si.price_diamonds, si.is_available, si.is_limited_time, si.available_until,
          si.item_id, i.item_name, i.item_type,
          sc.name as category_name, sc.display_name as category_display_name
         FROM wishlist w
         JOIN shop_items si ON w.shop_item_id = si.id
         JOIN items i ON si.item_id = i.item_id
         JOIN shop_categories sc ON si.category_id = sc.id
         WHERE w.user_id = ?
         AND si.is_available = TRUE
         AND si.is_limited_time = TRUE
         AND si.available_until IS NOT NULL
         AND si.available_until > NOW()
         AND si.available_until <= DATE_ADD(NOW(), INTERVAL ? HOUR)
         ORDER BY si.available_until ASC`,
        [userId, hoursAhead]
      );
      return rows;
    } catch (error) {
      console.error('Failed to get expiring wishlist items:', error);
      throw ShopError.invalidShopData('Failed to retrieve expiring wishlist items');
    }
  }

  /**
   * Clear user's entire wishlist
   */
  async clearWishlist(userId: string): Promise<void> {
    try {
      await pool.query<ResultSetHeader>(
        'DELETE FROM wishlist WHERE user_id = ?',
        [userId]
      );
    } catch (error) {
      console.error('Failed to clear wishlist:', error);
      throw ShopError.invalidShopData('Failed to clear wishlist');
    }
  }

  /**
   * Batch add items to wishlist
   */
  async batchAddToWishlist(userId: string, shopItemIds: number[]): Promise<WishlistItem[]> {
    if (shopItemIds.length === 0) {
      return [];
    }

    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      const results: WishlistItem[] = [];

      for (const shopItemId of shopItemIds) {
        try {
          const result = await this.addToWishlist(userId, shopItemId);
          results.push(result);
        } catch (error) {
          // Skip items that are already in wishlist or don't exist
          if (error instanceof ShopError &&
              (error.message.includes('already in') || error.message.includes('not found'))) {
            continue;
          }
          throw error;
        }
      }

      await connection.commit();
      return results;

    } catch (error) {
      await connection.rollback();
      console.error('Failed to batch add to wishlist:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.invalidShopData('Failed to batch add items to wishlist');
    } finally {
      connection.release();
    }
  }
}

export default WishlistService.getInstance();
