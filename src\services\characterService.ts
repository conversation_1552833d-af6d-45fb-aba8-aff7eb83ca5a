import pool from '../utils/db'; // Database connection pool utility
import { ResultSetHeader, RowDataPacket } from 'mysql2';
import { v4 as uuidv4 } from 'uuid'; // For generating character IDs

/**
 * @interface Character
 * @description Represents the structure of a character record in the `characters` table.
 * @property {string} character_id - The unique UUID of the character.
 * @property {string} user_id - The ID of the user who owns this character.
 * @property {string} character_name - The name of the character.
 * @property {number} level - The current level of the character.
 * @property {number} experience - The current experience points of the character.
 * @property {number} pos_x - The character's last known X position.
 * @property {number} pos_y - The character's last known Y position.
 * @property {string} current_scene_id - The ID of the scene the character is currently in.
 * @property {string} selected_appearance_details - A JSON string detailing the character's appearance.
 * @property {Date} [created_at] - Timestamp of character creation.
 * @property {Date} [updated_at] - Timestamp of the last update to the character record.
 */
interface Character extends RowDataPacket {
  character_id: string;
  user_id: string; // Should match the type of users.id (e.g., BIGINT UNSIGNED -> string/number in TS)
  character_name: string;
  level: number;
  experience: number;
  pos_x: number;
  pos_y: number;
  current_scene_id: string;
  selected_appearance_details: string; // JSON string
  created_at?: Date;
  updated_at?: Date;
}

// --- Constants for Character Service ---
const MAX_CHARACTERS_PER_USER = 3; // Maximum number of characters a user can create.
const DEFAULT_START_POS_X = 100;   // Default starting X position for new characters.
const DEFAULT_START_POS_Y = 100;   // Default starting Y position for new characters.
const DEFAULT_START_SCENE_ID = 'default_starting_zone'; // Default scene for new characters.

// Basic appearance schema definition (example).
// In a real application, this might be more complex or configurable.
const VALID_APPEARANCE_KEYS = ['body', 'hair', 'top', 'bottom', 'shoes'];

/**
 * @class CharacterService
 * @description Provides methods for managing player characters, including creation, retrieval, and updates.
 */
class CharacterService {

  /**
   * Validates the structure and keys of provided appearance details.
   * For this simulation, it checks if all main keys defined in `VALID_APPEARANCE_KEYS` are present
   * and that their values are strings.
   * @private
   * @param {any} details - The appearance details object to validate.
   * @returns {boolean} True if the details are valid, false otherwise.
   */
  private validateAppearanceDetails(details: any): boolean {
    if (typeof details !== 'object' || details === null) {
      console.warn('CharacterService: Appearance validation failed - details is not an object or is null.');
      return false;
    }
    for (const key of VALID_APPEARANCE_KEYS) {
      if (!(key in details) || typeof details[key] !== 'string') {
        console.warn(`CharacterService: Appearance validation failed - Missing or invalid type for key '${key}'. Expected string.`);
        return false;
      }
    }
    return true;
  }

  /**
   * Creates a new character for a specified user.
   * Validates character name, appearance details, character limits per user, and name uniqueness.
   * @param {string} userId - The ID of the user creating the character.
   * @param {string} characterName - The desired name for the new character.
   * @param {any} initialAppearanceDetails - An object containing the initial appearance settings.
   * @returns {Promise<{ success: boolean; message: string; characterId?: string; character?: Partial<Character> }>}
   *          An object indicating success or failure, along with a message and potentially the new character's data.
   * @throws {Error} If a critical database error occurs or validation fails that should halt execution.
   */
  async createCharacter(
    userId: string,
    characterName: string,
    initialAppearanceDetails: any
  ): Promise<{ success: boolean; message: string; characterId?: string; character?: Partial<Character> }> {

    // Validate character name length and content.
    const trimmedName = characterName.trim();
    if (!trimmedName || trimmedName.length < 3 || trimmedName.length > 30) {
      // This kind of validation error should ideally result in a specific error type or code
      // that the API layer can translate to a 400 Bad Request.
      throw new Error('Character name must be between 3 and 30 non-whitespace characters.');
    }

    // Validate the structure and content of appearance details.
    if (!this.validateAppearanceDetails(initialAppearanceDetails)) {
      throw new Error(`Invalid appearance details. Required keys: ${VALID_APPEARANCE_KEYS.join(', ')}, all with string values.`);
    }

    try {
      // Check if the user has reached the maximum number of characters allowed.
      const [countResult] = await pool.query<RowDataPacket[]>(
        'SELECT COUNT(*) as characterCount FROM characters WHERE user_id = ?',
        [userId]
      );
      // Ensure countResult and characterCount exist before accessing.
      if (countResult[0] && countResult[0].characterCount >= MAX_CHARACTERS_PER_USER) {
        // This is a business rule violation, could be a specific error type.
        throw new Error(`User cannot have more than ${MAX_CHARACTERS_PER_USER} characters.`);
      }

      // Check for global character name uniqueness.
      const [nameCheckResult] = await pool.query<Character[]>(
        'SELECT character_id FROM characters WHERE character_name = ?',
        [trimmedName]
      );
      if (nameCheckResult.length > 0) {
        // Conflict error.
        throw new Error('Character name is already taken. Please choose a different name.');
      }

      const characterId = uuidv4(); // Generate a unique UUID for the new character.
      const appearanceJson = JSON.stringify(initialAppearanceDetails); // Store appearance as JSON string.

      // Insert the new character into the database.
      const [insertResult] = await pool.query<ResultSetHeader>(
        `INSERT INTO characters (character_id, user_id, character_name, level, experience,
          pos_x, pos_y, current_scene_id, selected_appearance_details)
         VALUES (?, ?, ?, 1, 0, ?, ?, ?, ?)`,
        [
          characterId, userId, trimmedName,
          DEFAULT_START_POS_X, DEFAULT_START_POS_Y, DEFAULT_START_SCENE_ID,
          appearanceJson,
        ]
      );

      if (insertResult.affectedRows === 1) {
        // Character creation was successful.
        return {
          success: true,
          message: 'Character created successfully.',
          characterId: characterId,
          character: { // Return a partial representation of the newly created character.
            character_id: characterId,
            character_name: trimmedName,
            level: 1,
            experience: 0,
            pos_x: DEFAULT_START_POS_X,
            pos_y: DEFAULT_START_POS_Y,
            current_scene_id: DEFAULT_START_SCENE_ID,
            selected_appearance_details: appearanceJson
          } as Partial<Character>
        };
      } else {
        // Should not happen if query is correct and DB is operational without prior error.
        console.error('CharacterService: createCharacter - Insert query affected 0 rows.');
        throw new Error('Failed to create character due to an unexpected database issue.');
      }
    } catch (error: any) {
      // Log the original error for server-side diagnostics.
      console.error(`CharacterService: Error creating character for user ${userId}: ${error.message}`);
      // Re-throw specific errors if they were thrown by validation, or a generic one for DB issues.
      if (error.message.startsWith('Character name must be') ||
          error.message.startsWith('Invalid appearance details') ||
          error.message.startsWith('User cannot have more than') ||
          error.message.startsWith('Character name is already taken')) {
        throw error; // Re-throw validation/business rule errors.
      }
      throw new Error('Server error occurred during character creation.');
    }
  }

  /**
   * Retrieves a list of characters (summary data) for a given user.
   * @param {string} userId - The ID of the user whose characters are to be fetched.
   * @returns {Promise<{ success: boolean; characters?: Partial<Character>[]; message?: string }>}
   *          An object containing the list of characters or an error message.
   * @throws {Error} If a critical database error occurs or userId is invalid.
   */
  async getUserCharacters(userId: string): Promise<{ success: true; characters: Partial<Character>[] } | { success: false; message: string }> {
    if (!userId || typeof userId !== 'string') {
        console.warn('CharacterService: getUserCharacters called with invalid userId.');
        throw new Error('User ID is required and must be a string to fetch characters.');
    }
    try {
      // Fetch a summary of characters for the user.
      const [characters] = await pool.query<Character[]>(
        'SELECT character_id, character_name, level, selected_appearance_details FROM characters WHERE user_id = ?',
        [userId]
      );

      // Map database rows to a more client-friendly format if needed.
      const characterList = characters.map(char => ({
        character_id: char.character_id,
        character_name: char.character_name,
        level: char.level,
        selected_appearance_details: char.selected_appearance_details
      }));

      return { success: true, characters: characterList as Partial<Character>[] };
    } catch (error: any) {
      console.error(`CharacterService: Error fetching characters for user ${userId}: ${error.message}`);
      throw new Error('Server error occurred while fetching user characters.');
    }
  }

  /**
   * Retrieves full data for a specific character, ensuring the requesting user owns the character.
   * @param {string} userId - The ID of the user requesting the character data (for ownership verification).
   * @param {string} characterId - The ID of the character to fetch.
   * @returns {Promise<{ success: boolean; character?: Character; message?: string }>}
   *          An object containing the full character data or an error message.
   * @throws {Error} If a critical database error occurs, or if the character is not found or not owned by the user.
   */
  async getCharacterData(userId: string, characterId: string): Promise<{ success: true; character: Character } | { success: false; message: string }> {
     if (!userId || typeof userId !== 'string' || !characterId || typeof characterId !== 'string') {
        console.warn('CharacterService: getCharacterData called with missing or invalid userId or characterId.');
        throw new Error('User ID and Character ID are required and must be strings.');
    }
    try {
      const [characters] = await pool.query<Character[]>(
        'SELECT * FROM characters WHERE character_id = ?',
        [characterId]
      );

      if (characters.length === 0) {
        // Specific error for "not found" that can be translated to a 404.
        throw new Error(`Character with ID ${characterId} not found.`);
      }

      const character = characters[0];
      // Security check: Ensure the character belongs to the requesting user.
      if (character.user_id.toString() !== userId.toString()) { // Ensure type consistency for comparison
         console.warn(`CharacterService: User ${userId} attempted to access character ${characterId} owned by ${character.user_id}.`);
         // Specific error for "access denied" that can be translated to a 403 or 404.
        throw new Error('Access denied: You do not own this character, or the character does not exist.');
      }

      return { success: true, character: character };
    } catch (error: any) {
      // Log the original error for server-side diagnostics.
      console.error(`CharacterService: Error fetching data for character ${characterId}, user ${userId}: ${error.message}`);
      // Re-throw specific known errors or a generic one for the route handler.
      if (error.message.startsWith('Character with ID') || error.message.startsWith('Access denied')) {
          throw error;
      }
      throw new Error('Server error occurred while fetching character data.');
    }
  }

  /**
   * Updates specified fields for a character.
   * Verifies ownership before applying updates.
   * @param {string} userId - The ID of the user requesting the update (for ownership verification).
   * @param {string} characterId - The ID of the character to update.
   * @param {Partial<Pick<Character, 'level' | 'experience' | 'pos_x' | 'pos_y' | 'current_scene_id' | 'selected_appearance_details'>>} updates -
   *        An object containing the fields to update and their new values.
   * @returns {Promise<{ success: boolean; message: string; updatedFields?: any }>}
   *          An object indicating success or failure, with a message and potentially the updated fields.
   * @throws {Error} If a critical database error occurs, character not found, validation fails, or no valid fields are provided.
   */
  async updateCharacterData(
    userId: string,
    characterId: string,
    updates: Partial<Pick<Character, 'level' | 'experience' | 'pos_x' | 'pos_y' | 'current_scene_id' | 'selected_appearance_details'>>
  ): Promise<{ success: true; message: string; updatedFields: any } | { success: false; message: string }> {
    if (!userId || typeof userId !== 'string' ||
        !characterId || typeof characterId !== 'string' ||
        !updates || typeof updates !== 'object' || Object.keys(updates).length === 0) {
        throw new Error('User ID, Character ID, and at least one update field are required.');
    }
    try {
      // First, verify ownership by attempting to fetch the character data.
      // getCharacterData will throw an error if not found or not owned.
      await this.getCharacterData(userId, characterId);

      const allowedFields: (keyof typeof updates)[] = [
        'level', 'experience', 'pos_x', 'pos_y', 'current_scene_id', 'selected_appearance_details'
      ];
      const fieldsToUpdate: any = {}; // Stores validated and processed updates
      const queryParams: any[] = [];
      let setClause = '';

      // Iterate over allowed fields to build the SET clause and query parameters.
      // This prevents injection of arbitrary fields into the SQL query.
      for (const field of allowedFields) {
        if (updates[field] !== undefined) { // Check if the field is actually provided in the updates object
          if (field === 'selected_appearance_details') {
            // Validate and stringify appearance details if provided as an object.
            const appearanceValue = updates[field];
            if (typeof appearanceValue === 'object' && appearanceValue !== null) {
               if (!this.validateAppearanceDetails(appearanceValue)) {
                 // More specific error for route handler.
                 throw new Error(`Invalid 'selected_appearance_details' structure. Required keys: ${VALID_APPEARANCE_KEYS.join(', ')}.`);
               }
               fieldsToUpdate[field] = JSON.stringify(appearanceValue);
            } else if (typeof appearanceValue === 'string') {
               try {
                   const parsed = JSON.parse(appearanceValue); // Validate if it's a valid JSON string
                   if (!this.validateAppearanceDetails(parsed)) {
                     throw new Error(`Invalid JSON structure for 'selected_appearance_details'. Required keys: ${VALID_APPEARANCE_KEYS.join(', ')}.`);
                   }
                   fieldsToUpdate[field] = appearanceValue; // Already a valid JSON string
               } catch (e: any) {
                   throw new Error(`'selected_appearance_details' must be a valid JSON string or object: ${e.message}`);
               }
            } else { // Not an object, not a string, or null
                throw new Error(`'selected_appearance_details' must be a valid JSON string or an object.`);
            }
          } else if (['level', 'experience', 'pos_x', 'pos_y'].includes(field)) {
            // Ensure numeric fields are indeed numbers.
            const numValue = Number(updates[field]);
            if (isNaN(numValue)) {
                throw new Error(`Field '${field}' must be a valid number.`);
            }
            fieldsToUpdate[field] = numValue;
          } else if (field === 'current_scene_id') {
            // Ensure scene ID is a string and not empty if provided
            const sceneId = updates[field] as string;
            if (typeof sceneId !== 'string' || sceneId.trim() === '') {
                throw new Error(`Field 'current_scene_id' must be a non-empty string.`);
            }
            fieldsToUpdate[field] = sceneId.trim();
          }
           else { // Should not be reached if allowedFields is correct and covers all cases
            fieldsToUpdate[field] = updates[field]; // Fallback for any other allowed field type
          }

          // Build SET clause parts
          if (setClause !== '') setClause += ', ';
          setClause += `${field} = ?`;
          queryParams.push(fieldsToUpdate[field]);
        }
      }

      if (setClause === '') {
        // No valid fields were provided, or no fields that are allowed to be updated.
        throw new Error('No valid fields provided for update or no changes detected.');
      }

      // Add characterId and userId to queryParams for the WHERE clause to ensure ownership again at DB level.
      queryParams.push(characterId);
      queryParams.push(userId);

      const [result] = await pool.query<ResultSetHeader>(
        `UPDATE characters SET ${setClause}, updated_at = NOW() WHERE character_id = ? AND user_id = ?`,
        queryParams
      );

      if (result.affectedRows > 0) {
        return { success: true, message: 'Character updated successfully.', updatedFields: fieldsToUpdate };
      } else {
        // This could happen if the character_id is valid but user_id mismatch (though checked before),
        // or if the character was deleted between the ownership check and the update.
        // Or, if the data provided for update results in no actual change to the database record.
        console.warn(`CharacterService: Update for character ${characterId} (user ${userId}) affected 0 rows. Data may have been identical or character was deleted.`);
        throw new Error('Character not found, ownership mismatch, or data provided resulted in no change.');
      }
    } catch (error: any) {
      console.error(`CharacterService: Error updating data for character ${characterId}, user ${userId}: ${error.message}`);
      // Re-throw specific errors or a generic one.
       if (error.message.startsWith('Invalid') ||
           error.message.startsWith('Field \'') ||
           error.message.startsWith('Access denied') ||
           error.message.startsWith('Character with ID') ||
           error.message.startsWith('No valid fields')) {
          throw error; // Re-throw specific validation or access errors.
      }
      throw new Error('Server error occurred during character update.');
    }
  }
}

// Export a singleton instance of the CharacterService.
export default new CharacterService();
