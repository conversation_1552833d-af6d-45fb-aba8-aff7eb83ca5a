const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkTables() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🔍 Checking database tables...');
    
    const [tables] = await connection.query('SHOW TABLES');
    console.log('📊 Tables in database:');
    tables.forEach(table => console.log('  -', Object.values(table)[0]));
    
    // Check if user_inventory_config exists
    const hasInventoryConfig = tables.some(table => Object.values(table)[0] === 'user_inventory_config');
    console.log('\n🔍 user_inventory_config table exists:', hasInventoryConfig);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await connection.end();
  }
}

checkTables();
