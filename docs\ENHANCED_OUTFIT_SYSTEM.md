# 增强服装系统文档

## 概述

增强服装系统将原有的 JSON 字符串服装数据升级为嵌套的 Colyseus Schema 类型，提供了更好的实时同步、类型安全和客户端管理体验。系统支持分层渲染、颜色自定义、服装保存和模板应用等高级功能。

## 核心特性

### 🎯 主要改进
- **嵌套 Schema 结构**: 使用 Colyseus Schema 替代 JSON 字符串
- **实时同步**: 服装变化实时同步到所有客户端
- **分层渲染**: 支持渲染层级管理
- **颜色自定义**: 每个服装部件支持颜色自定义
- **服装管理**: 保存、加载、删除服装配置
- **模板系统**: 预定义服装模板
- **数据持久化**: 服装数据自动保存到数据库

### 👗 支持的服装部位
- **body**: 身体基础部分
- **hair**: 发型
- **top**: 上装（T恤、衬衫、外套等）
- **bottom**: 下装（裤子、裙子等）
- **shoes**: 鞋履
- **hat**: 帽子
- **glasses**: 眼镜
- **necklace**: 项链
- **gloves**: 手套
- **wings**: 翅膀
- **tail**: 尾巴
- **mask**: 面具

## 系统架构

### 核心组件

#### 1. OutfitState Schema
嵌套的 Colyseus Schema，包含完整的服装状态
```typescript
class OutfitState extends Schema {
  @type({ map: OutfitItemState }) items = new MapSchema<OutfitItemState>();
  @type('string') outfitName: string;
  @type('string') outfitId: string;
  @type('boolean') isCustom: boolean;
  @type('number') lastUpdated: number;
}
```

#### 2. OutfitItemState Schema
单个服装部件的状态
```typescript
class OutfitItemState extends Schema {
  @type('string') itemId: string;
  @type('string') itemTemplateId: string;
  @type('string') category: string;
  @type('string') subCategory: string;
  @type('string') color: string;
  @type('number') layer: number;
  @type('boolean') isVisible: boolean;
  @type('string') customData: string;
}
```

#### 3. OutfitService
服装管理服务，处理数据库操作和业务逻辑
- 服装保存和加载
- 模板管理
- 数据验证
- 默认服装生成

#### 4. 增强的 PlayerState
集成了新的 OutfitState，提供便利方法
```typescript
class PlayerState extends Schema {
  @type(OutfitState) currentOutfit: OutfitState = new OutfitState();
  
  // 便利方法
  equipOutfitItem(slot: string, itemTemplateId: string, options: any): void;
  unequipOutfitItem(slot: string): boolean;
  updateOutfitItem(slot: string, updates: any): boolean;
}
```

## 数据库结构

### outfit_templates 表
```sql
CREATE TABLE `outfit_templates` (
  `id` VARCHAR(255) NOT NULL PRIMARY KEY,
  `name` VARCHAR(255) NOT NULL,
  `description` TEXT NULL,
  `category` VARCHAR(100) NOT NULL,
  `rarity` ENUM('common', 'uncommon', 'rare', 'epic', 'legendary') NOT NULL DEFAULT 'common',
  `is_default` BOOLEAN NOT NULL DEFAULT FALSE,
  `is_purchasable` BOOLEAN NOT NULL DEFAULT TRUE,
  `price_gold` INT NOT NULL DEFAULT 0,
  `price_diamonds` INT NOT NULL DEFAULT 0,
  `unlock_level` INT NOT NULL DEFAULT 1,
  `outfit_data` JSON NOT NULL,
  `preview_image` VARCHAR(500) NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### user_outfits 表
```sql
CREATE TABLE `user_outfits` (
  `id` VARCHAR(255) NOT NULL PRIMARY KEY,
  `user_id` BIGINT NOT NULL,
  `outfit_name` VARCHAR(255) NOT NULL,
  `outfit_data` JSON NOT NULL,
  `is_current` BOOLEAN NOT NULL DEFAULT FALSE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `unique_user_outfit_name` (`user_id`, `outfit_name`)
);
```

## API 接口

### 服装管理

#### 获取当前服装
```http
GET /api/outfits/current
Authorization: Bearer TOKEN
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "outfit": {
      "outfitId": "outfit_123_1234567890",
      "outfitName": "My Cool Outfit",
      "isCustom": true,
      "lastUpdated": 1234567890000,
      "items": {
        "body": {
          "itemId": "body_default",
          "itemTemplateId": "body_default",
          "category": "body",
          "subCategory": "base",
          "color": "#FFDBAC",
          "layer": 0,
          "isVisible": true,
          "customData": {}
        },
        "hair": {
          "itemId": "hair_cool",
          "itemTemplateId": "hair_cool",
          "category": "hair",
          "subCategory": "base",
          "color": "#FF6B6B",
          "layer": 10,
          "isVisible": true,
          "customData": {}
        }
      }
    }
  }
}
```

#### 保存当前服装
```http
PUT /api/outfits/current
Authorization: Bearer TOKEN
Content-Type: application/json

{
  "outfitData": { /* outfit data */ },
  "outfitName": "Updated Outfit"
}
```

#### 保存新服装
```http
POST /api/outfits
Authorization: Bearer TOKEN
Content-Type: application/json

{
  "outfitName": "Summer Look",
  "outfitData": { /* outfit data */ },
  "setCurrent": false
}
```

#### 获取所有保存的服装
```http
GET /api/outfits
Authorization: Bearer TOKEN
```

#### 设置当前服装
```http
PUT /api/outfits/:outfitId/current
Authorization: Bearer TOKEN
```

#### 删除服装
```http
DELETE /api/outfits/:outfitId
Authorization: Bearer TOKEN
```

### 服装模板

#### 获取服装模板
```http
GET /api/outfits/templates/list?category=casual&rarity=common
```

#### 应用服装模板
```http
POST /api/outfits/templates/:templateId/apply
Authorization: Bearer TOKEN
```

## 客户端使用

### JavaScript 客户端示例

#### 1. 连接到房间并监听服装变化
```javascript
import { Client } from 'colyseus.js';

const client = new Client('ws://localhost:2567');

// 连接到大厅房间
const room = await client.joinOrCreate('public_lobby', {
  token: userToken,
  uid: userId
});

// 监听服装变化
room.onMessage('player_state_updated', (data) => {
  if (data.currentOutfit) {
    console.log('Player outfit updated:', data.currentOutfit);
    updatePlayerVisuals(data.sessionId, data.currentOutfit);
  }
});
```

#### 2. 更新服装
```javascript
// 更新单个服装部件
room.send('updatePlayerState', {
  outfitUpdates: {
    hair: {
      color: '#FF6B6B' // 改变发色为红色
    },
    top: {
      itemTemplateId: 'fancy_shirt',
      color: '#4ECDC4'
    },
    hat: {
      itemTemplateId: 'cool_hat',
      color: '#45B7D1',
      layer: 35
    }
  }
});

// 移除服装部件
room.send('updatePlayerState', {
  outfitUpdates: {
    hat: null, // 移除帽子
    glasses: null // 移除眼镜
  }
});
```

#### 3. 服装管理 API 调用
```javascript
// 获取当前服装
const currentOutfit = await fetch('/api/outfits/current', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
}).then(r => r.json());

// 保存服装
const saveResult = await fetch('/api/outfits', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    outfitName: 'My Cool Look',
    outfitData: currentOutfit.data.outfit,
    setCurrent: false
  })
});

// 获取所有保存的服装
const allOutfits = await fetch('/api/outfits', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
}).then(r => r.json());

// 应用服装模板
const applyResult = await fetch(`/api/outfits/templates/${templateId}/apply`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

#### 4. 客户端渲染
```javascript
// 根据层级渲染服装部件
function renderPlayerOutfit(player, outfitItems) {
  // outfitItems 已经按 layer 排序
  outfitItems.forEach(item => {
    if (item.isVisible) {
      const sprite = createOutfitSprite(item.itemTemplateId, item.color);
      sprite.zIndex = item.layer;
      player.addChild(sprite);
    }
  });
}

// 创建服装精灵
function createOutfitSprite(itemTemplateId, color) {
  const sprite = new PIXI.Sprite(getTexture(itemTemplateId));
  
  // 应用颜色
  if (color && color !== '#FFFFFF') {
    sprite.tint = parseInt(color.replace('#', '0x'));
  }
  
  return sprite;
}
```

## 服装数据结构

### 完整服装数据示例
```json
{
  "outfitId": "outfit_123_1234567890",
  "outfitName": "Summer Casual",
  "isCustom": true,
  "lastUpdated": 1234567890000,
  "items": {
    "body": {
      "itemId": "body_default",
      "itemTemplateId": "body_default",
      "category": "body",
      "subCategory": "base",
      "color": "#FFDBAC",
      "layer": 0,
      "isVisible": true,
      "customData": {}
    },
    "hair": {
      "itemId": "hair_long",
      "itemTemplateId": "hair_long_wavy",
      "category": "hair",
      "subCategory": "long",
      "color": "#8B4513",
      "layer": 10,
      "isVisible": true,
      "customData": {
        "style": "wavy",
        "length": "long"
      }
    },
    "top": {
      "itemId": "top_summer",
      "itemTemplateId": "summer_tshirt",
      "category": "clothing",
      "subCategory": "casual",
      "color": "#4ECDC4",
      "layer": 20,
      "isVisible": true,
      "customData": {
        "pattern": "solid",
        "fit": "regular"
      }
    },
    "bottom": {
      "itemId": "bottom_shorts",
      "itemTemplateId": "denim_shorts",
      "category": "clothing",
      "subCategory": "casual",
      "color": "#4169E1",
      "layer": 15,
      "isVisible": true,
      "customData": {}
    },
    "shoes": {
      "itemId": "shoes_sneakers",
      "itemTemplateId": "canvas_sneakers",
      "category": "clothing",
      "subCategory": "casual",
      "color": "#FFFFFF",
      "layer": 5,
      "isVisible": true,
      "customData": {}
    },
    "hat": {
      "itemId": "hat_cap",
      "itemTemplateId": "baseball_cap",
      "category": "accessory",
      "subCategory": "hat",
      "color": "#FF6B6B",
      "layer": 35,
      "isVisible": true,
      "customData": {
        "brand": "cool_brand"
      }
    }
  }
}
```

### 层级系统
```javascript
const DEFAULT_LAYERS = {
  'body': 0,        // 身体基础
  'underwear': 1,   // 内衣
  'socks': 2,       // 袜子
  'tail': 3,        // 尾巴
  'shoes': 5,       // 鞋子
  'hair': 10,       // 头发
  'bottom': 15,     // 下装
  'top': 20,        // 上装
  'gloves': 22,     // 手套
  'jacket': 25,     // 外套
  'necklace': 30,   // 项链
  'hat': 35,        // 帽子
  'glasses': 40,    // 眼镜
  'mask': 45,       // 面具
  'wings': 50       // 翅膀
};
```

## 性能优化

### Schema 同步优化
- 只同步变化的服装部件
- 使用 MapSchema 进行增量更新
- 客户端缓存服装数据

### 数据库优化
- JSON 字段索引优化
- 服装模板缓存
- 批量更新操作

### 渲染优化
- 纹理图集优化
- 层级排序缓存
- 颜色着色器优化

## 扩展功能

### 未来增强
- **服装染色系统**: 高级颜色自定义
- **服装合成**: 组合服装创建新样式
- **服装交易**: 玩家间服装交换
- **服装展示**: 服装秀和评分系统
- **季节服装**: 限时特殊服装
- **服装成就**: 收集成就系统

### 集成扩展
- **商店系统**: 服装购买集成
- **任务系统**: 服装奖励
- **社交系统**: 服装分享
- **活动系统**: 特殊服装活动

## 测试

### 运行测试
```bash
npm run test-outfits
```

### 客户端示例
```bash
npm install axios colyseus.js  # 安装依赖
node examples/outfit-system-client-example.js
```

增强服装系统现在已经完全集成到你的游戏服务端中，提供了类型安全的实时服装同步、完整的服装管理功能和良好的扩展性，为玩家提供了丰富的个性化体验。
