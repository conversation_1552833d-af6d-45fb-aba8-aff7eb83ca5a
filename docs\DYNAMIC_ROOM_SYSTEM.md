# 动态房间系统文档

## 概述

动态房间系统为 2D 虚拟世界游戏提供了灵活的私人空间访问机制。系统支持动态房间ID生成、高级匹配算法和访问权限控制，让玩家能够轻松访问自己或其他玩家的私人空间。

## 核心特性

### 🎯 主要功能
- **动态房间ID**: 自动生成格式化的房间ID（如 `home_123`, `garden_456`）
- **访问权限控制**: 支持私有、好友可见、公开三种访问级别
- **房间发现**: 智能房间匹配和发现系统
- **实时注册**: 房间状态实时跟踪和管理
- **向后兼容**: 保持与现有静态房间的兼容性

### 🏠 支持的房间类型
- **Home (家园)**: 玩家的私人家园空间
- **Garden (花园)**: 玩家的私人花园空间  
- **Quest (任务)**: 任务相关的动态房间

## 系统架构

### 核心组件

#### 1. RoomMatchingService
房间匹配和管理的核心服务
- 动态房间ID生成和解析
- 访问权限检查
- 房间注册表管理
- 房间发现和统计

#### 2. BasePrivateRoom
私人房间的基类
- 统一的房间初始化流程
- 增强的身份验证
- 访问权限控制
- 房间生命周期管理

#### 3. 动态房间实现
- `HomeRoomDynamic`: 增强的家园房间
- `GardenRoomDynamic`: 增强的花园房间
- 支持动态ID和访问控制

## 数据库结构

### room_registry 表
```sql
CREATE TABLE `room_registry` (
  `room_id` VARCHAR(255) NOT NULL PRIMARY KEY,
  `room_type` ENUM('home', 'garden', 'quest') NOT NULL,
  `owner_uid` BIGINT NOT NULL,
  `access_level` ENUM('private', 'friends_only', 'public') NOT NULL DEFAULT 'private',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `last_accessed` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## API 接口

### 房间发现

#### 获取可用房间
```http
GET /api/rooms/:roomType/available?token=TOKEN&uid=UID
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "ownRooms": [...],
    "friendRooms": [...],
    "publicRooms": [...]
  }
}
```

#### 生成房间ID
```http
GET /api/rooms/:roomType/generate-id/:ownerUid?token=TOKEN&uid=UID
```

#### 解析房间ID
```http
GET /api/rooms/parse/:roomId?token=TOKEN&uid=UID
```

### 房间管理

#### 获取房间信息
```http
GET /api/rooms/info/:roomId?token=TOKEN&uid=UID
```

#### 获取公开房间
```http
GET /api/rooms/:roomType/public?limit=20
```

#### 获取房间统计（管理员）
```http
GET /api/rooms/admin/statistics?token=TOKEN&uid=UID
```

## 客户端使用

### JavaScript 客户端示例

#### 1. 生成动态房间ID
```javascript
// 生成自己的家园房间ID
const myHomeRoomId = await fetch(`/api/rooms/home/<USER>/${myUserId}?token=${token}&uid=${myUserId}`)
  .then(r => r.json())
  .then(data => data.data.roomId);

console.log(myHomeRoomId); // "home_123"
```

#### 2. 检查房间访问权限
```javascript
// 检查是否可以访问朋友的花园
const canAccess = await fetch(`/api/rooms/parse/garden_456?token=${token}&uid=${myUserId}`)
  .then(r => r.json())
  .then(data => data.data.canAccess);

if (canAccess) {
  console.log('可以访问朋友的花园');
}
```

#### 3. 连接到动态房间
```javascript
import { Client } from 'colyseus.js';

const client = new Client('ws://localhost:2567');

// 连接到动态家园房间
const homeRoom = await client.joinOrCreate('home_dynamic', {
  roomId: 'home_123',
  token: userToken,
  uid: userId
});

// 连接到动态花园房间
const gardenRoom = await client.joinOrCreate('garden_dynamic', {
  roomId: 'garden_456', 
  token: userToken,
  uid: userId
});
```

#### 4. 发现可用房间
```javascript
// 获取所有可访问的家园房间
const availableRooms = await fetch(`/api/rooms/home/<USER>
  .then(r => r.json());

console.log('我的房间:', availableRooms.data.ownRooms);
console.log('朋友的房间:', availableRooms.data.friendRooms);
console.log('公开房间:', availableRooms.data.publicRooms);
```

## 房间ID 格式

### 标准格式
```
{roomType}_{ownerUid}
```

### 示例
- `home_123` - 用户123的家园
- `garden_456` - 用户456的花园
- `quest_789` - 用户789的任务房间

### 解析规则
```javascript
function parseRoomId(roomId) {
  const parts = roomId.split('_');
  if (parts.length < 2) return null;
  
  return {
    roomType: parts[0],
    ownerUid: parts.slice(1).join('_') // 支持包含下划线的UID
  };
}
```

## 访问权限系统

### 权限级别

#### 1. Private (私有)
- 只有房间所有者可以访问
- 默认权限级别

#### 2. Friends Only (好友可见)
- 房间所有者和其好友可以访问
- 需要已接受的好友关系

#### 3. Public (公开)
- 所有认证用户都可以访问
- 会出现在公开房间列表中

### 权限检查流程
```javascript
async function checkRoomAccess(ownerUid, requestingUid, roomType) {
  // 1. 如果是房间所有者，直接允许
  if (ownerUid === requestingUid) return true;
  
  // 2. 检查房间访问级别设置
  const spaceSettings = await getSpaceSettings(ownerUid);
  const accessLevel = spaceSettings[`${roomType}_access_level`];
  
  // 3. 根据访问级别检查权限
  switch (accessLevel) {
    case 'private':
      return false;
    case 'friends_only':
      return await areFriends(ownerUid, requestingUid);
    case 'public':
      return true;
    default:
      return false;
  }
}
```

## 房间生命周期

### 1. 房间创建
```javascript
// 客户端请求加入房间
const room = await client.joinOrCreate('home_dynamic', {
  roomId: 'home_123',
  token: userToken,
  uid: userId
});

// 服务端处理
class HomeRoomDynamic extends BasePrivateRoom {
  async onCreate(options) {
    // 初始化动态房间
    await this.initializeRoom(options, 'home');
    
    // 注册到房间注册表
    await RoomMatchingService.registerRoom(
      this.dynamicRoomId,
      'home',
      this.ownerUid
    );
  }
}
```

### 2. 房间活跃状态
- 房间在有玩家连接时保持活跃
- 定期更新 `last_accessed` 时间戳
- 支持自动清理不活跃房间

### 3. 房间销毁
```javascript
onDispose() {
  // 从注册表中移除
  RoomMatchingService.unregisterRoom(this.dynamicRoomId);
}
```

## 性能优化

### 缓存策略
- 房间访问权限缓存
- 好友关系缓存
- 房间注册表查询优化

### 数据库优化
- 复合索引优化查询性能
- 定期清理不活跃房间记录
- 分页查询减少数据传输

### 内存管理
- 房间自动销毁机制
- 连接池管理
- 状态同步优化

## 监控和统计

### 房间统计信息
```javascript
const stats = await RoomMatchingService.getRoomStatistics();
console.log({
  totalRooms: stats.totalRooms,
  activeRooms: stats.activeRooms,
  roomsByType: stats.roomsByType,
  roomsByAccess: stats.roomsByAccess
});
```

### 清理维护
```javascript
// 清理24小时未访问的房间
const cleanedCount = await RoomMatchingService.cleanupInactiveRooms(24);
console.log(`清理了 ${cleanedCount} 个不活跃房间`);
```

## 错误处理

### 常见错误类型
- **InvalidRoomId**: 房间ID格式错误
- **AccessDenied**: 访问权限不足
- **RoomNotFound**: 房间不存在
- **AuthenticationFailed**: 身份验证失败

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "name": "AccessDeniedError",
    "message": "Access denied to home of user 123."
  }
}
```

## 迁移指南

### 从静态房间迁移

#### 1. 保持向后兼容
```javascript
// 旧方式仍然支持
const legacyRoom = await client.joinOrCreate('home', {
  ownerUid: '123',
  token: userToken,
  uid: userId
});

// 新方式
const dynamicRoom = await client.joinOrCreate('home_dynamic', {
  roomId: 'home_123',
  token: userToken,
  uid: userId
});
```

#### 2. 逐步迁移
- 新功能使用动态房间
- 现有功能保持静态房间
- 逐步替换客户端代码

## 测试

### 运行测试
```bash
npm run test-rooms
```

### 客户端示例
```bash
npm install axios colyseus.js  # 安装依赖
node examples/dynamic-room-client-example.js
```

## 扩展功能

### 未来增强
- **房间模板**: 预定义房间配置
- **房间事件**: 房间状态变化通知
- **房间邀请**: 直接邀请系统
- **房间收藏**: 收藏常访问的房间
- **房间搜索**: 高级房间搜索功能
- **房间评级**: 房间质量评分系统

动态房间系统现在已经完全集成到你的游戏服务端中，提供了灵活的私人空间访问机制，支持你需求中的所有核心特性，并为未来的功能扩展做好了准备。
