import { BaseError } from './BaseError';

/**
 * Database related errors
 */
export class DatabaseError extends BaseError {
  constructor(message: string = 'Database operation failed', details?: any) {
    super('DatabaseError', 500, message, true, details);
  }
}

/**
 * Database connection error
 */
export class DatabaseConnectionError extends DatabaseError {
  constructor(message: string = 'Failed to connect to database') {
    super(message);
    this.name = 'DatabaseConnectionError';
  }
}

/**
 * Database query error
 */
export class DatabaseQueryError extends DatabaseError {
  constructor(query: string, originalError?: Error) {
    const message = `Database query failed: ${originalError?.message || 'Unknown error'}`;
    super(message, { query, originalError: originalError?.message });
    this.name = 'DatabaseQueryError';
  }
}

/**
 * Database transaction error
 */
export class DatabaseTransactionError extends DatabaseError {
  constructor(message: string = 'Database transaction failed', details?: any) {
    super(message, details);
    this.name = 'DatabaseTransactionError';
  }
}

/**
 * Database constraint violation error
 */
export class DatabaseConstraintError extends DatabaseError {
  constructor(constraint: string, table?: string) {
    let message = `Database constraint violation: ${constraint}`;
    if (table) {
      message += ` in table '${table}'`;
    }
    super(message, { constraint, table });
    this.name = 'DatabaseConstraintError';
  }
}

/**
 * Database timeout error
 */
export class DatabaseTimeoutError extends DatabaseError {
  constructor(timeout: number) {
    const message = `Database operation timed out after ${timeout}ms`;
    super(message, { timeout });
    this.name = 'DatabaseTimeoutError';
  }
}
