"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayerState = void 0;
const schema_1 = require("@colyseus/schema");
class PlayerState extends schema_1.Schema {
    // Consider a nested Schema for `currentOutfit` if individual parts need to be synced
    // or if the structure becomes very complex.
    constructor(sessionId, uid, x = 0, y = 0) {
        super();
        this.sessionId = ''; // The client's sessionId
        this.uid = ''; // User ID from your authentication system
        this.x = 0;
        this.y = 0;
        this.dir = 'down'; // e.g., 'up', 'down', 'left', 'right'
        this.isFlipped = false; // For sprite flipping
        this.isSitting = false;
        this.currentAnimation = 'idle'; // e.g., 'idle', 'walk', 'sit'
        // Example: Outfit structure (can be more complex)
        this.currentOutfit = JSON.stringify({
            body: 'default_body',
            hair: 'default_hair',
            top: 'default_top',
            bottom: 'default_bottom',
            shoes: 'default_shoes'
        }); // JSON string for outfit details. Parsed by client/server when accessed.
        this.sessionId = sessionId;
        this.uid = uid;
        this.x = x;
        this.y = y;
    }
}
exports.PlayerState = PlayerState;
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], PlayerState.prototype, "sessionId", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], PlayerState.prototype, "uid", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], PlayerState.prototype, "x", void 0);
__decorate([
    (0, schema_1.type)('number'),
    __metadata("design:type", Number)
], PlayerState.prototype, "y", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], PlayerState.prototype, "dir", void 0);
__decorate([
    (0, schema_1.type)('boolean'),
    __metadata("design:type", Boolean)
], PlayerState.prototype, "isFlipped", void 0);
__decorate([
    (0, schema_1.type)('boolean'),
    __metadata("design:type", Boolean)
], PlayerState.prototype, "isSitting", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], PlayerState.prototype, "currentAnimation", void 0);
__decorate([
    (0, schema_1.type)('string'),
    __metadata("design:type", String)
], PlayerState.prototype, "currentOutfit", void 0);
