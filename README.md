# 🎮 2D Virtual World Game Server

一个基于 Colyseus、TypeScript 和 MySQL 构建的多人 2D 虚拟世界游戏服务器。

## ✨ 主要功能

- 🏠 实时多人房间系统 (家园、花园、任务)
- 🎯 任务系统，支持 NPC 交互和对话
- 🛍️ 商店系统，支持多种货币 (金币、钻石)
- 🎒 库存管理，支持物品堆叠和分类
- 👥 好友系统，支持实时通知
- 👗 角色定制和服装系统
- 🏗️ 基于房间的架构，支持动态房间创建

## 🛠️ 技术栈

- **服务器**: Node.js, TypeScript, Colyseus
- **数据库**: MySQL 连接池
- **认证**: JWT 令牌 + 数据库验证
- **实时通信**: WebSocket (Colyseus)
- **测试**: Mocha, Chai, Colyseus Testing

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 环境配置
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接信息
```

### 3. 数据库设置
```bash
# 创建数据库并运行架构
mysql -u root -p < sql/complete_database_schema.sql

# 设置测试数据
npm run setup-test-data
```

### 4. 启动开发服务器
```bash
npm start
```

### 5. 运行测试
```bash
# 检查系统状态
npm run system-status

# 运行所有测试
npm test

# 或者设置测试数据后运行测试
npm run test-with-setup
```

## 📊 系统状态

当前系统状态：**✅ 稳定运行**

- ✅ **11+ 个测试通过**
- ✅ 核心功能正常运行
- ✅ 数据库架构完整
- ✅ API 路由完善

## 🔗 API 端点

### 认证系统
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出

### 好友系统
- `GET /api/friends` - 获取好友列表
- `POST /api/friends/request` - 发送好友请求
- `POST /api/friends/accept` - 接受好友请求

### 商店系统
- `GET /api/shop/categories` - 获取商店分类
- `GET /api/shop/items` - 获取商店物品
- `POST /api/shop/purchase` - 购买物品

### 库存系统 ✨ (已完善)
- `GET /api/inventory` - 获取用户库存
- `GET /api/inventory/search` - 搜索库存物品
- `GET /api/inventory/recent` - 获取最近物品
- `POST /api/inventory/use` - 使用物品
- `POST /api/inventory/mark-seen` - 标记物品为已读
- `POST /api/inventory/toggle-lock` - 锁定/解锁物品

### 任务系统
- `GET /api/quests/available` - 获取可用任务
- `POST /api/quests/start` - 开始任务
- `POST /api/quests/complete` - 完成任务

## 🏠 房间类型

### 动态房间 (推荐)
- `home_dynamic` - 动态家园房间 (home_123)
- `garden_dynamic` - 动态花园房间 (garden_456)
- `quest_dynamic` - 动态任务房间 (quest_789)

### 静态房间 (向后兼容)
- `home` - 用户家园房间
- `garden` - 花园房间
- `quest` - 任务房间

## 💻 开发指南

### 项目结构
```
src/
├── rooms/          # Colyseus 房间定义
├── routes/         # Express API 路由
├── services/       # 业务逻辑服务
├── schemas/        # Colyseus 状态模式
├── utils/          # 工具函数
└── index.ts        # 主服务器入口
```

### 数据库架构
完整的数据库结构请参考 `sql/complete_database_schema.sql`。

### 测试命令
```bash
# 运行所有测试
npm test

# 运行特定测试套件
npm run test-quest      # 任务系统测试
npm run test-shop       # 商店系统测试
npm run test-inventory  # 库存系统测试

# 设置测试数据
npm run setup-test-data

# 检查系统状态
npm run system-status

# 修复库存表结构
npm run fix-inventory
```

### 开发工具
```bash
# 启动开发服务器 (热重载)
npm run dev

# 构建项目
npm run build

# 代码检查
npm run lint
```

## 🚀 部署

### 生产环境部署
```bash
# 1. 构建项目
npm run build

# 2. 设置生产环境变量
export NODE_ENV=production
export DB_HOST=your-db-host
export DB_USER=your-db-user
export DB_PASSWORD=your-db-password

# 3. 启动生产服务器
npm run start:prod
```

## 📚 文档

- [系统完善报告](docs/FINAL_SYSTEM_REPORT.md) - 详细的系统改进报告
- [系统改进记录](docs/SYSTEM_IMPROVEMENTS.md) - 改进过程记录

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 开发规范
- 遵循 TypeScript 编码规范
- 为新功能编写测试用例
- 更新相关文档
- 确保所有测试通过

## 📄 许可证

本项目采用 UNLICENSED 许可证。

## 🆘 支持

如果您遇到问题或有疑问，请：

1. 查看 [文档](docs/)
2. 运行 `npm run system-status` 检查系统状态
3. 查看 Issues 页面
4. 创建新的 Issue

---

**最后更新**: 2024年12月26日
**版本**: v2.0.0
**状态**: ✅ 稳定运行
