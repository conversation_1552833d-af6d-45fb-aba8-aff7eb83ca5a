import { BaseError } from './BaseError';

/**
 * Game specific errors
 */
export class GameError extends BaseError {
  constructor(message: string, httpCode: number = 400, details?: any) {
    super('GameError', httpCode, message, true, details);
  }
}

/**
 * Room related errors
 */
export class RoomError extends GameError {
  constructor(message: string, roomId?: string) {
    super(message, 400, { roomId });
    this.name = 'RoomError';
  }
}

/**
 * Room not found error
 */
export class RoomNotFoundError extends RoomError {
  constructor(roomId: string) {
    super(`Room '${roomId}' not found`, roomId);
    this.name = 'RoomNotFoundError';
    this.httpCode = 404;
  }
}

/**
 * Room full error
 */
export class RoomFullError extends RoomError {
  constructor(roomId: string, maxClients: number) {
    super(`Room '${roomId}' is full (max ${maxClients} clients)`, roomId);
    this.name = 'RoomFullError';
    this.details = { ...this.details, maxClients };
  }
}

/**
 * Room access denied error
 */
export class RoomAccessDeniedError extends RoomError {
  constructor(roomId: string, reason?: string) {
    let message = `Access denied to room '${roomId}'`;
    if (reason) {
      message += `: ${reason}`;
    }
    super(message, roomId);
    this.name = 'RoomAccessDeniedError';
    this.httpCode = 403;
    if (reason) {
      this.details = { ...this.details, reason };
    }
  }
}

/**
 * Invalid room state error
 */
export class InvalidRoomStateError extends RoomError {
  constructor(roomId: string, currentState: string, expectedState: string) {
    const message = `Invalid room state: current '${currentState}', expected '${expectedState}'`;
    super(message, roomId);
    this.name = 'InvalidRoomStateError';
    this.details = { ...this.details, currentState, expectedState };
  }
}

/**
 * Player related errors
 */
export class PlayerError extends GameError {
  constructor(message: string, playerId?: string) {
    super(message, 400, { playerId });
    this.name = 'PlayerError';
  }
}

/**
 * Player not found error
 */
export class PlayerNotFoundError extends PlayerError {
  constructor(playerId: string) {
    super(`Player '${playerId}' not found`, playerId);
    this.name = 'PlayerNotFoundError';
    this.httpCode = 404;
  }
}

/**
 * Invalid player action error
 */
export class InvalidPlayerActionError extends PlayerError {
  constructor(action: string, reason: string, playerId?: string) {
    const message = `Invalid player action '${action}': ${reason}`;
    super(message, playerId);
    this.name = 'InvalidPlayerActionError';
    this.details = { ...this.details, action, reason };
  }
}

/**
 * Item related errors
 */
export class ItemError extends GameError {
  constructor(message: string, itemId?: string) {
    super(message, 400, { itemId });
    this.name = 'ItemError';
  }
}

/**
 * Item not found error
 */
export class ItemNotFoundError extends ItemError {
  constructor(itemId: string) {
    super(`Item '${itemId}' not found`, itemId);
    this.name = 'ItemNotFoundError';
    this.httpCode = 404;
  }
}

/**
 * Invalid item placement error
 */
export class InvalidItemPlacementError extends ItemError {
  constructor(itemId: string, reason: string, position?: { x: number; y: number }) {
    const message = `Cannot place item '${itemId}': ${reason}`;
    super(message, itemId);
    this.name = 'InvalidItemPlacementError';
    if (position) {
      this.details = { ...this.details, position };
    }
  }
}

/**
 * Character related errors
 */
export class CharacterError extends GameError {
  constructor(message: string, characterId?: string) {
    super(message, 400, { characterId });
    this.name = 'CharacterError';
  }
}

/**
 * Character not found error
 */
export class CharacterNotFoundError extends CharacterError {
  constructor(characterId: string) {
    super(`Character '${characterId}' not found`, characterId);
    this.name = 'CharacterNotFoundError';
    this.httpCode = 404;
  }
}

/**
 * Character name already exists error
 */
export class CharacterNameExistsError extends CharacterError {
  constructor(characterName: string) {
    super(`Character name '${characterName}' already exists`);
    this.name = 'CharacterNameExistsError';
    this.httpCode = 409;
    this.details = { characterName };
  }
}
