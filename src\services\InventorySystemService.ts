import pool from '../utils/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';
import { ShopError } from '../errors';
import InventoryService, { InventoryItemWithDetails } from './InventoryService';
import CurrencyService from './CurrencyService';

/**
 * Inventory configuration interface
 */
export interface InventoryConfig extends RowDataPacket {
  user_id: string;
  max_capacity: number;
  default_sort_order: string;
  auto_sort_enabled: boolean;
  show_new_items_first: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Enhanced inventory item interface with additional properties
 */
export interface EnhancedInventoryItem extends InventoryItemWithDetails {
  is_locked: boolean;
  is_new: boolean;
  is_equipped: boolean;
  custom_data: any;
  rarity?: string;
  can_use?: boolean;
  can_sell?: boolean;
  can_gift?: boolean;
  can_equip?: boolean;
}

/**
 * Inventory category mapping
 */
export const INVENTORY_CATEGORIES = {
  FASHION: {
    name: '时装',
    types: ['wearable'],
    subcategories: ['top', 'bottom', 'dress', 'hairstyle', 'shoes', 'wings', 'outfit']
  },
  FURNITURE: {
    name: '家具',
    types: ['furniture'],
    subcategories: ['indoor_furniture', 'garden_decoration', 'wallpaper', 'flooring', 'themed_set']
  },
  TOOLS: {
    name: '道具',
    types: ['tool', 'consumable'],
    subcategories: ['seed', 'growth_tool', 'experience_card', 'boost_item']
  },
  MATERIALS: {
    name: '材料',
    types: ['collectible'],
    subcategories: ['flower', 'dew', 'ore', 'craft_scroll']
  },
  QUEST: {
    name: '任务',
    types: ['quest_item'],
    subcategories: ['quest_tool', 'letter', 'certificate']
  }
} as const;

/**
 * Sort order options
 */
export type SortOrder =
  | 'acquired_time_desc'
  | 'acquired_time_asc'
  | 'name_asc'
  | 'name_desc'
  | 'quantity_desc'
  | 'quantity_asc'
  | 'rarity_desc';

/**
 * Inventory filter options
 */
export interface InventoryFilter {
  category?: keyof typeof INVENTORY_CATEGORIES;
  subcategory?: string;
  itemType?: string;
  isNew?: boolean;
  isEquipped?: boolean;
  isLocked?: boolean;
  search?: string;
  rarity?: string;
}

/**
 * Inventory pagination options
 */
export interface InventoryPagination {
  page: number;
  pageSize: number;
}

/**
 * Inventory response interface
 */
export interface InventoryResponse {
  items: EnhancedInventoryItem[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  hasNewItems: boolean;
  capacityUsed: number;
  maxCapacity: number;
  categories: Record<string, number>;
}

/**
 * Enhanced Inventory System Service
 * Extends the basic InventoryService with advanced inventory management features
 */
class InventorySystemService {
  private static instance: InventorySystemService;

  private constructor() {}

  public static getInstance(): InventorySystemService {
    if (!InventorySystemService.instance) {
      InventorySystemService.instance = new InventorySystemService();
    }
    return InventorySystemService.instance;
  }

  /**
   * Get user's inventory configuration
   */
  async getInventoryConfig(userId: string): Promise<InventoryConfig> {
    try {
      const [rows] = await pool.query<InventoryConfig[]>(
        'SELECT * FROM user_inventory_config WHERE user_id = ?',
        [userId]
      );

      if (rows.length === 0) {
        // Create default config if not exists
        return await this.createDefaultInventoryConfig(userId);
      }

      return rows[0];
    } catch (error) {
      console.error('Failed to get inventory config:', error);
      throw ShopError.inventoryOperationFailed('get_config', 'Database error');
    }
  }

  /**
   * Create default inventory configuration for user
   */
  async createDefaultInventoryConfig(userId: string): Promise<InventoryConfig> {
    try {
      await pool.query<ResultSetHeader>(
        `INSERT INTO user_inventory_config (user_id, max_capacity, default_sort_order, auto_sort_enabled, show_new_items_first)
         VALUES (?, 100, 'acquired_time_desc', FALSE, TRUE)`,
        [userId]
      );

      const [rows] = await pool.query<InventoryConfig[]>(
        'SELECT * FROM user_inventory_config WHERE user_id = ?',
        [userId]
      );

      return rows[0];
    } catch (error) {
      console.error('Failed to create default inventory config:', error);
      throw ShopError.inventoryOperationFailed('create_config', 'Database error');
    }
  }

  /**
   * Update inventory configuration
   */
  async updateInventoryConfig(
    userId: string,
    config: Partial<Omit<InventoryConfig, 'user_id' | 'created_at' | 'updated_at'>>
  ): Promise<InventoryConfig> {
    try {
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      Object.entries(config).forEach(([key, value]) => {
        if (value !== undefined) {
          updateFields.push(`${key} = ?`);
          updateValues.push(value);
        }
      });

      if (updateFields.length === 0) {
        throw ShopError.invalidRequest('No fields to update');
      }

      updateValues.push(userId);

      await pool.query<ResultSetHeader>(
        `UPDATE user_inventory_config SET ${updateFields.join(', ')} WHERE user_id = ?`,
        updateValues
      );

      return await this.getInventoryConfig(userId);
    } catch (error) {
      console.error('Failed to update inventory config:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('update_config', 'Database error');
    }
  }

  /**
   * Get enhanced inventory with advanced features
   */
  async getEnhancedInventory(
    userId: string,
    filter: InventoryFilter = {},
    sortOrder?: SortOrder,
    pagination?: InventoryPagination
  ): Promise<InventoryResponse> {
    try {
      const config = await this.getInventoryConfig(userId);
      const actualSortOrder = sortOrder || config.default_sort_order as SortOrder;

      // Build WHERE clause
      const whereConditions: string[] = ['ui.user_id = ?'];
      const queryParams: any[] = [userId];

      // Apply filters
      if (filter.category) {
        const categoryConfig = INVENTORY_CATEGORIES[filter.category];
        if (categoryConfig) {
          whereConditions.push(`i.item_type IN (${categoryConfig.types.map(() => '?').join(', ')})`);
          queryParams.push(...categoryConfig.types);
        }
      }

      if (filter.subcategory) {
        whereConditions.push('i.category = ?');
        queryParams.push(filter.subcategory);
      }

      if (filter.itemType) {
        whereConditions.push('i.item_type = ?');
        queryParams.push(filter.itemType);
      }

      if (filter.isNew !== undefined) {
        whereConditions.push('ui.is_new = ?');
        queryParams.push(filter.isNew);
      }

      if (filter.isEquipped !== undefined) {
        whereConditions.push('ui.is_equipped = ?');
        queryParams.push(filter.isEquipped);
      }

      if (filter.isLocked !== undefined) {
        whereConditions.push('ui.is_locked = ?');
        queryParams.push(filter.isLocked);
      }

      if (filter.search) {
        whereConditions.push('(i.item_name LIKE ? OR i.description LIKE ?)');
        const searchTerm = `%${filter.search}%`;
        queryParams.push(searchTerm, searchTerm);
      }

      // Build ORDER BY clause
      let orderByClause = '';
      switch (actualSortOrder) {
        case 'acquired_time_desc':
          orderByClause = 'ORDER BY ui.acquired_at DESC';
          break;
        case 'acquired_time_asc':
          orderByClause = 'ORDER BY ui.acquired_at ASC';
          break;
        case 'name_asc':
          orderByClause = 'ORDER BY i.item_name ASC';
          break;
        case 'name_desc':
          orderByClause = 'ORDER BY i.item_name DESC';
          break;
        case 'quantity_desc':
          orderByClause = 'ORDER BY ui.quantity DESC';
          break;
        case 'quantity_asc':
          orderByClause = 'ORDER BY ui.quantity ASC';
          break;
        case 'rarity_desc':
          orderByClause = 'ORDER BY FIELD(JSON_UNQUOTE(JSON_EXTRACT(i.data, "$.rarity")), "legendary", "epic", "rare", "uncommon", "common") ASC';
          break;
        default:
          orderByClause = 'ORDER BY ui.acquired_at DESC';
      }

      // Add "new items first" if enabled
      if (config.show_new_items_first) {
        orderByClause = 'ORDER BY ui.is_new DESC, ' + orderByClause.replace('ORDER BY ', '');
      }

      // Build main query
      const baseQuery = `
        SELECT
          ui.id, ui.user_id, ui.item_id, ui.quantity, ui.acquired_at, ui.updated_at,
          ui.is_locked, ui.is_new, ui.is_equipped, ui.custom_data,
          i.item_name, i.description as item_description, i.item_type, i.category as item_category,
          i.is_stackable, i.max_stack_size, i.data as item_data
        FROM user_inventory ui
        JOIN items i ON ui.item_id = i.item_id
        WHERE ${whereConditions.join(' AND ')}
        ${orderByClause}
      `;

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM user_inventory ui
        JOIN items i ON ui.item_id = i.item_id
        WHERE ${whereConditions.join(' AND ')}
      `;

      const [countRows] = await pool.query<RowDataPacket[]>(countQuery, queryParams);
      const totalItems = countRows[0].total;

      // Apply pagination
      let finalQuery = baseQuery;
      if (pagination) {
        const offset = (pagination.page - 1) * pagination.pageSize;
        finalQuery += ` LIMIT ${pagination.pageSize} OFFSET ${offset}`;
      }

      const [rows] = await pool.query<EnhancedInventoryItem[]>(finalQuery, queryParams);

      // Enhance items with additional properties
      const enhancedItems = rows.map(item => this.enhanceInventoryItem(item));

      // Get category counts
      const categories = await this.getCategoryCounts(userId);

      // Check for new items
      const hasNewItems = rows.some(item => item.is_new);

      return {
        items: enhancedItems,
        totalItems,
        totalPages: pagination ? Math.ceil(totalItems / pagination.pageSize) : 1,
        currentPage: pagination?.page || 1,
        hasNewItems,
        capacityUsed: totalItems,
        maxCapacity: config.max_capacity,
        categories
      };

    } catch (error) {
      console.error('Failed to get enhanced inventory:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('get_enhanced_inventory', 'Database error');
    }
  }

  /**
   * Enhance inventory item with additional properties
   */
  private enhanceInventoryItem(item: any): EnhancedInventoryItem {
    const itemData = item.item_data ? JSON.parse(item.item_data) : {};
    const customData = item.custom_data ? JSON.parse(item.custom_data) : {};

    return {
      ...item,
      rarity: itemData.rarity || 'common',
      can_use: this.canUseItem(item),
      can_sell: this.canSellItem(item),
      can_gift: this.canGiftItem(item),
      can_equip: this.canEquipItem(item),
      custom_data: customData
    };
  }

  /**
   * Check if item can be used
   */
  private canUseItem(item: any): boolean {
    return ['consumable', 'tool'].includes(item.item_type) && !item.is_locked;
  }

  /**
   * Check if item can be sold
   */
  private canSellItem(item: any): boolean {
    const itemData = item.item_data ? JSON.parse(item.item_data) : {};
    return !item.is_locked && !itemData.bound && item.item_type !== 'quest_item';
  }

  /**
   * Check if item can be gifted
   */
  private canGiftItem(item: any): boolean {
    const itemData = item.item_data ? JSON.parse(item.item_data) : {};
    return !item.is_locked && !itemData.bound && !item.is_equipped && item.item_type !== 'quest_item';
  }

  /**
   * Check if item can be equipped
   */
  private canEquipItem(item: any): boolean {
    return item.item_type === 'wearable' && !item.is_locked;
  }

  /**
   * Get category counts for user's inventory
   */
  async getCategoryCounts(userId: string): Promise<Record<string, number>> {
    try {
      const counts: Record<string, number> = {};

      for (const [categoryKey, categoryConfig] of Object.entries(INVENTORY_CATEGORIES)) {
        const [rows] = await pool.query<RowDataPacket[]>(
          `SELECT COUNT(*) as count
           FROM user_inventory ui
           JOIN items i ON ui.item_id = i.item_id
           WHERE ui.user_id = ? AND i.item_type IN (${categoryConfig.types.map(() => '?').join(', ')})`,
          [userId, ...categoryConfig.types]
        );

        counts[categoryKey.toLowerCase()] = rows[0].count;
      }

      return counts;
    } catch (error) {
      console.error('Failed to get category counts:', error);
      return {};
    }
  }

  /**
   * Mark items as seen (remove "new" status)
   */
  async markItemsAsSeen(userId: string, itemIds?: string[]): Promise<void> {
    try {
      let query = 'UPDATE user_inventory SET is_new = FALSE WHERE user_id = ?';
      const params: any[] = [userId];

      if (itemIds && itemIds.length > 0) {
        query += ` AND item_id IN (${itemIds.map(() => '?').join(', ')})`;
        params.push(...itemIds);
      }

      await pool.query<ResultSetHeader>(query, params);

      // Log operation
      if (itemIds && itemIds.length > 0) {
        for (const itemId of itemIds) {
          await this.logInventoryOperation(userId, itemId, 'mark_seen', 0, 0, 0, 'user_action');
        }
      }
    } catch (error) {
      console.error('Failed to mark items as seen:', error);
      throw ShopError.inventoryOperationFailed('mark_seen', 'Database error');
    }
  }

  /**
   * Lock/unlock inventory item
   */
  async toggleItemLock(userId: string, itemId: string, locked: boolean): Promise<void> {
    try {
      const [result] = await pool.query<ResultSetHeader>(
        'UPDATE user_inventory SET is_locked = ? WHERE user_id = ? AND item_id = ?',
        [locked, userId, itemId]
      );

      if (result.affectedRows === 0) {
        throw ShopError.itemNotFound(itemId);
      }

      // Log operation
      await this.logInventoryOperation(
        userId,
        itemId,
        locked ? 'lock' : 'unlock',
        0,
        0,
        0,
        'user_action'
      );
    } catch (error) {
      console.error('Failed to toggle item lock:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('toggle_lock', 'Database error');
    }
  }

  /**
   * Equip/unequip wearable item
   */
  async toggleItemEquip(userId: string, itemId: string, equipped: boolean): Promise<void> {
    try {
      // Verify item is wearable
      const [itemRows] = await pool.query<RowDataPacket[]>(
        'SELECT i.item_type FROM user_inventory ui JOIN items i ON ui.item_id = i.item_id WHERE ui.user_id = ? AND ui.item_id = ?',
        [userId, itemId]
      );

      if (itemRows.length === 0) {
        throw ShopError.itemNotFound(itemId);
      }

      if (itemRows[0].item_type !== 'wearable') {
        throw ShopError.invalidRequest('Item is not wearable');
      }

      const [result] = await pool.query<ResultSetHeader>(
        'UPDATE user_inventory SET is_equipped = ? WHERE user_id = ? AND item_id = ?',
        [equipped, userId, itemId]
      );

      if (result.affectedRows === 0) {
        throw ShopError.itemNotFound(itemId);
      }

      // Log operation
      await this.logInventoryOperation(
        userId,
        itemId,
        equipped ? 'equip' : 'unequip',
        0,
        0,
        0,
        'user_action'
      );
    } catch (error) {
      console.error('Failed to toggle item equip:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('toggle_equip', 'Database error');
    }
  }

  /**
   * Use consumable item
   */
  async useItem(userId: string, itemId: string, quantity: number = 1): Promise<{ success: boolean; effects?: any }> {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Get item details
      const [itemRows] = await connection.query<RowDataPacket[]>(
        `SELECT i.item_type, ui.quantity, ui.is_locked, i.data as item_data
         FROM user_inventory ui
         JOIN items i ON ui.item_id = i.item_id
         WHERE ui.user_id = ? AND ui.item_id = ?`,
        [userId, itemId]
      );

      if (itemRows.length === 0) {
        throw ShopError.itemNotFound(itemId);
      }

      const item = itemRows[0];

      if (item.is_locked) {
        throw ShopError.invalidRequest('Item is locked');
      }

      if (!['consumable', 'tool'].includes(item.item_type)) {
        throw ShopError.invalidRequest('Item is not usable');
      }

      if (item.quantity < quantity) {
        throw ShopError.invalidQuantity(quantity, 1, item.quantity);
      }

      // Remove item from inventory
      await InventoryService.removeItem(userId, itemId, quantity);

      // Apply item effects (this would be expanded based on specific item effects)
      const itemData = item.item_data ? JSON.parse(item.item_data) : {};
      const effects = await this.applyItemEffects(userId, itemId, itemData, quantity);

      // Log operation
      await this.logInventoryOperation(
        userId,
        itemId,
        'use',
        -quantity,
        item.quantity,
        item.quantity - quantity,
        'user_action',
        { effects }
      );

      await connection.commit();

      return { success: true, effects };

    } catch (error) {
      await connection.rollback();
      console.error('Failed to use item:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('use_item', 'Transaction failed');
    } finally {
      connection.release();
    }
  }

  /**
   * Apply item effects when used
   */
  private async applyItemEffects(userId: string, itemId: string, itemData: any, quantity: number): Promise<any> {
    const effects: any = {};

    // Example effects - expand based on your game's needs
    if (itemData.effects) {
      for (const effect of itemData.effects) {
        switch (effect.type) {
          case 'currency':
            if (effect.currency === 'gold') {
              await CurrencyService.addCurrency(userId, effect.amount * quantity, 0, `Used ${itemId}`);
              effects.goldGained = effect.amount * quantity;
            } else if (effect.currency === 'diamonds') {
              await CurrencyService.addCurrency(userId, 0, effect.amount * quantity, `Used ${itemId}`);
              effects.diamondsGained = effect.amount * quantity;
            }
            break;
          case 'experience':
            // Add experience logic here
            effects.experienceGained = effect.amount * quantity;
            break;
          // Add more effect types as needed
        }
      }
    }

    return effects;
  }

  /**
   * Sell item to system shop
   */
  async sellItem(userId: string, itemId: string, quantity: number = 1): Promise<{ success: boolean; goldEarned: number }> {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Get item details
      const [itemRows] = await connection.query<RowDataPacket[]>(
        `SELECT i.item_type, ui.quantity, ui.is_locked, ui.is_equipped, i.price, i.data as item_data
         FROM user_inventory ui
         JOIN items i ON ui.item_id = i.item_id
         WHERE ui.user_id = ? AND ui.item_id = ?`,
        [userId, itemId]
      );

      if (itemRows.length === 0) {
        throw ShopError.itemNotFound(itemId);
      }

      const item = itemRows[0];

      if (item.is_locked) {
        throw ShopError.invalidRequest('Item is locked');
      }

      if (item.is_equipped) {
        throw ShopError.invalidRequest('Cannot sell equipped item');
      }

      if (item.item_type === 'quest_item') {
        throw ShopError.invalidRequest('Quest items cannot be sold');
      }

      const itemData = item.item_data ? JSON.parse(item.item_data) : {};
      if (itemData.bound) {
        throw ShopError.invalidRequest('Bound items cannot be sold');
      }

      if (item.quantity < quantity) {
        throw ShopError.invalidQuantity(quantity, 1, item.quantity);
      }

      // Calculate sell price (typically 50% of original price)
      const sellPrice = Math.floor((item.price || 0) * 0.5);
      const totalGold = sellPrice * quantity;

      // Remove item from inventory
      await InventoryService.removeItem(userId, itemId, quantity);

      // Add gold to user
      if (totalGold > 0) {
        await CurrencyService.addCurrency(userId, totalGold, 0, `Sold ${quantity}x ${itemId}`);
      }

      // Log operation
      await this.logInventoryOperation(
        userId,
        itemId,
        'sell',
        -quantity,
        item.quantity,
        item.quantity - quantity,
        'system_shop',
        { goldEarned: totalGold, sellPrice }
      );

      await connection.commit();

      return { success: true, goldEarned: totalGold };

    } catch (error) {
      await connection.rollback();
      console.error('Failed to sell item:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('sell_item', 'Transaction failed');
    } finally {
      connection.release();
    }
  }

  /**
   * Destroy item permanently
   */
  async destroyItem(userId: string, itemId: string, quantity: number = 1): Promise<void> {
    try {
      // Get item details
      const [itemRows] = await pool.query<RowDataPacket[]>(
        'SELECT ui.quantity, ui.is_locked FROM user_inventory ui WHERE ui.user_id = ? AND ui.item_id = ?',
        [userId, itemId]
      );

      if (itemRows.length === 0) {
        throw ShopError.itemNotFound(itemId);
      }

      const item = itemRows[0];

      if (item.is_locked) {
        throw ShopError.invalidRequest('Item is locked');
      }

      if (item.quantity < quantity) {
        throw ShopError.invalidQuantity(quantity, 1, item.quantity);
      }

      // Remove item from inventory
      await InventoryService.removeItem(userId, itemId, quantity);

      // Log operation
      await this.logInventoryOperation(
        userId,
        itemId,
        'destroy',
        -quantity,
        item.quantity,
        item.quantity - quantity,
        'user_action'
      );

    } catch (error) {
      console.error('Failed to destroy item:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('destroy_item', 'Database error');
    }
  }

  /**
   * Expand inventory capacity
   */
  async expandInventoryCapacity(userId: string, additionalSlots: number, costDiamonds: number): Promise<InventoryConfig> {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Check if user has enough diamonds
      const userCurrency = await CurrencyService.getUserCurrency(userId);
      if (userCurrency.diamonds < costDiamonds) {
        throw ShopError.insufficientCurrency('diamonds', costDiamonds, userCurrency.diamonds);
      }

      // Get current config
      const config = await this.getInventoryConfig(userId);

      // Update capacity
      const newCapacity = config.max_capacity + additionalSlots;
      await this.updateInventoryConfig(userId, { max_capacity: newCapacity });

      // Deduct diamonds
      await CurrencyService.deductCurrency(userId, 0, costDiamonds, 'Inventory expansion');

      await connection.commit();

      return await this.getInventoryConfig(userId);

    } catch (error) {
      await connection.rollback();
      console.error('Failed to expand inventory capacity:', error);
      if (error instanceof ShopError) {
        throw error;
      }
      throw ShopError.inventoryOperationFailed('expand_capacity', 'Transaction failed');
    } finally {
      connection.release();
    }
  }

  /**
   * Get recently acquired items
   */
  async getRecentlyAcquiredItems(userId: string, limit: number = 10): Promise<EnhancedInventoryItem[]> {
    try {
      const [rows] = await pool.query<EnhancedInventoryItem[]>(
        `SELECT
          ui.id, ui.user_id, ui.item_id, ui.quantity, ui.acquired_at, ui.updated_at,
          ui.is_locked, ui.is_new, ui.is_equipped, ui.custom_data,
          i.item_name, i.description as item_description, i.item_type, i.category as item_category,
          i.is_stackable, i.max_stack_size, i.data as item_data
         FROM user_inventory ui
         JOIN items i ON ui.item_id = i.item_id
         WHERE ui.user_id = ?
         ORDER BY ui.acquired_at DESC
         LIMIT ?`,
        [userId, limit]
      );

      return rows.map(item => this.enhanceInventoryItem(item));
    } catch (error) {
      console.error('Failed to get recently acquired items:', error);
      throw ShopError.inventoryOperationFailed('get_recent_items', 'Database error');
    }
  }

  /**
   * Search inventory items
   */
  async searchInventoryItems(userId: string, searchTerm: string, limit: number = 20): Promise<EnhancedInventoryItem[]> {
    try {
      const [rows] = await pool.query<EnhancedInventoryItem[]>(
        `SELECT
          ui.id, ui.user_id, ui.item_id, ui.quantity, ui.acquired_at, ui.updated_at,
          ui.is_locked, ui.is_new, ui.is_equipped, ui.custom_data,
          i.item_name, i.description as item_description, i.item_type, i.category as item_category,
          i.is_stackable, i.max_stack_size, i.data as item_data
         FROM user_inventory ui
         JOIN items i ON ui.item_id = i.item_id
         WHERE ui.user_id = ? AND (i.item_name LIKE ? OR i.description LIKE ?)
         ORDER BY ui.acquired_at DESC
         LIMIT ?`,
        [userId, `%${searchTerm}%`, `%${searchTerm}%`, limit]
      );

      return rows.map(item => this.enhanceInventoryItem(item));
    } catch (error) {
      console.error('Failed to search inventory items:', error);
      throw ShopError.inventoryOperationFailed('search_items', 'Database error');
    }
  }

  /**
   * Log inventory operation
   */
  private async logInventoryOperation(
    userId: string,
    itemId: string,
    operationType: string,
    quantityChange: number,
    quantityBefore: number,
    quantityAfter: number,
    operationSource: string,
    metadata?: any
  ): Promise<void> {
    try {
      await pool.query<ResultSetHeader>(
        `INSERT INTO inventory_operations_log
         (user_id, item_id, operation_type, quantity_change, quantity_before, quantity_after, operation_source, metadata)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          userId,
          itemId,
          operationType,
          quantityChange,
          quantityBefore,
          quantityAfter,
          operationSource,
          metadata ? JSON.stringify(metadata) : null
        ]
      );
    } catch (error) {
      console.error('Failed to log inventory operation:', error);
      // Don't throw error for logging failures
    }
  }

  /**
   * Get inventory operation history
   */
  async getInventoryOperationHistory(
    userId: string,
    itemId?: string,
    operationType?: string,
    limit: number = 50
  ): Promise<any[]> {
    try {
      let query = `
        SELECT iol.*, i.item_name
        FROM inventory_operations_log iol
        JOIN items i ON iol.item_id = i.item_id
        WHERE iol.user_id = ?
      `;
      const params: any[] = [userId];

      if (itemId) {
        query += ' AND iol.item_id = ?';
        params.push(itemId);
      }

      if (operationType) {
        query += ' AND iol.operation_type = ?';
        params.push(operationType);
      }

      query += ' ORDER BY iol.created_at DESC LIMIT ?';
      params.push(limit);

      const [rows] = await pool.query<any[]>(query, params);

      return rows.map(row => ({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : null
      }));
    } catch (error) {
      console.error('Failed to get inventory operation history:', error);
      throw ShopError.inventoryOperationFailed('get_operation_history', 'Database error');
    }
  }
}

export default InventorySystemService.getInstance();
