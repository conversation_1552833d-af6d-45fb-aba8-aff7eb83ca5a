// Test script to verify boolean conversion in SpaceService
const path = require('path');
require('ts-node').register({
  project: path.join(__dirname, '..', 'tsconfig.json')
});

const SpaceService = require('../src/services/SpaceService').default;

async function testBooleanConversion() {
  const testUserId = '2';
  
  try {
    console.log('🧪 Testing boolean conversion in SpaceService...');
    
    // Get space data
    const spaceData = await SpaceService.getFullPrivateSpaceData(testUserId);
    
    console.log(`📊 Found ${spaceData.homeItems.length} home items`);
    
    if (spaceData.homeItems.length > 0) {
      const firstItem = spaceData.homeItems[0];
      
      console.log('🔍 First item details:');
      console.log(`  - item_instance_id: ${firstItem.item_instance_id}`);
      console.log(`  - item_template_id: ${firstItem.item_template_id}`);
      console.log(`  - is_flipped: ${firstItem.is_flipped} (type: ${typeof firstItem.is_flipped})`);
      console.log(`  - pos_x: ${firstItem.pos_x} (type: ${typeof firstItem.pos_x})`);
      console.log(`  - pos_y: ${firstItem.pos_y} (type: ${typeof firstItem.pos_y})`);
      console.log(`  - rotation: ${firstItem.rotation} (type: ${typeof firstItem.rotation})`);
      
      // Test boolean conversion
      if (typeof firstItem.is_flipped === 'boolean') {
        console.log('✅ Boolean conversion is working correctly!');
      } else {
        console.log('❌ Boolean conversion is NOT working - still getting:', typeof firstItem.is_flipped);
      }
    } else {
      console.log('⚠️  No home items found for testing');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testBooleanConversion();
