-- Fix user_inventory table structure
-- Add missing columns that are referenced in the code

-- Add missing columns to user_inventory table
ALTER TABLE user_inventory 
ADD COLUMN IF NOT EXISTS is_locked BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether item is locked to prevent accidental operations',
ADD COLUMN IF NOT EXISTS is_new BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether item has "new" marker',
ADD COLUMN IF NOT EXISTS is_equipped BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether item is currently equipped/worn',
ADD COLUMN IF NOT EXISTS custom_data JSON NULL COMMENT 'Custom item data (enchantments, durability, etc.)';

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_new ON user_inventory (user_id, is_new);
CREATE INDEX IF NOT EXISTS idx_user_equipped ON user_inventory (user_id, is_equipped);
CREATE INDEX IF NOT EXISTS idx_user_locked ON user_inventory (user_id, is_locked);

-- Create user_inventory_config table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_inventory_config (
  user_id BIGINT NOT NULL COMMENT 'Reference to user ID',
  max_capacity INT UNSIGNED NOT NULL DEFAULT 100 COMMENT 'Maximum inventory capacity',
  default_sort_order ENUM('acquired_time_desc', 'acquired_time_asc', 'name_asc', 'name_desc', 'quantity_desc', 'quantity_asc', 'rarity_desc') NOT NULL DEFAULT 'acquired_time_desc' COMMENT 'Default sort order',
  auto_sort_enabled BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether auto-sort is enabled',
  show_new_items_first BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether to show new items first',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (user_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores user inventory configuration and preferences';

-- Create inventory_operations_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS inventory_operations_log (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique log entry ID',
  user_id BIGINT NOT NULL COMMENT 'Reference to user ID',
  item_id VARCHAR(255) NOT NULL COMMENT 'Reference to item template',
  operation_type ENUM('add', 'remove', 'use', 'sell', 'gift_send', 'gift_receive', 'equip', 'unequip', 'lock', 'unlock', 'destroy') NOT NULL COMMENT 'Type of operation',
  quantity_change INT NOT NULL COMMENT 'Quantity change (positive for add, negative for remove)',
  quantity_before BIGINT UNSIGNED NOT NULL COMMENT 'Quantity before operation',
  quantity_after BIGINT UNSIGNED NOT NULL COMMENT 'Quantity after operation',
  operation_source VARCHAR(255) COMMENT 'Source of operation (shop, quest, gift, etc.)',
  metadata JSON COMMENT 'Additional operation metadata',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (item_id) REFERENCES items(item_id) ON DELETE CASCADE,
  INDEX idx_user_operation (user_id, operation_type, created_at),
  INDEX idx_item_operation (item_id, operation_type, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Logs all inventory operations for auditing and analytics';
