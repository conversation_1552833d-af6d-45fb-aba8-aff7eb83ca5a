import { Router, Request, Response } from 'express';
import OutfitService from '../services/OutfitService';
import authService from '../services/authService';

const router = Router();

/**
 * Middleware to verify authentication token
 */
const verifyAuth = async (req: Request & { user?: { id: string } }, res: Response, next: any) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          name: 'MissingTokenError',
          message: 'Authorization token is required'
        }
      });
    }

    const userPayload = await authService.verifyToken(token);
    if (!userPayload || !userPayload.userId) {
      return res.status(401).json({
        success: false,
        error: {
          name: 'InvalidTokenError',
          message: 'Invalid or expired token'
        }
      });
    }

    req.user = { id: userPayload.userId.toString() };
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: {
        name: 'AuthenticationError',
        message: 'Authentication failed'
      }
    });
  }
};

/**
 * Get user's current outfit
 * GET /api/outfits/current
 */
router.get('/current', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const userId = req.user!.id;
    const outfitData = await OutfitService.loadUserCurrentOutfit(userId);

    res.json({
      success: true,
      data: {
        outfit: outfitData
      }
    });

  } catch (error) {
    console.error('Get current outfit error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to get current outfit'
      }
    });
  }
});

/**
 * Save user's current outfit
 * PUT /api/outfits/current
 */
router.put('/current', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const userId = req.user!.id;
    const { outfitData, outfitName } = req.body;

    if (!outfitData) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Outfit data is required'
        }
      });
    }

    // Validate outfit data
    const validation = OutfitService.validateOutfitData(outfitData);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Invalid outfit data',
          details: validation.errors
        }
      });
    }

    const success = await OutfitService.saveUserCurrentOutfit(userId, outfitData, outfitName);

    if (success) {
      res.json({
        success: true,
        message: 'Current outfit saved successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        error: {
          name: 'SaveError',
          message: 'Failed to save current outfit'
        }
      });
    }

  } catch (error) {
    console.error('Save current outfit error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to save current outfit'
      }
    });
  }
});

/**
 * Get all user outfits
 * GET /api/outfits
 */
router.get('/', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const userId = req.user!.id;
    const outfits = await OutfitService.getUserOutfits(userId);

    res.json({
      success: true,
      data: {
        outfits: outfits.map(outfit => ({
          id: outfit.id,
          name: outfit.outfit_name,
          isCurrent: outfit.is_current,
          createdAt: outfit.created_at,
          updatedAt: outfit.updated_at,
          outfitData: JSON.parse(outfit.outfit_data)
        }))
      }
    });

  } catch (error) {
    console.error('Get user outfits error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to get user outfits'
      }
    });
  }
});

/**
 * Save a new outfit
 * POST /api/outfits
 */
router.post('/', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const userId = req.user!.id;
    const { outfitName, outfitData, setCurrent } = req.body;

    if (!outfitName || !outfitData) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Outfit name and data are required'
        }
      });
    }

    // Validate outfit data
    const validation = OutfitService.validateOutfitData(outfitData);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Invalid outfit data',
          details: validation.errors
        }
      });
    }

    const result = await OutfitService.saveUserOutfit(userId, outfitName, outfitData, setCurrent);

    if (result.success) {
      res.json({
        success: true,
        data: {
          outfitId: result.outfitId
        },
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        error: {
          name: 'SaveError',
          message: result.message
        }
      });
    }

  } catch (error) {
    console.error('Save outfit error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to save outfit'
      }
    });
  }
});

/**
 * Load a specific outfit
 * GET /api/outfits/:outfitId
 */
router.get('/:outfitId', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const userId = req.user!.id;
    const { outfitId } = req.params;

    const outfitData = await OutfitService.loadUserOutfit(userId, outfitId);

    if (outfitData) {
      res.json({
        success: true,
        data: {
          outfit: outfitData
        }
      });
    } else {
      res.status(404).json({
        success: false,
        error: {
          name: 'NotFoundError',
          message: 'Outfit not found'
        }
      });
    }

  } catch (error) {
    console.error('Load outfit error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to load outfit'
      }
    });
  }
});

/**
 * Delete an outfit
 * DELETE /api/outfits/:outfitId
 */
router.delete('/:outfitId', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const userId = req.user!.id;
    const { outfitId } = req.params;

    const result = await OutfitService.deleteUserOutfit(userId, outfitId);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        error: {
          name: 'DeleteError',
          message: result.message
        }
      });
    }

  } catch (error) {
    console.error('Delete outfit error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to delete outfit'
      }
    });
  }
});

/**
 * Set an outfit as current
 * PUT /api/outfits/:outfitId/current
 */
router.put('/:outfitId/current', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const userId = req.user!.id;
    const { outfitId } = req.params;

    const result = await OutfitService.setCurrentOutfit(userId, outfitId);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        error: {
          name: 'UpdateError',
          message: result.message
        }
      });
    }

  } catch (error) {
    console.error('Set current outfit error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to set current outfit'
      }
    });
  }
});

/**
 * Get outfit templates
 * GET /api/outfits/templates
 */
router.get('/templates/list', async (req: Request, res: Response) => {
  try {
    const { category, rarity } = req.query;

    const templates = await OutfitService.getOutfitTemplates(
      category as string,
      rarity as string
    );

    res.json({
      success: true,
      data: {
        templates: templates.map(template => ({
          id: template.id,
          name: template.name,
          description: template.description,
          category: template.category,
          rarity: template.rarity,
          isDefault: template.is_default,
          isPurchasable: template.is_purchasable,
          priceGold: template.price_gold,
          priceDiamonds: template.price_diamonds,
          unlockLevel: template.unlock_level,
          previewImage: template.preview_image,
          outfitData: JSON.parse(template.outfit_data)
        }))
      }
    });

  } catch (error) {
    console.error('Get outfit templates error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to get outfit templates'
      }
    });
  }
});

/**
 * Apply an outfit template
 * POST /api/outfits/templates/:templateId/apply
 */
router.post('/templates/:templateId/apply', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const userId = req.user!.id;
    const { templateId } = req.params;

    const result = await OutfitService.applyOutfitTemplate(userId, templateId);

    if (result.success) {
      res.json({
        success: true,
        data: {
          outfit: result.outfitData
        },
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        error: {
          name: 'ApplyError',
          message: result.message
        }
      });
    }

  } catch (error) {
    console.error('Apply outfit template error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to apply outfit template'
      }
    });
  }
});

export default router;
