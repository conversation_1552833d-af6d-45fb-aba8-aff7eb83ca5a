import pool from '../utils/db'; // Database connection pool utility
import { RowDataPacket, ResultSetHeader } from 'mysql2';

/**
 * @export
 * @interface ItemDetails
 * @description Represents the detailed structure of an item template from the `items` table.
 * @property {string} item_id - The unique identifier for the item template (e.g., "basic_chair", "tomato_seed").
 * @property {string} name - The display name of the item.
 * @property {string | null} description - A text description of the item.
 * @property {'furniture' | 'seed' | 'crop' | 'tool' | 'wearable' | 'consumable' | 'material' | 'background' | 'plot_template'} type - The broad category of the item.
 * @property {string | null} category - A more specific category (e.g., "decor", "chair", "vegetable", "hat").
 * @property {boolean} is_stackable - Whether the item can be stacked in inventory.
 * @property {number} max_stack_size - The maximum number of items in a single stack.
 * @property {string | null} data - A JSON string for additional item-specific data (e.g., dimensions, growth times, effects).
 */
export interface ItemDetails extends RowDataPacket {
  item_id: string;
  name: string;
  description: string | null;
  type: 'furniture' | 'seed' | 'crop' | 'tool' | 'wearable' | 'consumable' | 'material' | 'background' | 'plot_template';
  category: string | null;
  is_stackable: boolean;
  max_stack_size: number;
  data: string | null; // JSON string
}

/**
 * @class ItemService
 * @description Provides methods for interacting with item data and simulated inventory.
 *              Implemented as a singleton.
 */
class ItemService {
  private static instance: ItemService;

  /**
   * Private constructor to enforce singleton pattern.
   * @private
   */
  private constructor() {
    // Initialization logic if needed
  }

  /**
   * Gets the singleton instance of the ItemService.
   * @public
   * @static
   * @returns {ItemService} The singleton instance.
   */
  public static getInstance(): ItemService {
    if (!ItemService.instance) {
      ItemService.instance = new ItemService();
    }
    return ItemService.instance;
  }

  /**
   * Fetches details for a specific item template from the database.
   * @param {string} itemId - The template ID of the item to fetch.
   * @returns {Promise<ItemDetails | null>} A promise that resolves with the item details if found, otherwise null.
   * @throws {Error} If `itemId` is not provided or if a database error occurs.
   */
  async getItemDetails(itemId: string): Promise<ItemDetails | null> {
    if (!itemId || typeof itemId !== 'string') {
      throw new Error('ItemService: getItemDetails - itemId must be a non-empty string.');
    }
    try {
      const [rows] = await pool.query<RowDataPacket[]>(
        `SELECT
          item_id,
          item_name as name,
          description,
          item_type as type,
          category,
          price,
          data,
          created_at,
          updated_at
        FROM items WHERE item_id = ?`,
        [itemId]
      );
      if (rows.length > 0) {
        const item = rows[0] as ItemDetails;
        // Set default values for missing fields
        item.is_stackable = true; // Default to stackable
        item.max_stack_size = 99; // Default stack size
        return item;
      }
      // If no item is found, it's a valid case, so return null, not an error.
      // The caller can then decide if this constitutes an error in their context.
      console.log(`ItemService: No item found with itemId '${itemId}'.`);
      return null;
    } catch (error: any) {
      console.error(`ItemService: Error fetching item details for itemId '${itemId}': ${error.message}`);
      // Re-throw a more generic error to avoid exposing DB details.
      throw new Error(`Failed to fetch details for item '${itemId}' due to a server error.`);
    }
  }

  /**
   * Checks if a user has sufficient quantity of an item and uses it (decrements quantity).
   * This now uses the real inventory system instead of simulation.
   * @param {string} userId - The ID of the user whose inventory is being checked.
   * @param {string} itemId - The ID of the item template to check and use.
   * @param {number} [quantity=1] - The quantity of the item to use. Defaults to 1.
   * @returns {Promise<boolean>} A promise that resolves to true if the user has the item and it was used, false otherwise.
   * @throws {Error} If `userId` or `itemId` is invalid, quantity is not positive, or if a database/item validation error occurs.
   */
  async checkAndUseInventoryItem(userId: string, itemId: string, quantity: number = 1): Promise<boolean> {
    if (!userId || typeof userId !== 'string') {
      throw new Error('ItemService: checkAndUseInventoryItem - userId must be a non-empty string.');
    }
    if (!itemId || typeof itemId !== 'string') {
      throw new Error('ItemService: checkAndUseInventoryItem - itemId must be a non-empty string.');
    }
    if (typeof quantity !== 'number' || quantity <= 0) {
      throw new Error('ItemService: checkAndUseInventoryItem - quantity must be a positive number.');
    }

    console.log(`ItemService: Checking and attempting to use ${quantity} of item '${itemId}' for user '${userId}'.`);

    try {
      // Import InventoryService dynamically to avoid circular dependency
      const InventoryService = (await import('./InventoryService')).default;

      // Use the real inventory system to check and use the item
      const result = await InventoryService.checkAndUseItem(userId, itemId, quantity, 'item_usage');

      if (result.success) {
        console.log(`ItemService: Successfully used ${quantity} of item '${itemId}' for user '${userId}'.`);
        return true;
      } else {
        console.warn(`ItemService: Failed to use item '${itemId}' for user '${userId}': ${result.message}`);
        return false;
      }
    } catch (error: any) {
      console.error(`ItemService: Error using item '${itemId}' for user '${userId}':`, error);

      // Re-throw with more context
      if (error.message.includes('not found in inventory') || error.message.includes('Insufficient quantity')) {
        throw new Error(`Insufficient quantity of item '${itemId}' in inventory for user '${userId}'.`);
      }

      throw new Error(`Failed to use item '${itemId}' for user '${userId}': ${error.message}`);
    }
  }

   /**
   * Adds an item to a user's inventory using the real inventory system.
   * @param {string} userId - The ID of the user to whose inventory the item will be added.
   * @param {string} itemId - The ID of the item template to add.
   * @param {number} [quantity=1] - The quantity of the item to add. Defaults to 1.
   * @param {string} [source='item_service'] - The source of the item addition.
   * @returns {Promise<boolean>} A promise that resolves to true if the item was added successfully.
   * @throws {Error} If `userId` or `itemId` is invalid, quantity is not positive, or if a database/item validation error occurs.
   */
  async addInventoryItem(userId: string, itemId: string, quantity: number = 1, source: string = 'item_service'): Promise<boolean> {
    if (!userId || typeof userId !== 'string') {
      throw new Error('ItemService: addInventoryItem - userId must be a non-empty string.');
    }
    if (!itemId || typeof itemId !== 'string') {
      throw new Error('ItemService: addInventoryItem - itemId must be a non-empty string.');
    }
    if (typeof quantity !== 'number' || quantity <= 0) {
      throw new Error('ItemService: addInventoryItem - quantity must be a positive number.');
    }

    console.log(`ItemService: Attempting to add ${quantity} of item '${itemId}' to inventory for user '${userId}'.`);

    try {
      // Import InventoryService dynamically to avoid circular dependency
      const InventoryService = (await import('./InventoryService')).default;

      // Use the real inventory system to add the item
      const result = await InventoryService.addItem(userId, itemId, quantity, source);

      if (result.success) {
        console.log(`ItemService: Successfully added ${quantity} of item '${itemId}' to user '${userId}'s inventory.`);
        return true;
      } else {
        console.warn(`ItemService: Failed to add item '${itemId}' to user '${userId}': ${result.message}`);
        return false;
      }
    } catch (error: any) {
      console.error(`ItemService: Error adding item '${itemId}' to user '${userId}':`, error);

      // Re-throw with more context
      if (error.message.includes('not found') || error.message.includes('does not exist')) {
        throw new Error(`ItemService: Cannot add item '${itemId}' to inventory because it does not exist in the items table.`);
      }

      throw new Error(`Failed to add item '${itemId}' to user '${userId}' inventory: ${error.message}`);
    }
  }
}

// Export a singleton instance of ItemService.
export default ItemService.getInstance();
