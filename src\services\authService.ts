import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import pool from '../utils/db'; // Database connection pool utility
import { ResultSetHeader, RowDataPacket } from 'mysql2';
import {
  InvalidCredentialsError,
  ConflictError,
  TokenExpiredError,
  InvalidTokenError,
  TokenRevokedError,
  DatabaseError,
  ValidationError
} from '../errors';

/**
 * @interface UserAuthToken
 * @description Represents the structure of a token record in the user_auth_tokens table.
 * @property {number} uid - The ID of the user associated with this token (matches database schema).
 * @property {string} token - The SHA-256 hash of the token (matches database schema).
 * @property {number} expires_at - The expiration timestamp in Unix milliseconds (matches database schema).
 */
interface UserAuthToken extends RowDataPacket {
  uid: number;
  token: string;
  expires_at: number; // Unix timestamp in milliseconds
}

/**
 * @class AuthService
 * @description Handles authentication-related operations such as token generation, verification, and management.
 */
class AuthService {
  private jwtSecret: string;

  /**
   * @constructor
   * Initializes the AuthService, primarily setting up the JWT secret.
   * It warns if a default, insecure JWT secret is being used.
   */
  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'your_default_secret_key';
    if (this.jwtSecret === 'your_default_secret_key') {
      // It's crucial to use a strong, unique secret in production.
      // This warning helps prevent accidental use of the default key in a live environment.
      console.warn('Warning: JWT_SECRET is not set in .env, using default. This is not secure for production. Please set a strong secret in your .env file.');
    }
  }

  /**
   * Generates a JWT for a given user ID and stores its hash in the database.
   * The token's expiration time can be customized.
   * @param {number} userId - The ID of the user for whom the token is generated.
   * @param {string} [expiresIn='1h'] - Optional. Token expiration time (e.g., '1h', '7d'). Defaults to '1h'.
   * @returns {Promise<string>} A promise that resolves with the generated JWT.
   * @throws {Error} If token generation or database interaction fails.
   */
  async generateToken(userId: number, expiresIn: string = '1h'): Promise<string> {
    // Input validation: Ensure userId is a positive number.
    if (!userId || typeof userId !== 'number' || userId <= 0) {
        throw new Error('Invalid userId provided for token generation.');
    }

    const tokenPayload = { userId };
    const token = jwt.sign(tokenPayload, this.jwtSecret as jwt.Secret, { expiresIn } as jwt.SignOptions);
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

    // Calculate expiry timestamp for the database record (Unix milliseconds)
    const now = new Date();
    let expiryDate = new Date(now);

    // Simple parsing for expiresIn (e.g., '1h', '7d').
    // A more robust solution might use a library like `ms` to parse time strings.
    const unit = expiresIn.charAt(expiresIn.length - 1);
    const value = parseInt(expiresIn.slice(0, -1), 10);

    if (isNaN(value)) {
        console.warn(`Invalid expiresIn format: ${expiresIn}. Defaulting to 1 hour.`);
        expiryDate.setHours(now.getHours() + 1);
    } else {
        if (unit === 'h') {
            expiryDate.setHours(now.getHours() + value);
        } else if (unit === 'd') {
            expiryDate.setDate(now.getDate() + value);
        } else {
            // Default to 1 hour if format is unrecognized or unsupported
            console.warn(`Unrecognized expiresIn unit: ${unit} in ${expiresIn}. Defaulting to 1 hour.`);
            expiryDate.setHours(now.getHours() + 1);
        }
    }

    // Convert to Unix timestamp in milliseconds
    const expiryTimestamp = expiryDate.getTime();

    try {
      // Insert the token hash and its expiry into the database.
      // This allows for server-side invalidation/management of tokens.
      // Note: Using 'uid' and 'token' columns as per database schema
      const [result] = await pool.query<ResultSetHeader>(
        'INSERT INTO user_auth_tokens (uid, token, expires_at) VALUES (?, ?, ?)',
        [userId, tokenHash, expiryTimestamp]
      );

      if (result.affectedRows === 0) {
        // This case should ideally not happen with a valid INSERT unless there's a DB issue not throwing an error.
        throw new Error('Failed to save authentication token to the database.');
      }
      return token;
    } catch (error: any) {
      console.error('AuthService: Error saving auth token to database:', error.message);
      // Re-throw a more generic error to avoid exposing DB details.
      throw new Error('Failed to generate and save authentication token due to a server error.');
    }
  }

  /**
   * Verifies a JWT and checks its validity against the stored hash in the database.
   * @param {string} token - The JWT to verify.
   * @returns {Promise<jwt.JwtPayload | null>} A promise that resolves with the decoded token payload if valid, otherwise null.
   */
  async verifyToken(token: string): Promise<jwt.JwtPayload | null> {
    if (!token || typeof token !== 'string') {
        console.warn('AuthService: Attempted to verify an invalid or empty token.');
        return null;
    }

    try {
      // Verify the token's signature and expiration using jwt.verify.
      const decoded = jwt.verify(token, this.jwtSecret as jwt.Secret) as jwt.JwtPayload;

      // Check if the payload contains the expected userId.
      if (!decoded || typeof decoded.userId !== 'number') {
        console.warn('AuthService: Invalid token payload after verification.');
        return null;
      }

      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

      // Check if the token hash exists in the database and has not expired.
      // Note: Using 'uid' and 'token' columns as per database schema, and checking against Unix timestamp
      const [rows] = await pool.query<UserAuthToken[]>(
        'SELECT uid FROM user_auth_tokens WHERE uid = ? AND token = ? AND expires_at > ?',
        [decoded.userId, tokenHash, Date.now()]
      );

      if (rows.length > 0) {
        // Token is valid and present in the database.
        return decoded;
      } else {
        // Token hash not found or expired in DB, meaning it's effectively revoked or invalid.
        console.warn(`AuthService: Token for user ${decoded.userId} with hash ${tokenHash.substring(0,10)}... not found in DB or expired.`);
        return null;
      }
    } catch (error: any) {
      // Handle specific JWT errors (e.g., TokenExpiredError, JsonWebTokenError)
      if (error instanceof jwt.TokenExpiredError) {
        console.info(`AuthService: Token verification failed - Token expired at ${error.expiredAt}`);
      } else if (error instanceof jwt.JsonWebTokenError) {
        console.warn(`AuthService: Token verification failed - ${error.message}`);
      } else {
        // General error during verification or database query.
        console.error('AuthService: An unexpected error occurred during token verification:', error.message);
      }
      return null; // Return null for any verification failure.
    }
  }

  /**
   * Deletes a specific token (identified by the token itself) from the database.
   * This is typically used for logging out a specific session.
   * @param {string} token - The JWT to delete.
   * @returns {Promise<boolean>} A promise that resolves to true if deletion was successful, false otherwise.
   */
  async deleteToken(token: string): Promise<boolean> {
     if (!token || typeof token !== 'string') {
        console.warn('AuthService: Attempted to delete an invalid or empty token.');
        return false;
    }
    try {
      // Decode the token to get userId without verifying expiry, as we might want to delete an expired token record.
      // However, verifying signature is still important to ensure it's a legitimately issued token.
      const decoded = jwt.verify(token, this.jwtSecret as jwt.Secret, { ignoreExpiration: true }) as jwt.JwtPayload;
      if (!decoded || typeof decoded.userId !== 'number') {
        console.warn('AuthService: Could not decode token or userId missing for deletion.');
        return false;
      }

      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

      const [result] = await pool.query<ResultSetHeader>(
        'DELETE FROM user_auth_tokens WHERE uid = ? AND token = ?',
        [decoded.userId, tokenHash]
      );

      if (result.affectedRows > 0) {
        console.log(`AuthService: Token for user ${decoded.userId} deleted successfully.`);
        return true;
      } else {
        console.log(`AuthService: Token for user ${decoded.userId} not found for deletion or already deleted.`);
        return false; // Token not found or already deleted.
      }
    } catch (error: any) {
      // Log specific JWT errors if they occur during the verify step
      if (error instanceof jwt.JsonWebTokenError && !(error instanceof jwt.TokenExpiredError)) {
         console.warn(`AuthService: Attempt to delete an invalid token - ${error.message}`);
      } else {
        console.error('AuthService: Error deleting token from database:', error.message);
      }
      return false; // Indicate failure if any error occurs.
    }
  }

  /**
   * Deletes all stored tokens for a specific user.
   * This is useful for "log out all sessions" functionality or when a user changes their password.
   * @param {number} userId - The ID of the user whose tokens are to be deleted.
   * @returns {Promise<boolean>} A promise that resolves to true if deletion was successful (even if no tokens existed), false on error.
   */
  async deleteAllTokensForUser(userId: number): Promise<boolean> {
    if (!userId || typeof userId !== 'number' || userId <= 0) {
        console.warn('AuthService: Attempted to delete tokens for an invalid userId.');
        return false;
    }
    try {
      const [result] = await pool.query<ResultSetHeader>(
        'DELETE FROM user_auth_tokens WHERE uid = ?',
        [userId]
      );
      // affectedRows could be 0 if the user had no tokens, which is still a "successful" deletion in this context.
      console.log(`AuthService: All tokens for user ${userId} deleted. Rows affected: ${result.affectedRows}.`);
      return true;
    } catch (error: any) {
      console.error(`AuthService: Error deleting all tokens for user ${userId}:`, error.message);
      return false; // Indicate failure on database error.
    }
  }
}

// Export a singleton instance of the AuthService.
export default new AuthService();
