/**
 * Dynamic Room System Client Example
 * 
 * This example demonstrates how to use the dynamic room system
 * to join/create private spaces with dynamic room IDs.
 * 
 * To run this example:
 * 1. Make sure the server is running (npm start)
 * 2. Make sure you have registered users in the database
 * 3. Run: node examples/dynamic-room-client-example.js
 */

const axios = require('axios');
const { Client } = require('colyseus.js');

class DynamicRoomClientExample {
  constructor() {
    this.baseURL = 'http://localhost:2567';
    this.wsURL = 'ws://localhost:2567';
    this.client = new Client(this.wsURL);
    
    // Test user credentials (make sure these exist in your database)
    this.testUser1 = {
      username: 'testuser1',
      password: 'password123',
      token: null,
      uid: null
    };
    
    this.testUser2 = {
      username: 'testuser2', 
      password: 'password123',
      token: null,
      uid: null
    };
  }

  async runExample() {
    console.log('🏠 Dynamic Room System Client Example');
    console.log('====================================\n');

    try {
      // Step 1: Authenticate users
      console.log('1️⃣ Authenticating test users...');
      await this.authenticateUser(this.testUser1);
      await this.authenticateUser(this.testUser2);
      
      console.log(`✅ User 1 authenticated: ${this.testUser1.uid}`);
      console.log(`✅ User 2 authenticated: ${this.testUser2.uid}`);

      // Step 2: Generate room IDs
      console.log('\n2️⃣ Generating dynamic room IDs...');
      const user1HomeRoomId = await this.generateRoomId('home', this.testUser1.uid, this.testUser1);
      const user1GardenRoomId = await this.generateRoomId('garden', this.testUser1.uid, this.testUser1);
      
      console.log(`User 1 Home Room ID: ${user1HomeRoomId}`);
      console.log(`User 1 Garden Room ID: ${user1GardenRoomId}`);

      // Step 3: Check room access permissions
      console.log('\n3️⃣ Checking room access permissions...');
      const user2CanAccessUser1Home = await this.checkRoomAccess(user1HomeRoomId, this.testUser2);
      console.log(`User 2 can access User 1's home: ${user2CanAccessUser1Home}`);

      // Step 4: Get available rooms for users
      console.log('\n4️⃣ Getting available rooms...');
      const user1AvailableRooms = await this.getAvailableRooms('home', this.testUser1);
      const user2AvailableRooms = await this.getAvailableRooms('home', this.testUser2);
      
      console.log(`User 1 available home rooms: ${user1AvailableRooms.ownRooms.length} own, ${user1AvailableRooms.friendRooms.length} friend, ${user1AvailableRooms.publicRooms.length} public`);
      console.log(`User 2 available home rooms: ${user2AvailableRooms.ownRooms.length} own, ${user2AvailableRooms.friendRooms.length} friend, ${user2AvailableRooms.publicRooms.length} public`);

      // Step 5: Connect to dynamic home room
      console.log('\n5️⃣ Connecting to dynamic home room...');
      const homeRoom = await this.connectToRoom('home_dynamic', {
        roomId: user1HomeRoomId,
        token: this.testUser1.token,
        uid: this.testUser1.uid
      });

      console.log(`✅ Connected to home room: ${homeRoom.id}`);
      console.log(`Room state - Owner: ${homeRoom.state.ownerUid}, Background: ${homeRoom.state.homeBackgroundId}`);

      // Step 6: Connect to dynamic garden room
      console.log('\n6️⃣ Connecting to dynamic garden room...');
      const gardenRoom = await this.connectToRoom('garden_dynamic', {
        roomId: user1GardenRoomId,
        token: this.testUser1.token,
        uid: this.testUser1.uid
      });

      console.log(`✅ Connected to garden room: ${gardenRoom.id}`);
      console.log(`Room state - Owner: ${gardenRoom.state.ownerUid}, Background: ${gardenRoom.state.gardenBackgroundId}`);

      // Step 7: Test room operations
      console.log('\n7️⃣ Testing room operations...');
      
      // Test home room operations
      console.log('Testing home room operations...');
      homeRoom.send('updatePlayerState', { x: 100, y: 200, dir: 'down' });
      
      // Test garden room operations  
      console.log('Testing garden room operations...');
      gardenRoom.send('updatePlayerState', { x: 150, y: 250, dir: 'up' });

      // Step 8: Get public rooms
      console.log('\n8️⃣ Getting public rooms...');
      const publicHomeRooms = await this.getPublicRooms('home');
      const publicGardenRooms = await this.getPublicRooms('garden');
      
      console.log(`Public home rooms: ${publicHomeRooms.length}`);
      console.log(`Public garden rooms: ${publicGardenRooms.length}`);

      // Step 9: Parse room IDs
      console.log('\n9️⃣ Parsing room IDs...');
      const parsedHomeRoom = await this.parseRoomId(user1HomeRoomId, this.testUser1);
      const parsedGardenRoom = await this.parseRoomId(user1GardenRoomId, this.testUser1);
      
      console.log(`Parsed home room - Type: ${parsedHomeRoom.roomType}, Owner: ${parsedHomeRoom.ownerUid}, Can Access: ${parsedHomeRoom.canAccess}`);
      console.log(`Parsed garden room - Type: ${parsedGardenRoom.roomType}, Owner: ${parsedGardenRoom.ownerUid}, Can Access: ${parsedGardenRoom.canAccess}`);

      // Step 10: Get room statistics (admin)
      console.log('\n🔟 Getting room statistics...');
      try {
        const roomStats = await this.getRoomStatistics(this.testUser1);
        console.log('Room Statistics:');
        console.log(`  - Total rooms: ${roomStats.totalRooms}`);
        console.log(`  - Active rooms: ${roomStats.activeRooms}`);
        console.log(`  - Rooms by type:`, roomStats.roomsByType);
        console.log(`  - Rooms by access:`, roomStats.roomsByAccess);
      } catch (error) {
        console.log('ℹ️ Could not get room statistics (admin access may be required)');
      }

      // Step 11: Demonstrate room events
      console.log('\n1️⃣1️⃣ Setting up room event listeners...');
      
      homeRoom.onMessage('player_state_updated', (data) => {
        console.log(`Home room - Player ${data.sessionId} moved to (${data.x}, ${data.y})`);
      });

      gardenRoom.onMessage('player_state_updated', (data) => {
        console.log(`Garden room - Player ${data.sessionId} moved to (${data.x}, ${data.y})`);
      });

      // Wait a bit to see events
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Step 12: Cleanup
      console.log('\n1️⃣2️⃣ Cleaning up connections...');
      await homeRoom.leave();
      await gardenRoom.leave();
      
      console.log('✅ Disconnected from all rooms');

      console.log('\n🎉 Dynamic room system demo completed successfully!');
      console.log('\nKey features demonstrated:');
      console.log('- Dynamic room ID generation');
      console.log('- Room access permission checking');
      console.log('- Available rooms discovery');
      console.log('- Dynamic room connection');
      console.log('- Room state synchronization');
      console.log('- Public room listing');
      console.log('- Room ID parsing');
      console.log('- Room statistics');

    } catch (error) {
      console.error('❌ Demo failed:', error.response?.data || error.message);
    }
  }

  async authenticateUser(user) {
    try {
      // Try to login first
      const response = await axios.post(`${this.baseURL}/auth/login`, {
        username: user.username,
        password: user.password
      });
      
      user.token = response.data.token;
      user.uid = response.data.userId.toString();
    } catch (error) {
      if (error.response?.status === 401) {
        // User doesn't exist, try to register
        console.log(`User ${user.username} not found, registering...`);
        const registerResponse = await axios.post(`${this.baseURL}/auth/register`, {
          username: user.username,
          password: user.password
        });
        
        user.token = registerResponse.data.token;
        user.uid = registerResponse.data.userId.toString();
      } else {
        throw error;
      }
    }
  }

  async generateRoomId(roomType, ownerUid, user) {
    const response = await axios.get(`${this.baseURL}/api/rooms/${roomType}/generate-id/${ownerUid}`, {
      params: {
        token: user.token,
        uid: user.uid
      }
    });
    return response.data.data.roomId;
  }

  async checkRoomAccess(roomId, user) {
    try {
      const response = await axios.get(`${this.baseURL}/api/rooms/parse/${roomId}`, {
        params: {
          token: user.token,
          uid: user.uid
        }
      });
      return response.data.data.canAccess;
    } catch (error) {
      return false;
    }
  }

  async getAvailableRooms(roomType, user) {
    const response = await axios.get(`${this.baseURL}/api/rooms/${roomType}/available`, {
      params: {
        token: user.token,
        uid: user.uid
      }
    });
    return response.data.data;
  }

  async connectToRoom(roomName, options) {
    const room = await this.client.joinOrCreate(roomName, options);
    
    room.onError((code, message) => {
      console.error(`Room error ${code}: ${message}`);
    });

    room.onLeave((code) => {
      console.log(`Left room with code: ${code}`);
    });

    return room;
  }

  async getPublicRooms(roomType) {
    const response = await axios.get(`${this.baseURL}/api/rooms/${roomType}/public`);
    return response.data.data;
  }

  async parseRoomId(roomId, user) {
    const response = await axios.get(`${this.baseURL}/api/rooms/parse/${roomId}`, {
      params: {
        token: user.token,
        uid: user.uid
      }
    });
    return response.data.data;
  }

  async getRoomStatistics(user) {
    const response = await axios.get(`${this.baseURL}/api/rooms/admin/statistics`, {
      params: {
        token: user.token,
        uid: user.uid
      }
    });
    return response.data.data;
  }
}

// Check if required packages are available
try {
  require('axios');
  require('colyseus.js');
} catch (error) {
  console.error('❌ This example requires axios and colyseus.js. Please install them with:');
  console.error('npm install axios colyseus.js');
  process.exit(1);
}

// Run the example
const example = new DynamicRoomClientExample();
example.runExample().catch(error => {
  console.error('❌ Example failed:', error.message);
  process.exit(1);
});
