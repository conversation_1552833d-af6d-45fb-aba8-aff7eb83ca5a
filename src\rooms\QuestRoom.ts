import { Room, Client } from '@colyseus/core';
import { QuestRoomState } from './schema/QuestRoomState';
import { PlayerQuestState } from '../models/QuestState';
import QuestService from '../services/QuestService';
import { QuestError } from '../errors';

/**
 * Quest action message interface
 */
interface QuestActionMessage {
  action: 'start_quest' | 'update_progress' | 'complete_step' | 'get_npc_response';
  questId?: number;
  stepOrder?: number;
  npcId?: number;
  responseType?: 'finished' | 'unfinished';
  progressData?: any;
}

/**
 * Player movement message interface
 */
interface PlayerMovementMessage {
  x: number;
  y: number;
  dir?: string;
  animation?: string;
  isFlipped?: boolean;
  isSitting?: boolean;
}

/**
 * Room with integrated quest system
 */
export class QuestRoom extends Room<QuestRoomState> {
  maxClients = 10;
  state = new QuestRoomState();

  onCreate(options: any) {
    console.log('QuestRoom created with options:', options);

    // Handle quest actions
    this.onMessage('quest_action', async (client, message: QuestActionMessage) => {
      try {
        await this.handleQuestAction(client, message);
      } catch (error) {
        console.error('Quest action error:', error);
        if (error instanceof QuestError) {
          client.send('quest_error', error.toColyseusError());
        } else {
          client.send('quest_error', {
            code: 500,
            message: 'Internal quest system error'
          });
        }
      }
    });

    // Handle player movement
    this.onMessage('player_movement', (client, message: PlayerMovementMessage) => {
      try {
        this.handlePlayerMovement(client, message);
      } catch (error) {
        console.error('Player movement error:', error);
        client.send('movement_error', {
          code: 400,
          message: 'Invalid movement data'
        });
      }
    });

    // Handle player outfit update
    this.onMessage('update_outfit', (client, outfit: any) => {
      try {
        this.state.updatePlayerOutfit(client.sessionId, outfit);
      } catch (error) {
        console.error('Outfit update error:', error);
      }
    });

    // Handle NPC interaction
    this.onMessage('npc_interaction', async (client, message: { npcId: number; questId?: number; stepOrder?: number }) => {
      try {
        await this.handleNpcInteraction(client, message);
      } catch (error) {
        console.error('NPC interaction error:', error);
        if (error instanceof QuestError) {
          client.send('npc_interaction_error', error.toColyseusError());
        }
      }
    });

    // Set up periodic quest progress sync
    this.clock.setInterval(() => {
      this.syncQuestProgress();
    }, 30000); // Sync every 30 seconds
  }

  async onJoin(client: Client, options: any) {
    console.log(`${client.sessionId} joined QuestRoom`);

    // Validate required options
    if (!options.uid) {
      throw new Error('User ID is required to join quest room');
    }

    // Add player to room state
    const player = this.state.addPlayer(
      client.sessionId,
      options.uid,
      options.x || 0,
      options.y || 0
    );

    // Load player's quest progress from database
    try {
      await this.loadPlayerQuests(options.uid);
      
      // Send initial quest data to client
      const playerQuests = await QuestService.getPlayerQuests(options.uid);
      client.send('quest_data_loaded', {
        quests: playerQuests,
        availableQuests: QuestService.getAllQuests()
      });
    } catch (error) {
      console.error('Failed to load player quests:', error);
      client.send('quest_load_error', {
        code: 500,
        message: 'Failed to load quest data'
      });
    }

    // Send welcome message
    client.send('room_joined', {
      sessionId: client.sessionId,
      roomStats: this.state.getRoomStats()
    });
  }

  async onLeave(client: Client, consented: boolean) {
    console.log(`${client.sessionId} left QuestRoom (consented: ${consented})`);

    const player = this.state.getPlayer(client.sessionId);
    if (player) {
      // Save player's quest progress to database
      try {
        await this.savePlayerQuests(player.uid);
      } catch (error) {
        console.error('Failed to save player quests on leave:', error);
      }

      // Remove player from room state
      this.state.removePlayer(client.sessionId);
    }
  }

  onDispose() {
    console.log('QuestRoom disposing...');
  }

  /**
   * Handle quest-related actions
   */
  private async handleQuestAction(client: Client, message: QuestActionMessage) {
    const player = this.state.getPlayer(client.sessionId);
    if (!player) {
      throw new QuestError('Player not found in room');
    }

    switch (message.action) {
      case 'start_quest':
        if (!message.questId) {
          throw QuestError.invalidQuestData('Quest ID is required');
        }
        await this.startPlayerQuest(player.uid, message.questId);
        client.send('quest_started', { questId: message.questId });
        break;

      case 'update_progress':
        if (!message.questId || typeof message.stepOrder !== 'number') {
          throw QuestError.invalidQuestData('Quest ID and step order are required');
        }
        await this.updatePlayerQuestProgress(
          player.uid,
          message.questId,
          message.stepOrder,
          message.progressData || {}
        );
        client.send('quest_progress_updated', {
          questId: message.questId,
          stepOrder: message.stepOrder
        });
        break;

      case 'complete_step':
        if (!message.questId || typeof message.stepOrder !== 'number') {
          throw QuestError.invalidQuestData('Quest ID and step order are required');
        }
        await this.completeQuestStep(
          player.uid,
          message.questId,
          message.stepOrder,
          message.progressData || {}
        );
        client.send('quest_step_completed', {
          questId: message.questId,
          stepOrder: message.stepOrder
        });
        break;

      case 'get_npc_response':
        if (!message.questId || typeof message.stepOrder !== 'number' || !message.npcId || !message.responseType) {
          throw QuestError.invalidQuestData('All parameters are required for NPC response');
        }
        const response = QuestService.getNpcResponse(
          message.questId,
          message.stepOrder,
          message.npcId,
          message.responseType
        );
        client.send('npc_response', response);
        break;

      default:
        throw QuestError.invalidQuestAction(message.action, message.questId || 0);
    }
  }

  /**
   * Handle player movement
   */
  private handlePlayerMovement(client: Client, message: PlayerMovementMessage) {
    if (typeof message.x !== 'number' || typeof message.y !== 'number') {
      throw new Error('Invalid movement coordinates');
    }

    this.state.updatePlayerPosition(client.sessionId, message.x, message.y, message.dir);
    
    if (message.animation) {
      this.state.updatePlayerAnimation(client.sessionId, message.animation, message.isFlipped);
    }

    if (typeof message.isSitting === 'boolean') {
      this.state.updatePlayerSitting(client.sessionId, message.isSitting);
    }
  }

  /**
   * Handle NPC interaction
   */
  private async handleNpcInteraction(client: Client, message: { npcId: number; questId?: number; stepOrder?: number }) {
    const player = this.state.getPlayer(client.sessionId);
    if (!player) {
      throw new QuestError('Player not found in room');
    }

    // If quest-related interaction
    if (message.questId && typeof message.stepOrder === 'number') {
      const playerQuest = this.state.questSystem.getPlayerQuest(player.uid, message.questId);
      if (!playerQuest) {
        throw QuestError.questNotStarted(message.questId);
      }

      const responseType = playerQuest.isStepCompleted(message.stepOrder) ? 'finished' : 'unfinished';
      const response = QuestService.getNpcResponse(message.questId, message.stepOrder, message.npcId, responseType);
      
      client.send('npc_dialogue', {
        npcId: message.npcId,
        questId: message.questId,
        stepOrder: message.stepOrder,
        response: response
      });
    } else {
      // General NPC interaction
      client.send('npc_dialogue', {
        npcId: message.npcId,
        message: 'Hello! How can I help you today?'
      });
    }
  }

  /**
   * Load player quests from database into room state
   */
  private async loadPlayerQuests(playerId: string) {
    const playerQuests = await QuestService.getPlayerQuests(playerId);
    
    for (const quest of playerQuests) {
      const questState = new PlayerQuestState(quest.quest_id, quest.current_step_order);
      questState.isCompleted = quest.is_completed;
      questState.startedAt = quest.started_at.getTime();
      if (quest.completed_at) {
        questState.completedAt = quest.completed_at.getTime();
      }

      // Parse step progress
      if (quest.step_progress) {
        const stepProgress = typeof quest.step_progress === 'string' 
          ? JSON.parse(quest.step_progress) 
          : quest.step_progress;

        for (const [stepKey, progress] of Object.entries(stepProgress)) {
          const stepOrder = parseInt(stepKey.replace('step_', ''));
          if (!isNaN(stepOrder)) {
            questState.setStepProgress(stepOrder, (progress as any).isCompleted || false, progress);
          }
        }
      }

      this.state.questSystem.setPlayerQuest(playerId, questState);
    }
  }

  /**
   * Save player quests from room state to database
   */
  private async savePlayerQuests(playerId: string) {
    // This would typically be handled by the QuestService
    // For now, we'll just log that we should save
    console.log(`Should save quest progress for player ${playerId}`);
  }

  /**
   * Start a quest for a player
   */
  private async startPlayerQuest(playerId: string, questId: number) {
    const playerQuest = await QuestService.startQuest(playerId, questId);
    
    // Update room state
    const questState = new PlayerQuestState(questId, 0);
    questState.startedAt = playerQuest.started_at.getTime();
    this.state.questSystem.setPlayerQuest(playerId, questState);
  }

  /**
   * Update quest progress
   */
  private async updatePlayerQuestProgress(playerId: string, questId: number, stepOrder: number, progressData: any) {
    await QuestService.updateQuestProgress(playerId, questId, stepOrder, progressData, false);
    
    // Update room state
    const questState = this.state.questSystem.getPlayerQuest(playerId, questId);
    if (questState) {
      questState.setStepProgress(stepOrder, false, progressData);
    }
  }

  /**
   * Complete a quest step
   */
  private async completeQuestStep(playerId: string, questId: number, stepOrder: number, progressData: any) {
    const updatedQuest = await QuestService.updateQuestProgress(playerId, questId, stepOrder, progressData, true);
    
    // Update room state
    const questState = this.state.questSystem.getPlayerQuest(playerId, questId);
    if (questState) {
      questState.setStepProgress(stepOrder, true, progressData);
      questState.currentStepOrder = updatedQuest.current_step_order;
      
      if (updatedQuest.is_completed) {
        questState.complete();
      }
    }
  }

  /**
   * Sync quest progress periodically
   */
  private async syncQuestProgress() {
    // This could be used to periodically sync quest progress
    // For now, we'll just log
    console.log('Syncing quest progress for all players...');
  }
}
