/**
 * Real Inventory System Example
 * 
 * This example demonstrates the enhanced inventory system that replaces
 * the simulated inventory checks with real database operations.
 * 
 * Features demonstrated:
 * - Real inventory tracking with user_inventory table
 * - Item addition and removal with proper validation
 * - Inventory operation logging
 * - Item locking and equipment status
 * - Inventory history tracking
 * - Integration with ItemService and SpaceService
 * 
 * To run this example:
 * 1. Make sure the server is running (npm start)
 * 2. Make sure you have registered users in the database
 * 3. Run: node examples/real-inventory-system-example.js
 */

const axios = require('axios');

class RealInventorySystemExample {
  constructor() {
    this.baseURL = 'http://localhost:2567';
    
    // Test user credentials
    this.testUser = {
      username: 'inventoryuser1',
      password: 'password123',
      token: null,
      uid: null
    };
  }

  async runExample() {
    console.log('📦 Real Inventory System Example');
    console.log('================================\n');

    try {
      // Step 1: Authenticate user
      console.log('1️⃣ Authenticating test user...');
      await this.authenticateUser(this.testUser);
      console.log(`✅ User authenticated: ${this.testUser.uid} (${this.testUser.username})`);

      // Step 2: Get initial inventory
      console.log('\n2️⃣ Getting initial inventory...');
      let inventory = await this.getInventory();
      console.log(`Current inventory has ${inventory.length} items`);
      this.displayInventory(inventory);

      // Step 3: Add some test items
      console.log('\n3️⃣ Adding test items to inventory...');
      
      const testItems = [
        { itemId: 'basic_chair', quantity: 3, source: 'test_add' },
        { itemId: 'apple_seed', quantity: 10, source: 'test_add' },
        { itemId: 'wooden_table', quantity: 1, source: 'test_add' },
        { itemId: 'magic_potion', quantity: 5, source: 'test_add' }
      ];

      for (const item of testItems) {
        try {
          const result = await this.addItem(item.itemId, item.quantity, item.source);
          console.log(`✅ Added ${item.quantity}x ${item.itemId}: ${result.message}`);
        } catch (error) {
          console.log(`❌ Failed to add ${item.itemId}: ${error.response?.data?.error?.message || error.message}`);
        }
      }

      // Step 4: Get updated inventory
      console.log('\n4️⃣ Getting updated inventory...');
      inventory = await this.getInventory();
      console.log(`Updated inventory has ${inventory.length} items`);
      this.displayInventory(inventory);

      // Step 5: Test item usage (removal)
      console.log('\n5️⃣ Testing item usage...');
      
      if (inventory.length > 0) {
        const itemToUse = inventory.find(item => item.quantity > 1);
        if (itemToUse) {
          try {
            const result = await this.removeItem(itemToUse.item_id, 2, 'use', 'test_usage');
            console.log(`✅ Used 2x ${itemToUse.item_id}: ${result.message}`);
            console.log(`   Quantity before: ${result.quantityBefore}, after: ${result.quantityAfter}`);
          } catch (error) {
            console.log(`❌ Failed to use ${itemToUse.item_id}: ${error.response?.data?.error?.message || error.message}`);
          }
        }
      }

      // Step 6: Test item locking
      console.log('\n6️⃣ Testing item locking...');
      
      if (inventory.length > 0) {
        const itemToLock = inventory[0];
        try {
          const result = await this.updateItem(itemToLock.item_id, { isLocked: true });
          console.log(`✅ Locked item ${itemToLock.item_id}: ${result.message}`);
          
          // Try to remove locked item (should fail)
          try {
            await this.removeItem(itemToLock.item_id, 1, 'remove', 'test_locked_removal');
            console.log(`❌ Unexpectedly succeeded in removing locked item`);
          } catch (error) {
            console.log(`✅ Correctly prevented removal of locked item: ${error.response?.data?.error?.message || error.message}`);
          }
          
          // Unlock the item
          await this.updateItem(itemToLock.item_id, { isLocked: false });
          console.log(`✅ Unlocked item ${itemToLock.item_id}`);
        } catch (error) {
          console.log(`❌ Failed to lock/unlock item: ${error.response?.data?.error?.message || error.message}`);
        }
      }

      // Step 7: Test equipment status
      console.log('\n7️⃣ Testing equipment status...');
      
      if (inventory.length > 0) {
        const itemToEquip = inventory.find(item => item.item_type === 'clothing' || item.item_type === 'furniture');
        if (itemToEquip) {
          try {
            const result = await this.updateItem(itemToEquip.item_id, { isEquipped: true });
            console.log(`✅ Equipped item ${itemToEquip.item_id}: ${result.message}`);
            
            // Unequip the item
            await this.updateItem(itemToEquip.item_id, { isEquipped: false });
            console.log(`✅ Unequipped item ${itemToEquip.item_id}`);
          } catch (error) {
            console.log(`❌ Failed to equip/unequip item: ${error.response?.data?.error?.message || error.message}`);
          }
        }
      }

      // Step 8: Test custom data
      console.log('\n8️⃣ Testing custom data...');
      
      if (inventory.length > 0) {
        const itemForCustomData = inventory[0];
        const customData = {
          enchantment: 'fire_resistance',
          durability: 95,
          owner_notes: 'My favorite item'
        };
        
        try {
          const result = await this.updateItem(itemForCustomData.item_id, { customData });
          console.log(`✅ Added custom data to ${itemForCustomData.item_id}: ${result.message}`);
          console.log(`   Custom data:`, customData);
        } catch (error) {
          console.log(`❌ Failed to add custom data: ${error.response?.data?.error?.message || error.message}`);
        }
      }

      // Step 9: Test inventory filtering
      console.log('\n9️⃣ Testing inventory filtering...');
      
      // Get only new items
      const newItems = await this.getInventory({ isNew: true });
      console.log(`Found ${newItems.length} new items`);
      
      // Get only equipped items
      const equippedItems = await this.getInventory({ isEquipped: true });
      console.log(`Found ${equippedItems.length} equipped items`);
      
      // Get items sorted by name
      const sortedItems = await this.getInventory({ sortBy: 'name', sortOrder: 'ASC' });
      console.log(`Items sorted by name:`, sortedItems.map(item => item.item_name).slice(0, 5));

      // Step 10: Test space integration
      console.log('\n🔟 Testing space integration...');
      
      // Try to place an item in home space (this will use real inventory)
      const furnitureItem = inventory.find(item => item.item_type === 'furniture');
      if (furnitureItem) {
        try {
          const placeResult = await this.placeItemInHome(furnitureItem.item_id, 100, 100);
          console.log(`✅ Placed ${furnitureItem.item_id} in home: ${placeResult.message}`);
          
          // Remove the item from home (should return to inventory)
          if (placeResult.itemInstanceId) {
            const removeResult = await this.removeItemFromHome(placeResult.itemInstanceId);
            console.log(`✅ Removed item from home: ${removeResult.message}`);
          }
        } catch (error) {
          console.log(`❌ Failed space integration test: ${error.response?.data?.error?.message || error.message}`);
        }
      }

      // Step 11: Test farming integration
      console.log('\n1️⃣1️⃣ Testing farming integration...');
      
      // Try to plant a seed (this will use real inventory)
      const seedItem = inventory.find(item => item.item_id.includes('seed'));
      if (seedItem) {
        try {
          const plantResult = await this.plantSeed(seedItem.item_id, 0, 0);
          console.log(`✅ Planted ${seedItem.item_id}: ${plantResult.message}`);
        } catch (error) {
          console.log(`❌ Failed farming integration test: ${error.response?.data?.error?.message || error.message}`);
        }
      }

      // Step 12: Get inventory history
      console.log('\n1️⃣2️⃣ Getting inventory operation history...');
      
      const history = await this.getInventoryHistory({ limit: 10 });
      console.log(`Found ${history.length} recent operations:`);
      history.forEach((op, index) => {
        console.log(`  ${index + 1}. ${op.operation_type} ${op.quantity_change > 0 ? '+' : ''}${op.quantity_change} ${op.item_name} (${op.operation_source})`);
      });

      // Step 13: Mark items as seen
      console.log('\n1️⃣3️⃣ Marking new items as seen...');
      
      const currentInventory = await this.getInventory({ isNew: true });
      if (currentInventory.length > 0) {
        const itemsToMarkSeen = currentInventory.slice(0, 3); // Mark first 3 as seen
        for (const item of itemsToMarkSeen) {
          try {
            await this.updateItem(item.item_id, { isNew: false });
            console.log(`✅ Marked ${item.item_id} as seen`);
          } catch (error) {
            console.log(`❌ Failed to mark item as seen: ${error.response?.data?.error?.message || error.message}`);
          }
        }
      }

      // Step 14: Final inventory state
      console.log('\n1️⃣4️⃣ Final inventory state...');
      const finalInventory = await this.getInventory();
      console.log(`Final inventory has ${finalInventory.length} items`);
      this.displayInventory(finalInventory);

      console.log('\n🎉 Real Inventory System demo completed successfully!');
      console.log('\nKey features demonstrated:');
      console.log('- Real database inventory tracking');
      console.log('- Item addition and removal with validation');
      console.log('- Inventory operation logging and history');
      console.log('- Item locking and equipment status');
      console.log('- Custom data storage per item');
      console.log('- Inventory filtering and sorting');
      console.log('- Integration with SpaceService and farming');
      console.log('- Atomic operations with proper error handling');
      console.log('- Stack size validation and management');

    } catch (error) {
      console.error('❌ Demo failed:', error.response?.data || error.message);
    }
  }

  async authenticateUser(user) {
    try {
      // Try to login first
      const response = await axios.post(`${this.baseURL}/auth/login`, {
        username: user.username,
        password: user.password
      });
      
      user.token = response.data.token;
      user.uid = response.data.userId.toString();
    } catch (error) {
      if (error.response?.status === 401) {
        // User doesn't exist, try to register
        console.log(`User ${user.username} not found, registering...`);
        const registerResponse = await axios.post(`${this.baseURL}/auth/register`, {
          username: user.username,
          password: user.password
        });
        
        user.token = registerResponse.data.token;
        user.uid = registerResponse.data.userId.toString();
      } else {
        throw error;
      }
    }
  }

  async getInventory(options = {}) {
    try {
      const params = new URLSearchParams();
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });

      const response = await axios.get(`${this.baseURL}/api/inventory?${params}`, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`
        }
      });
      return response.data.data.inventory;
    } catch (error) {
      console.error('Failed to get inventory:', error.response?.data || error.message);
      return [];
    }
  }

  async addItem(itemId, quantity, source, customData) {
    const response = await axios.post(`${this.baseURL}/api/inventory/add`, {
      itemId,
      quantity,
      source,
      customData
    }, {
      headers: {
        'Authorization': `Bearer ${this.testUser.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data.data;
  }

  async removeItem(itemId, quantity, operationType, source) {
    const response = await axios.post(`${this.baseURL}/api/inventory/remove`, {
      itemId,
      quantity,
      operationType,
      source
    }, {
      headers: {
        'Authorization': `Bearer ${this.testUser.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data.data;
  }

  async updateItem(itemId, updates) {
    const response = await axios.put(`${this.baseURL}/api/inventory/item/${itemId}`, updates, {
      headers: {
        'Authorization': `Bearer ${this.testUser.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data.data;
  }

  async getInventoryHistory(options = {}) {
    try {
      const params = new URLSearchParams();
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });

      const response = await axios.get(`${this.baseURL}/api/inventory/history?${params}`, {
        headers: {
          'Authorization': `Bearer ${this.testUser.token}`
        }
      });
      return response.data.data.history;
    } catch (error) {
      console.error('Failed to get inventory history:', error.response?.data || error.message);
      return [];
    }
  }

  async placeItemInHome(itemId, x, y) {
    const response = await axios.post(`${this.baseURL}/api/spaces/home/<USER>
      itemTemplateId: itemId,
      x,
      y
    }, {
      headers: {
        'Authorization': `Bearer ${this.testUser.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }

  async removeItemFromHome(itemInstanceId) {
    const response = await axios.delete(`${this.baseURL}/api/spaces/home/<USER>/${itemInstanceId}`, {
      headers: {
        'Authorization': `Bearer ${this.testUser.token}`
      }
    });
    return response.data;
  }

  async plantSeed(seedItemId, x, y) {
    const response = await axios.post(`${this.baseURL}/api/spaces/home/<USER>/plant`, {
      seedItemId,
      x,
      y
    }, {
      headers: {
        'Authorization': `Bearer ${this.testUser.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }

  displayInventory(inventory) {
    if (inventory.length === 0) {
      console.log('  (empty)');
      return;
    }

    inventory.forEach((item, index) => {
      const status = [];
      if (item.is_new) status.push('NEW');
      if (item.is_equipped) status.push('EQUIPPED');
      if (item.is_locked) status.push('LOCKED');
      
      const statusStr = status.length > 0 ? ` [${status.join(', ')}]` : '';
      console.log(`  ${index + 1}. ${item.quantity}x ${item.item_name || item.item_id} (${item.item_type})${statusStr}`);
    });
  }

  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Check if required packages are available
try {
  require('axios');
} catch (error) {
  console.error('❌ This example requires axios. Please install it with:');
  console.error('npm install axios');
  process.exit(1);
}

// Run the example
const example = new RealInventorySystemExample();
example.runExample().catch(error => {
  console.error('❌ Example failed:', error.message);
  process.exit(1);
});
