/**
 * Shop System Client Example
 * 
 * This example demonstrates how to interact with the shop system
 * through HTTP API calls.
 * 
 * To run this example:
 * 1. Make sure the server is running (npm start)
 * 2. Make sure the shop system is set up (npm run setup-shop-system)
 * 3. Run: node examples/shop-client-example.js
 */

const axios = require('axios');

class ShopClientExample {
  constructor() {
    this.baseURL = 'http://localhost:2567/api/shop';
    this.testUserId = 'example_shop_user_' + Date.now();
  }

  async runExample() {
    console.log('🛍️ Shop System Client Example');
    console.log('==============================\n');

    try {
      // Step 1: Get shop categories
      console.log('1️⃣ Getting shop categories...');
      const categories = await this.getShopCategories();
      console.log(`Found ${categories.length} categories:`);
      categories.forEach(cat => {
        console.log(`  - ${cat.display_name} (${cat.name})`);
      });

      // Step 2: Get featured items
      console.log('\n2️⃣ Getting featured items...');
      const featuredItems = await this.getFeaturedItems();
      console.log(`Found ${featuredItems.length} featured items:`);
      featuredItems.slice(0, 3).forEach(item => {
        console.log(`  - ${item.name}: ${item.price_gold || 0} gold, ${item.price_diamonds || 0} diamonds`);
      });

      // Step 3: Get limited time items
      console.log('\n3️⃣ Getting limited time items...');
      const limitedItems = await this.getLimitedTimeItems();
      console.log(`Found ${limitedItems.length} limited time items:`);
      limitedItems.slice(0, 3).forEach(item => {
        console.log(`  - ${item.name}: Available until ${item.available_until || 'N/A'}`);
      });

      // Step 4: Search for items
      console.log('\n4️⃣ Searching for "dress" items...');
      const searchResults = await this.searchItems('dress');
      console.log(`Found ${searchResults.length} items matching "dress":`);
      searchResults.slice(0, 3).forEach(item => {
        console.log(`  - ${item.name}: ${item.description || 'No description'}`);
      });

      // Step 5: Get user currency (will initialize if not exists)
      console.log('\n5️⃣ Getting user currency...');
      const currency = await this.getUserCurrency();
      console.log(`User ${this.testUserId} has:`);
      console.log(`  - Gold coins: ${currency.gold_coins}`);
      console.log(`  - Diamonds: ${currency.diamonds}`);

      // Step 6: Get user inventory
      console.log('\n6️⃣ Getting user inventory...');
      const inventory = await this.getUserInventory();
      console.log(`User has ${inventory.length} items in inventory:`);
      inventory.slice(0, 5).forEach(item => {
        console.log(`  - ${item.item_name} x${item.quantity}`);
      });

      // Step 7: Add item to wishlist
      if (featuredItems.length > 0) {
        console.log('\n7️⃣ Adding item to wishlist...');
        const itemToWishlist = featuredItems[0];
        try {
          await this.addToWishlist(itemToWishlist.id);
          console.log(`✅ Added "${itemToWishlist.name}" to wishlist`);
        } catch (error) {
          console.log(`ℹ️ ${error.response?.data?.error?.message || error.message}`);
        }
      }

      // Step 8: Get user wishlist
      console.log('\n8️⃣ Getting user wishlist...');
      const wishlist = await this.getUserWishlist();
      console.log(`User has ${wishlist.length} items in wishlist:`);
      wishlist.forEach(item => {
        console.log(`  - ${item.shop_item_name}: ${item.price_gold || 0} gold, ${item.price_diamonds || 0} diamonds`);
      });

      // Step 9: Check if user can purchase an item
      if (featuredItems.length > 0) {
        console.log('\n9️⃣ Checking purchase eligibility...');
        const itemToPurchase = featuredItems[0];
        const eligibility = await this.checkPurchaseEligibility(itemToPurchase.id);
        console.log(`Can purchase "${itemToPurchase.name}": ${eligibility.canPurchase}`);
        if (!eligibility.canPurchase) {
          console.log(`  Reason: ${eligibility.reason}`);
          if (eligibility.requiredGold) {
            console.log(`  Required gold: ${eligibility.requiredGold}`);
          }
          if (eligibility.requiredDiamonds) {
            console.log(`  Required diamonds: ${eligibility.requiredDiamonds}`);
          }
        }
      }

      // Step 10: Attempt to purchase an affordable item
      console.log('\n🔟 Attempting to purchase an affordable item...');
      const affordableItem = featuredItems.find(item => 
        (item.price_gold || 0) <= 1000 && (item.price_diamonds || 0) <= 50
      );

      if (affordableItem) {
        try {
          const purchaseResult = await this.purchaseItem(affordableItem.id, 1);
          console.log(`✅ Successfully purchased "${affordableItem.name}"!`);
          console.log(`  Transaction ID: ${purchaseResult.transactionId}`);
          console.log(`  Currency spent: ${purchaseResult.currencySpent.gold} gold, ${purchaseResult.currencySpent.diamonds} diamonds`);
          console.log(`  Remaining currency: ${purchaseResult.remainingCurrency.gold} gold, ${purchaseResult.remainingCurrency.diamonds} diamonds`);
        } catch (error) {
          console.log(`❌ Purchase failed: ${error.response?.data?.error?.message || error.message}`);
        }
      } else {
        console.log('ℹ️ No affordable items found for purchase demo');
      }

      // Step 11: Get purchase history
      console.log('\n1️⃣1️⃣ Getting purchase history...');
      const purchaseHistory = await this.getPurchaseHistory();
      console.log(`User has ${purchaseHistory.length} transactions in history:`);
      purchaseHistory.slice(0, 3).forEach(transaction => {
        console.log(`  - ${transaction.item_name} x${transaction.quantity} (${transaction.transaction_type})`);
      });

      // Step 12: Get inventory and wishlist statistics
      console.log('\n1️⃣2️⃣ Getting statistics...');
      const inventoryStats = await this.getInventoryStats();
      const wishlistStats = await this.getWishlistStats();
      
      console.log('Inventory Statistics:');
      console.log(`  - Total items: ${inventoryStats.totalItems}`);
      console.log(`  - Unique items: ${inventoryStats.totalUniqueItems}`);
      
      console.log('Wishlist Statistics:');
      console.log(`  - Total items: ${wishlistStats.totalItems}`);
      console.log(`  - Available items: ${wishlistStats.availableItems}`);
      console.log(`  - Limited time items: ${wishlistStats.limitedTimeItems}`);

      console.log('\n🎉 Shop system demo completed successfully!');

    } catch (error) {
      console.error('❌ Demo failed:', error.response?.data || error.message);
    }
  }

  async getShopCategories() {
    const response = await axios.get(`${this.baseURL}/categories`);
    return response.data.data;
  }

  async getFeaturedItems() {
    const response = await axios.get(`${this.baseURL}/featured`);
    return response.data.data;
  }

  async getLimitedTimeItems() {
    const response = await axios.get(`${this.baseURL}/limited-time`);
    return response.data.data;
  }

  async searchItems(query) {
    const response = await axios.get(`${this.baseURL}/search?q=${encodeURIComponent(query)}`);
    return response.data.data;
  }

  async getUserCurrency() {
    const response = await axios.get(`${this.baseURL}/users/${this.testUserId}/currency`);
    return response.data.data;
  }

  async getUserInventory() {
    const response = await axios.get(`${this.baseURL}/users/${this.testUserId}/inventory`);
    return response.data.data;
  }

  async getInventoryStats() {
    const response = await axios.get(`${this.baseURL}/users/${this.testUserId}/inventory/stats`);
    return response.data.data;
  }

  async addToWishlist(shopItemId) {
    const response = await axios.post(`${this.baseURL}/users/${this.testUserId}/wishlist`, {
      shopItemId
    });
    return response.data.data;
  }

  async getUserWishlist() {
    const response = await axios.get(`${this.baseURL}/users/${this.testUserId}/wishlist`);
    return response.data.data;
  }

  async getWishlistStats() {
    const response = await axios.get(`${this.baseURL}/users/${this.testUserId}/wishlist/stats`);
    return response.data.data;
  }

  async checkPurchaseEligibility(shopItemId, quantity = 1) {
    const response = await axios.get(`${this.baseURL}/items/${shopItemId}/can-purchase/${this.testUserId}?quantity=${quantity}`);
    return response.data.data;
  }

  async purchaseItem(shopItemId, quantity = 1) {
    const response = await axios.post(`${this.baseURL}/purchase`, {
      userId: this.testUserId,
      shopItemId,
      quantity
    });
    return response.data.data;
  }

  async getPurchaseHistory() {
    const response = await axios.get(`${this.baseURL}/users/${this.testUserId}/purchases`);
    return response.data.data;
  }
}

// Check if axios is available
try {
  require('axios');
} catch (error) {
  console.error('❌ This example requires axios. Please install it with: npm install axios');
  process.exit(1);
}

// Run the example
const example = new ShopClientExample();
example.runExample().catch(error => {
  console.error('❌ Example failed:', error.message);
  process.exit(1);
});
