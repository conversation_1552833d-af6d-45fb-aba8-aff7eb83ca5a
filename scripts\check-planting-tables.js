const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkPlantingTables() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🔍 Checking Planting System Tables...');
    
    // Check if tables exist
    const [tables] = await connection.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME LIKE '%garden%' OR TABLE_NAME LIKE '%plant%' OR TABLE_NAME LIKE '%flower%'
    `, [process.env.DB_NAME]);
    
    console.log('📋 Found tables:', tables.map(t => t.TABLE_NAME));
    
    // Check garden_plots table structure
    try {
      const [columns] = await connection.query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'garden_plots'
        ORDER BY ORDINAL_POSITION
      `, [process.env.DB_NAME]);
      
      console.log('\n🌱 garden_plots table structure:');
      columns.forEach(col => {
        console.log(`  - ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'})`);
      });
    } catch (error) {
      console.log('❌ garden_plots table does not exist or has issues');
    }
    
    // Check planted_flowers table structure
    try {
      const [columns2] = await connection.query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'planted_flowers'
        ORDER BY ORDINAL_POSITION
      `, [process.env.DB_NAME]);
      
      console.log('\n🌸 planted_flowers table structure:');
      columns2.forEach(col => {
        console.log(`  - ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'})`);
      });
    } catch (error) {
      console.log('❌ planted_flowers table does not exist or has issues');
    }
    
    // Check flower_seeds table
    try {
      const [seedCount] = await connection.query('SELECT COUNT(*) as count FROM flower_seeds');
      console.log(`\n🌺 flower_seeds table has ${seedCount[0].count} seeds`);
    } catch (error) {
      console.log('❌ flower_seeds table does not exist or has issues');
    }
    
  } catch (error) {
    console.error('❌ Error checking planting tables:', error);
  } finally {
    await connection.end();
  }
}

checkPlantingTables();
