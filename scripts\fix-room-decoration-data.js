const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixRoomDecorationData() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🔧 Fixing Room Decoration System Data...');

    // Step 1: Add basic furniture items
    console.log('📦 Adding furniture items...');
    const furnitureItems = [
      ['basic_chair', 'Basic Chair', 'A simple wooden chair for your home', 'furniture', 'seating', 100, '{"category":"furniture","subcategory":"seating","placement":{"width":1,"height":1}}'],
      ['wooden_table', 'Wooden Table', 'A sturdy wooden table', 'furniture', 'table', 200, '{"category":"furniture","subcategory":"table","placement":{"width":2,"height":1}}'],
      ['flower_pot', 'Flower Pot', 'A decorative flower pot', 'furniture', 'decoration', 50, '{"category":"furniture","subcategory":"decoration","placement":{"width":1,"height":1}}'],
      ['modern_sofa', 'Modern Sofa', 'A comfortable modern sofa', 'furniture', 'seating', 500, '{"category":"furniture","subcategory":"seating","placement":{"width":3,"height":1}}'],
      ['bookshelf', 'Bookshelf', 'A tall bookshelf for storing books', 'furniture', 'storage', 300, '{"category":"furniture","subcategory":"storage","placement":{"width":1,"height":2}}'],
      ['coffee_table', 'Coffee Table', 'A low coffee table', 'furniture', 'table', 150, '{"category":"furniture","subcategory":"table","placement":{"width":2,"height":1}}'],
      ['plant_large', 'Large Plant', 'A large decorative plant', 'furniture', 'decoration', 80, '{"category":"furniture","subcategory":"decoration","placement":{"width":1,"height":1}}'],
      ['desk_lamp', 'Desk Lamp', 'A modern desk lamp', 'furniture', 'lighting', 75, '{"category":"furniture","subcategory":"lighting","placement":{"width":1,"height":1}}'],
      ['wardrobe', 'Wardrobe', 'A large wardrobe for clothes', 'furniture', 'storage', 400, '{"category":"furniture","subcategory":"storage","placement":{"width":2,"height":2}}'],
      ['dining_chair', 'Dining Chair', 'A comfortable dining chair', 'furniture', 'seating', 120, '{"category":"furniture","subcategory":"seating","placement":{"width":1,"height":1}}']
    ];

    for (const item of furnitureItems) {
      try {
        await connection.query(
          `INSERT IGNORE INTO items (item_id, item_name, description, item_type, category, price, data, created_at, updated_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
          item
        );
        console.log(`✅ Added furniture: ${item[1]}`);
      } catch (error) {
        console.log(`⚠️  Furniture ${item[1]} already exists or error: ${error.message}`);
      }
    }

    // Step 2: Add background items (need to add to items table first)
    console.log('🖼️  Adding background items...');

    // First, let's check if we need to add a background item type to the enum
    try {
      await connection.query(`
        ALTER TABLE items MODIFY COLUMN item_type
        ENUM('furniture', 'plant', 'seed', 'tool', 'wearable', 'consumable', 'collectible', 'background')
        NOT NULL COMMENT 'Broad category of the item'
      `);
      console.log('✅ Updated item_type enum to include background');
    } catch (error) {
      console.log('⚠️  item_type enum update error (may already include background):', error.message);
    }

    const backgroundItems = [
      ['default_home_bg', 'Default Home Background', 'The default home background', 'furniture', 'background', 0, '{"allowed_scenes":["home"],"type":"background"}'],
      ['modern_home_bg', 'Modern Home Background', 'A sleek modern home interior', 'furniture', 'background', 500, '{"allowed_scenes":["home"],"type":"background"}'],
      ['cozy_home_bg', 'Cozy Home Background', 'A warm and cozy home interior', 'furniture', 'background', 300, '{"allowed_scenes":["home"],"type":"background"}'],
      ['luxury_home_bg', 'Luxury Home Background', 'An elegant luxury home interior', 'furniture', 'background', 1000, '{"allowed_scenes":["home"],"type":"background"}'],
      ['default_garden_bg', 'Default Garden Background', 'The default garden background', 'furniture', 'background', 0, '{"allowed_scenes":["garden"],"type":"background"}'],
      ['garden_spring_bg', 'Spring Garden Background', 'A beautiful spring garden scene', 'furniture', 'background', 400, '{"allowed_scenes":["garden"],"type":"background"}'],
      ['garden_summer_bg', 'Summer Garden Background', 'A vibrant summer garden scene', 'furniture', 'background', 400, '{"allowed_scenes":["garden"],"type":"background"}'],
      ['garden_autumn_bg', 'Autumn Garden Background', 'A colorful autumn garden scene', 'furniture', 'background', 400, '{"allowed_scenes":["garden"],"type":"background"}'],
      ['garden_winter_bg', 'Winter Garden Background', 'A peaceful winter garden scene', 'furniture', 'background', 400, '{"allowed_scenes":["garden"],"type":"background"}']
    ];

    for (const item of backgroundItems) {
      try {
        await connection.query(
          `INSERT IGNORE INTO items (item_id, item_name, description, item_type, category, price, data, created_at, updated_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
          item
        );
        console.log(`✅ Added background: ${item[1]}`);
      } catch (error) {
        console.log(`⚠️  Background ${item[1]} already exists or error: ${error.message}`);
      }
    }

    // Step 3: Fix foreign key constraints
    console.log('🔗 Fixing foreign key constraints...');
    try {
      // Drop existing foreign key constraints
      await connection.query('ALTER TABLE user_private_spaces DROP FOREIGN KEY IF EXISTS user_private_spaces_ibfk_2');
      await connection.query('ALTER TABLE user_private_spaces DROP FOREIGN KEY IF EXISTS user_private_spaces_ibfk_3');
      console.log('✅ Dropped old foreign key constraints');
    } catch (error) {
      console.log('⚠️  Foreign key constraints may not exist:', error.message);
    }

    try {
      // Allow NULL values for background IDs
      await connection.query('ALTER TABLE user_private_spaces MODIFY COLUMN home_background_id VARCHAR(50) NULL');
      await connection.query('ALTER TABLE user_private_spaces MODIFY COLUMN garden_background_id VARCHAR(50) NULL');
      console.log('✅ Modified columns to allow NULL');
    } catch (error) {
      console.log('⚠️  Column modification error:', error.message);
    }

    try {
      // Add new foreign key constraints with proper references
      await connection.query(`
        ALTER TABLE user_private_spaces
        ADD CONSTRAINT user_private_spaces_home_bg_fk
        FOREIGN KEY (home_background_id) REFERENCES items(item_id) ON DELETE SET NULL
      `);
      await connection.query(`
        ALTER TABLE user_private_spaces
        ADD CONSTRAINT user_private_spaces_garden_bg_fk
        FOREIGN KEY (garden_background_id) REFERENCES items(item_id) ON DELETE SET NULL
      `);
      console.log('✅ Added new foreign key constraints');
    } catch (error) {
      console.log('⚠️  Foreign key constraint error:', error.message);
    }

    // Step 4: Ensure test user exists
    console.log('👤 Ensuring test user exists...');
    try {
      await connection.query(
        `INSERT IGNORE INTO users (id, username, password_hash, created_at, updated_at)
         VALUES (2, 'test_user', 'dummy_hash', NOW(), NOW())`
      );
      console.log('✅ Test user ensured');
    } catch (error) {
      console.log('⚠️  Test user error:', error.message);
    }

    // Step 5: Add starter furniture to test user's inventory
    console.log('🎒 Adding starter furniture to test user inventory...');
    const starterItems = [
      [2, 'basic_chair', 5],
      [2, 'wooden_table', 3],
      [2, 'flower_pot', 10],
      [2, 'coffee_table', 2],
      [2, 'plant_large', 3],
      [2, 'desk_lamp', 2]
    ];

    for (const item of starterItems) {
      try {
        await connection.query(
          `INSERT IGNORE INTO user_inventory (user_id, item_id, quantity, acquired_at, updated_at)
           VALUES (?, ?, ?, NOW(), NOW())`,
          item
        );
        console.log(`✅ Added ${item[2]}x ${item[1]} to test user inventory`);
      } catch (error) {
        console.log(`⚠️  Inventory item ${item[1]} error: ${error.message}`);
      }
    }

    // Step 6: Create default space settings for test user
    console.log('🏠 Creating default space settings...');
    try {
      await connection.query(`
        INSERT IGNORE INTO user_private_spaces (
          owner_user_id,
          home_background_id,
          home_access_level,
          garden_background_id,
          garden_access_level,
          updated_at
        ) VALUES (
          2,
          'default_home_bg',
          'friends_only',
          'default_garden_bg',
          'private',
          NOW()
        )
      `);
      console.log('✅ Created default space settings for test user');
    } catch (error) {
      console.log('⚠️  Space settings error:', error.message);
    }

    // Step 7: Add some sample home items for testing
    console.log('🪑 Adding sample home items...');
    try {
      // First, clear any existing home items for test user
      await connection.query('DELETE FROM home_items WHERE owner_user_id = 2');

      const sampleItems = [
        ['basic_chair', 100, 100, 0, false],
        ['wooden_table', 200, 150, 0, false],
        ['flower_pot', 50, 50, 0, false]
      ];

      for (const item of sampleItems) {
        const itemInstanceId = `test_${item[0]}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await connection.query(`
          INSERT INTO home_items (
            item_instance_id,
            owner_user_id,
            item_template_id,
            pos_x,
            pos_y,
            rotation,
            is_flipped,
            placed_at
          ) VALUES (?, 2, ?, ?, ?, ?, ?, NOW())
        `, [itemInstanceId, ...item]);
        console.log(`✅ Added sample home item: ${item[0]} at (${item[1]}, ${item[2]})`);
      }
    } catch (error) {
      console.log('⚠️  Sample home items error:', error.message);
    }

    console.log('\n🎉 Room decoration system data has been successfully fixed!');
    console.log('\n📊 Summary:');

    // Check final status
    const [furnitureCount] = await connection.query("SELECT COUNT(*) as count FROM items WHERE item_type = 'furniture'");
    const [backgroundCount] = await connection.query("SELECT COUNT(*) as count FROM items WHERE item_type = 'background'");
    const [inventoryCount] = await connection.query("SELECT COUNT(*) as count FROM user_inventory WHERE user_id = 2");
    const [homeItemsCount] = await connection.query("SELECT COUNT(*) as count FROM home_items WHERE owner_user_id = 2");
    const [spaceSettingsCount] = await connection.query("SELECT COUNT(*) as count FROM user_private_spaces WHERE owner_user_id = 2");

    console.log(`✅ Furniture items: ${furnitureCount[0].count}`);
    console.log(`✅ Background items: ${backgroundCount[0].count}`);
    console.log(`✅ Test user inventory items: ${inventoryCount[0].count}`);
    console.log(`✅ Test user home items: ${homeItemsCount[0].count}`);
    console.log(`✅ Test user space settings: ${spaceSettingsCount[0].count}`);

  } catch (error) {
    console.error('❌ Error fixing room decoration data:', error);
  } finally {
    await connection.end();
  }
}

fixRoomDecorationData();
