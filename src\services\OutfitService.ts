import pool from '../utils/db';
import { ResultSetHeader, RowDataPacket } from 'mysql2';
import { OutfitState, OutfitItemState } from '../models/OutfitState';

/**
 * Outfit template interface for database
 */
export interface OutfitTemplate extends RowDataPacket {
  id: string;
  name: string;
  description: string;
  category: string;
  rarity: string;
  is_default: boolean;
  is_purchasable: boolean;
  price_gold: number;
  price_diamonds: number;
  unlock_level: number;
  outfit_data: string; // JSON string
  preview_image: string;
  created_at: Date;
}

/**
 * User outfit save interface
 */
export interface UserOutfitSave extends RowDataPacket {
  id: string;
  user_id: string;
  outfit_name: string;
  outfit_data: string; // JSON string
  is_current: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Service for managing player outfits and outfit templates
 */
class OutfitService {
  private static instance: OutfitService;

  private constructor() {}

  public static getInstance(): OutfitService {
    if (!OutfitService.instance) {
      OutfitService.instance = new OutfitService();
    }
    return OutfitService.instance;
  }

  /**
   * Load user's current outfit from database
   */
  async loadUserCurrentOutfit(userId: string): Promise<any | null> {
    try {
      const [rows] = await pool.query<UserOutfitSave[]>(
        'SELECT * FROM user_outfits WHERE user_id = ? AND is_current = TRUE LIMIT 1',
        [userId]
      );

      if (rows.length > 0) {
        return JSON.parse(rows[0].outfit_data);
      }

      // If no current outfit, return default
      return this.getDefaultOutfitData();
    } catch (error) {
      console.error('Error loading user current outfit:', error);
      return this.getDefaultOutfitData();
    }
  }

  /**
   * Save user's current outfit to database
   */
  async saveUserCurrentOutfit(userId: string, outfitData: any, outfitName: string = 'Current Outfit'): Promise<boolean> {
    try {
      // First, unset any existing current outfit
      await pool.query(
        'UPDATE user_outfits SET is_current = FALSE WHERE user_id = ?',
        [userId]
      );

      // Insert or update the current outfit
      const outfitId = `outfit_${userId}_${Date.now()}`;
      const [result] = await pool.query<ResultSetHeader>(
        `INSERT INTO user_outfits (id, user_id, outfit_name, outfit_data, is_current)
         VALUES (?, ?, ?, ?, TRUE)
         ON DUPLICATE KEY UPDATE 
         outfit_data = VALUES(outfit_data),
         outfit_name = VALUES(outfit_name),
         is_current = TRUE,
         updated_at = NOW()`,
        [outfitId, userId, outfitName, JSON.stringify(outfitData)]
      );

      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error saving user current outfit:', error);
      return false;
    }
  }

  /**
   * Save a named outfit for the user
   */
  async saveUserOutfit(userId: string, outfitName: string, outfitData: any, setCurrent: boolean = false): Promise<{ success: boolean; outfitId?: string; message?: string }> {
    try {
      // Check if outfit name already exists for this user
      const [existingRows] = await pool.query<UserOutfitSave[]>(
        'SELECT id FROM user_outfits WHERE user_id = ? AND outfit_name = ?',
        [userId, outfitName]
      );

      if (existingRows.length > 0) {
        return {
          success: false,
          message: 'An outfit with this name already exists'
        };
      }

      const outfitId = `outfit_${userId}_${Date.now()}`;

      // If setting as current, unset other current outfits
      if (setCurrent) {
        await pool.query(
          'UPDATE user_outfits SET is_current = FALSE WHERE user_id = ?',
          [userId]
        );
      }

      const [result] = await pool.query<ResultSetHeader>(
        'INSERT INTO user_outfits (id, user_id, outfit_name, outfit_data, is_current) VALUES (?, ?, ?, ?, ?)',
        [outfitId, userId, outfitName, JSON.stringify(outfitData), setCurrent]
      );

      if (result.affectedRows > 0) {
        return {
          success: true,
          outfitId,
          message: 'Outfit saved successfully'
        };
      } else {
        return {
          success: false,
          message: 'Failed to save outfit'
        };
      }
    } catch (error) {
      console.error('Error saving user outfit:', error);
      return {
        success: false,
        message: 'Database error occurred'
      };
    }
  }

  /**
   * Load a specific user outfit
   */
  async loadUserOutfit(userId: string, outfitId: string): Promise<any | null> {
    try {
      const [rows] = await pool.query<UserOutfitSave[]>(
        'SELECT * FROM user_outfits WHERE user_id = ? AND id = ?',
        [userId, outfitId]
      );

      if (rows.length > 0) {
        return JSON.parse(rows[0].outfit_data);
      }

      return null;
    } catch (error) {
      console.error('Error loading user outfit:', error);
      return null;
    }
  }

  /**
   * Get all saved outfits for a user
   */
  async getUserOutfits(userId: string): Promise<UserOutfitSave[]> {
    try {
      const [rows] = await pool.query<UserOutfitSave[]>(
        'SELECT * FROM user_outfits WHERE user_id = ? ORDER BY updated_at DESC',
        [userId]
      );

      return rows;
    } catch (error) {
      console.error('Error getting user outfits:', error);
      return [];
    }
  }

  /**
   * Delete a user outfit
   */
  async deleteUserOutfit(userId: string, outfitId: string): Promise<{ success: boolean; message?: string }> {
    try {
      // Check if this is the current outfit
      const [currentRows] = await pool.query<UserOutfitSave[]>(
        'SELECT is_current FROM user_outfits WHERE user_id = ? AND id = ?',
        [userId, outfitId]
      );

      if (currentRows.length === 0) {
        return {
          success: false,
          message: 'Outfit not found'
        };
      }

      if (currentRows[0].is_current) {
        return {
          success: false,
          message: 'Cannot delete the current outfit'
        };
      }

      const [result] = await pool.query<ResultSetHeader>(
        'DELETE FROM user_outfits WHERE user_id = ? AND id = ?',
        [userId, outfitId]
      );

      if (result.affectedRows > 0) {
        return {
          success: true,
          message: 'Outfit deleted successfully'
        };
      } else {
        return {
          success: false,
          message: 'Failed to delete outfit'
        };
      }
    } catch (error) {
      console.error('Error deleting user outfit:', error);
      return {
        success: false,
        message: 'Database error occurred'
      };
    }
  }

  /**
   * Set an outfit as current
   */
  async setCurrentOutfit(userId: string, outfitId: string): Promise<{ success: boolean; message?: string }> {
    try {
      // First, unset all current outfits for this user
      await pool.query(
        'UPDATE user_outfits SET is_current = FALSE WHERE user_id = ?',
        [userId]
      );

      // Set the specified outfit as current
      const [result] = await pool.query<ResultSetHeader>(
        'UPDATE user_outfits SET is_current = TRUE WHERE user_id = ? AND id = ?',
        [userId, outfitId]
      );

      if (result.affectedRows > 0) {
        return {
          success: true,
          message: 'Current outfit updated successfully'
        };
      } else {
        return {
          success: false,
          message: 'Outfit not found'
        };
      }
    } catch (error) {
      console.error('Error setting current outfit:', error);
      return {
        success: false,
        message: 'Database error occurred'
      };
    }
  }

  /**
   * Get outfit templates (predefined outfits)
   */
  async getOutfitTemplates(category?: string, rarity?: string): Promise<OutfitTemplate[]> {
    try {
      let query = 'SELECT * FROM outfit_templates WHERE 1=1';
      const params: any[] = [];

      if (category) {
        query += ' AND category = ?';
        params.push(category);
      }

      if (rarity) {
        query += ' AND rarity = ?';
        params.push(rarity);
      }

      query += ' ORDER BY rarity, name';

      const [rows] = await pool.query<OutfitTemplate[]>(query, params);
      return rows;
    } catch (error) {
      console.error('Error getting outfit templates:', error);
      return [];
    }
  }

  /**
   * Get a specific outfit template
   */
  async getOutfitTemplate(templateId: string): Promise<OutfitTemplate | null> {
    try {
      const [rows] = await pool.query<OutfitTemplate[]>(
        'SELECT * FROM outfit_templates WHERE id = ?',
        [templateId]
      );

      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('Error getting outfit template:', error);
      return null;
    }
  }

  /**
   * Apply an outfit template to a user
   */
  async applyOutfitTemplate(userId: string, templateId: string): Promise<{ success: boolean; outfitData?: any; message?: string }> {
    try {
      const template = await this.getOutfitTemplate(templateId);
      if (!template) {
        return {
          success: false,
          message: 'Outfit template not found'
        };
      }

      const outfitData = JSON.parse(template.outfit_data);
      
      // Save as current outfit
      const saved = await this.saveUserCurrentOutfit(userId, outfitData, template.name);
      
      if (saved) {
        return {
          success: true,
          outfitData,
          message: 'Outfit template applied successfully'
        };
      } else {
        return {
          success: false,
          message: 'Failed to apply outfit template'
        };
      }
    } catch (error) {
      console.error('Error applying outfit template:', error);
      return {
        success: false,
        message: 'Error applying outfit template'
      };
    }
  }

  /**
   * Get default outfit data
   */
  private getDefaultOutfitData(): any {
    return {
      outfitId: 'default_outfit',
      outfitName: 'Default Outfit',
      isCustom: false,
      lastUpdated: Date.now(),
      items: {
        body: {
          itemId: 'body_default',
          itemTemplateId: 'body_default',
          category: 'body',
          subCategory: 'base',
          color: '#FFDBAC',
          layer: 0,
          isVisible: true,
          customData: {}
        },
        hair: {
          itemId: 'hair_default',
          itemTemplateId: 'hair_default',
          category: 'hair',
          subCategory: 'base',
          color: '#8B4513',
          layer: 10,
          isVisible: true,
          customData: {}
        },
        top: {
          itemId: 'top_default',
          itemTemplateId: 'top_default',
          category: 'clothing',
          subCategory: 'top',
          color: '#4169E1',
          layer: 20,
          isVisible: true,
          customData: {}
        },
        bottom: {
          itemId: 'bottom_default',
          itemTemplateId: 'bottom_default',
          category: 'clothing',
          subCategory: 'bottom',
          color: '#2F4F4F',
          layer: 15,
          isVisible: true,
          customData: {}
        },
        shoes: {
          itemId: 'shoes_default',
          itemTemplateId: 'shoes_default',
          category: 'clothing',
          subCategory: 'shoes',
          color: '#8B4513',
          layer: 5,
          isVisible: true,
          customData: {}
        }
      }
    };
  }

  /**
   * Validate outfit data
   */
  validateOutfitData(outfitData: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!outfitData || typeof outfitData !== 'object') {
      errors.push('Invalid outfit data format');
      return { isValid: false, errors };
    }

    if (!outfitData.items || typeof outfitData.items !== 'object') {
      errors.push('Outfit items are required');
      return { isValid: false, errors };
    }

    // Check for required body item
    if (!outfitData.items.body) {
      errors.push('Body item is required');
    }

    // Validate each item
    Object.entries(outfitData.items).forEach(([slot, item]: [string, any]) => {
      if (!item.itemTemplateId) {
        errors.push(`Item template ID is required for slot: ${slot}`);
      }
      if (!item.category) {
        errors.push(`Category is required for slot: ${slot}`);
      }
      if (typeof item.layer !== 'number') {
        errors.push(`Layer must be a number for slot: ${slot}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default OutfitService.getInstance();
