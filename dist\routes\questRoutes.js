"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const QuestService_1 = __importDefault(require("../services/QuestService"));
const errors_1 = require("../errors");
const router = (0, express_1.Router)();
/**
 * Get all available quests
 */
router.get('/quests', async (req, res) => {
    try {
        const quests = QuestService_1.default.getAllQuests();
        res.json({
            success: true,
            data: quests
        });
    }
    catch (error) {
        console.error('Error fetching quests:', error);
        if (error instanceof errors_1.QuestError) {
            return res.status(error.httpCode).json(error.toJSON());
        }
        res.status(500).json({
            success: false,
            error: {
                message: 'Internal server error'
            }
        });
    }
});
/**
 * Get specific quest by ID
 */
router.get('/quests/:questId', async (req, res) => {
    try {
        const questId = parseInt(req.params.questId);
        if (isNaN(questId)) {
            throw errors_1.QuestError.invalidQuestData('Invalid quest ID');
        }
        const quest = QuestService_1.default.getQuest(questId);
        if (!quest) {
            throw errors_1.QuestError.questNotFound(questId);
        }
        const steps = QuestService_1.default.getQuestSteps(questId);
        const responses = QuestService_1.default.getQuestStepResponses(questId);
        res.json({
            success: true,
            data: {
                quest,
                steps,
                responses
            }
        });
    }
    catch (error) {
        console.error('Error fetching quest:', error);
        if (error instanceof errors_1.QuestError) {
            return res.status(error.httpCode).json(error.toJSON());
        }
        res.status(500).json({
            success: false,
            error: {
                message: 'Internal server error'
            }
        });
    }
});
/**
 * Get quest steps for a specific quest
 */
router.get('/quests/:questId/steps', async (req, res) => {
    try {
        const questId = parseInt(req.params.questId);
        if (isNaN(questId)) {
            throw errors_1.QuestError.invalidQuestData('Invalid quest ID');
        }
        const quest = QuestService_1.default.getQuest(questId);
        if (!quest) {
            throw errors_1.QuestError.questNotFound(questId);
        }
        const steps = QuestService_1.default.getQuestSteps(questId);
        res.json({
            success: true,
            data: steps
        });
    }
    catch (error) {
        console.error('Error fetching quest steps:', error);
        if (error instanceof errors_1.QuestError) {
            return res.status(error.httpCode).json(error.toJSON());
        }
        res.status(500).json({
            success: false,
            error: {
                message: 'Internal server error'
            }
        });
    }
});
/**
 * Get NPC response for a quest step
 */
router.get('/quests/:questId/steps/:stepOrder/npc/:npcId/response/:responseType', async (req, res) => {
    try {
        const questId = parseInt(req.params.questId);
        const stepOrder = parseInt(req.params.stepOrder);
        const npcId = parseInt(req.params.npcId);
        const responseType = req.params.responseType;
        if (isNaN(questId) || isNaN(stepOrder) || isNaN(npcId)) {
            throw errors_1.QuestError.invalidQuestData('Invalid parameters');
        }
        if (responseType !== 'finished' && responseType !== 'unfinished') {
            throw errors_1.QuestError.invalidQuestData('Invalid response type');
        }
        const response = QuestService_1.default.getNpcResponse(questId, stepOrder, npcId, responseType);
        if (!response) {
            throw errors_1.QuestError.npcResponseNotFound(stepOrder, npcId, responseType);
        }
        res.json({
            success: true,
            data: response
        });
    }
    catch (error) {
        console.error('Error fetching NPC response:', error);
        if (error instanceof errors_1.QuestError) {
            return res.status(error.httpCode).json(error.toJSON());
        }
        res.status(500).json({
            success: false,
            error: {
                message: 'Internal server error'
            }
        });
    }
});
/**
 * Get player's quest progress
 */
router.get('/players/:playerId/quests', async (req, res) => {
    try {
        const playerId = req.params.playerId;
        if (!playerId) {
            throw errors_1.QuestError.invalidQuestData('Player ID is required');
        }
        const playerQuests = await QuestService_1.default.getPlayerQuests(playerId);
        res.json({
            success: true,
            data: playerQuests
        });
    }
    catch (error) {
        console.error('Error fetching player quests:', error);
        if (error instanceof errors_1.QuestError) {
            return res.status(error.httpCode).json(error.toJSON());
        }
        res.status(500).json({
            success: false,
            error: {
                message: 'Internal server error'
            }
        });
    }
});
/**
 * Get specific player quest progress
 */
router.get('/players/:playerId/quests/:questId', async (req, res) => {
    try {
        const playerId = req.params.playerId;
        const questId = parseInt(req.params.questId);
        if (!playerId || isNaN(questId)) {
            throw errors_1.QuestError.invalidQuestData('Invalid parameters');
        }
        const playerQuest = await QuestService_1.default.getPlayerQuest(playerId, questId);
        if (!playerQuest) {
            throw errors_1.QuestError.questNotStarted(questId);
        }
        res.json({
            success: true,
            data: playerQuest
        });
    }
    catch (error) {
        console.error('Error fetching player quest:', error);
        if (error instanceof errors_1.QuestError) {
            return res.status(error.httpCode).json(error.toJSON());
        }
        res.status(500).json({
            success: false,
            error: {
                message: 'Internal server error'
            }
        });
    }
});
/**
 * Start a quest for a player
 */
router.post('/players/:playerId/quests/:questId/start', async (req, res) => {
    try {
        const playerId = req.params.playerId;
        const questId = parseInt(req.params.questId);
        if (!playerId || isNaN(questId)) {
            throw errors_1.QuestError.invalidQuestData('Invalid parameters');
        }
        const playerQuest = await QuestService_1.default.startQuest(playerId, questId);
        res.status(201).json({
            success: true,
            data: playerQuest
        });
    }
    catch (error) {
        console.error('Error starting quest:', error);
        if (error instanceof errors_1.QuestError) {
            return res.status(error.httpCode).json(error.toJSON());
        }
        res.status(500).json({
            success: false,
            error: {
                message: 'Internal server error'
            }
        });
    }
});
/**
 * Update quest progress
 */
router.put('/players/:playerId/quests/:questId/progress', async (req, res) => {
    try {
        const playerId = req.params.playerId;
        const questId = parseInt(req.params.questId);
        const { stepOrder, stepProgress, isStepCompleted } = req.body;
        if (!playerId || isNaN(questId) || typeof stepOrder !== 'number') {
            throw errors_1.QuestError.invalidQuestData('Invalid parameters');
        }
        const updatedQuest = await QuestService_1.default.updateQuestProgress(playerId, questId, stepOrder, stepProgress || {}, isStepCompleted || false);
        res.json({
            success: true,
            data: updatedQuest
        });
    }
    catch (error) {
        console.error('Error updating quest progress:', error);
        if (error instanceof errors_1.QuestError) {
            return res.status(error.httpCode).json(error.toJSON());
        }
        res.status(500).json({
            success: false,
            error: {
                message: 'Internal server error'
            }
        });
    }
});
/**
 * Refresh quest cache (admin endpoint)
 */
router.post('/admin/quests/refresh-cache', async (req, res) => {
    try {
        await QuestService_1.default.refreshCache();
        res.json({
            success: true,
            message: 'Quest cache refreshed successfully'
        });
    }
    catch (error) {
        console.error('Error refreshing quest cache:', error);
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to refresh quest cache'
            }
        });
    }
});
exports.default = router;
