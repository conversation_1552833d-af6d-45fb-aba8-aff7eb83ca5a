import { Router, Request, Response } from 'express';
import NotificationService from '../services/NotificationService';
import authService from '../services/authService';

const router = Router();

/**
 * Middleware to verify authentication token
 */
const verifyAuth = async (req: Request & { user?: { id: string } }, res: Response, next: any) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          name: 'MissingTokenError',
          message: 'Authorization token is required'
        }
      });
    }

    const userPayload = await authService.verifyToken(token);
    if (!userPayload || !userPayload.userId) {
      return res.status(401).json({
        success: false,
        error: {
          name: 'InvalidTokenError',
          message: 'Invalid or expired token'
        }
      });
    }

    req.user = { id: userPayload.userId.toString() };
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: {
        name: 'AuthenticationError',
        message: 'Authentication failed'
      }
    });
  }
};

/**
 * Get user notifications
 * GET /api/notifications
 */
router.get('/', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const userId = req.user!.id;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    if (limit > 100) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Limit cannot exceed 100'
        }
      });
    }

    const notifications = await NotificationService.getUserNotifications(userId, limit, offset);

    res.json({
      success: true,
      data: {
        notifications,
        hasMore: notifications.length === limit,
        total: notifications.length
      }
    });

  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to get notifications'
      }
    });
  }
});

/**
 * Mark notification as read
 * PUT /api/notifications/:notificationId/read
 */
router.put('/:notificationId/read', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const userId = req.user!.id;
    const { notificationId } = req.params;

    if (!notificationId) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Notification ID is required'
        }
      });
    }

    const success = await NotificationService.markNotificationAsRead(notificationId, userId);

    if (success) {
      res.json({
        success: true,
        message: 'Notification marked as read'
      });
    } else {
      res.status(404).json({
        success: false,
        error: {
          name: 'NotFoundError',
          message: 'Notification not found or already read'
        }
      });
    }

  } catch (error) {
    console.error('Mark notification as read error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to mark notification as read'
      }
    });
  }
});

/**
 * Mark all notifications as read
 * PUT /api/notifications/read-all
 */
router.put('/read-all', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const userId = req.user!.id;

    const count = await NotificationService.markAllNotificationsAsRead(userId);

    res.json({
      success: true,
      data: {
        markedCount: count
      },
      message: `${count} notifications marked as read`
    });

  } catch (error) {
    console.error('Mark all notifications as read error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to mark all notifications as read'
      }
    });
  }
});

/**
 * Send a custom notification (for testing or admin purposes)
 * POST /api/notifications/send
 */
router.post('/send', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const fromUserId = req.user!.id;
    const { toUserId, type, title, message, data } = req.body;

    // Validate required fields
    if (!toUserId || !type || !title || !message) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'toUserId, type, title, and message are required'
        }
      });
    }

    // Validate notification type
    const validTypes = [
      'friend_request_sent', 'friend_request_received', 'friend_request_accepted',
      'friend_request_declined', 'friend_removed', 'user_blocked', 'user_unblocked',
      'user_online', 'user_offline', 'room_invitation', 'system_message'
    ];

    if (!validTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'Invalid notification type'
        }
      });
    }

    await NotificationService.sendRealtimeNotification({
      type,
      fromUserId,
      toUserId,
      title,
      message,
      data
    });

    res.json({
      success: true,
      message: 'Notification sent successfully'
    });

  } catch (error) {
    console.error('Send notification error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to send notification'
      }
    });
  }
});

/**
 * Get user online status
 * GET /api/notifications/online-status/:userId
 */
router.get('/online-status/:userId', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: {
          name: 'ValidationError',
          message: 'User ID is required'
        }
      });
    }

    const status = NotificationService.getUserOnlineStatus(userId);
    const isOnline = NotificationService.isUserOnline(userId);

    res.json({
      success: true,
      data: {
        userId,
        isOnline,
        status
      }
    });

  } catch (error) {
    console.error('Get online status error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to get online status'
      }
    });
  }
});

/**
 * Get all online users (for admin or friends list)
 * GET /api/notifications/online-users
 */
router.get('/online-users', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    const onlineUsers = NotificationService.getOnlineUsers();

    res.json({
      success: true,
      data: {
        onlineUsers,
        count: onlineUsers.length
      }
    });

  } catch (error) {
    console.error('Get online users error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to get online users'
      }
    });
  }
});

/**
 * Clean up expired notifications (admin endpoint)
 * POST /api/notifications/admin/cleanup
 */
router.post('/admin/cleanup', verifyAuth, async (req: Request & { user?: { id: string } }, res: Response) => {
  try {
    // TODO: Add admin role check here
    // For now, any authenticated user can trigger cleanup

    const cleanedCount = await NotificationService.cleanupExpiredNotifications();

    res.json({
      success: true,
      data: {
        cleanedCount
      },
      message: `${cleanedCount} expired notifications cleaned up`
    });

  } catch (error) {
    console.error('Cleanup notifications error:', error);
    res.status(500).json({
      success: false,
      error: {
        name: 'InternalServerError',
        message: 'Failed to cleanup notifications'
      }
    });
  }
});

export default router;
