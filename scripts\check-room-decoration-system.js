const mysql = require('mysql2/promise');
const fs = require('fs');
require('dotenv').config();

async function checkRoomDecorationSystem() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: Number(process.env.DB_PORT) || 3306,
  });

  try {
    console.log('🏠 Checking Room Decoration System Status...');
    
    // 1. Check database tables
    console.log('\n📊 Database Tables:');
    console.log('==================');
    
    let databaseScore = 0;
    const maxDatabaseScore = 4;
    
    // Check home_items table
    try {
      const [homeItemsResult] = await connection.query('SELECT COUNT(*) as count FROM home_items');
      const homeItemsCount = homeItemsResult[0].count;
      console.log(`✅ home_items table: ${homeItemsCount} items placed`);
      databaseScore++;
    } catch (error) {
      console.log('❌ home_items table: Missing or inaccessible');
    }
    
    // Check user_private_spaces table
    try {
      const [spacesResult] = await connection.query('SELECT COUNT(*) as count FROM user_private_spaces');
      const spacesCount = spacesResult[0].count;
      console.log(`✅ user_private_spaces table: ${spacesCount} user spaces configured`);
      databaseScore++;
    } catch (error) {
      console.log('❌ user_private_spaces table: Missing or inaccessible');
    }
    
    // Check items table for furniture
    try {
      const [furnitureResult] = await connection.query("SELECT COUNT(*) as count FROM items WHERE item_type = 'furniture'");
      const furnitureCount = furnitureResult[0].count;
      console.log(`✅ items table (furniture): ${furnitureCount} furniture items available`);
      databaseScore++;
    } catch (error) {
      console.log('❌ items table: Missing or inaccessible');
    }
    
    // Check room_registry table
    try {
      const [roomsResult] = await connection.query("SELECT COUNT(*) as count FROM room_registry WHERE room_type = 'home'");
      const roomsCount = roomsResult[0].count;
      console.log(`✅ room_registry table: ${roomsCount} home rooms registered`);
      databaseScore++;
    } catch (error) {
      console.log('❌ room_registry table: Missing or inaccessible');
    }
    
    // 2. Check Colyseus Schema Models
    console.log('\n🏗️  Colyseus Schema Models:');
    console.log('===========================');
    
    let schemaScore = 0;
    const maxSchemaScore = 3;
    
    // Check HomeRoomState
    if (fs.existsSync('src/models/HomeRoomState.ts')) {
      const homeRoomContent = fs.readFileSync('src/models/HomeRoomState.ts', 'utf8');
      if (homeRoomContent.includes('HomeItemState') && homeRoomContent.includes('furniture')) {
        console.log('✅ HomeRoomState.ts: Complete with furniture management');
        schemaScore++;
      } else {
        console.log('⚠️  HomeRoomState.ts: Exists but missing furniture integration');
      }
    } else {
      console.log('❌ HomeRoomState.ts: Missing');
    }
    
    // Check HomeItemState
    if (fs.existsSync('src/models/HomeItemState.ts')) {
      const homeItemContent = fs.readFileSync('src/models/HomeItemState.ts', 'utf8');
      if (homeItemContent.includes('rotation') && homeItemContent.includes('isFlipped')) {
        console.log('✅ HomeItemState.ts: Complete with positioning and rotation');
        schemaScore++;
      } else {
        console.log('⚠️  HomeItemState.ts: Exists but missing advanced features');
      }
    } else {
      console.log('❌ HomeItemState.ts: Missing');
    }
    
    // Check PlayerState integration
    if (fs.existsSync('src/models/PlayerState.ts')) {
      const playerStateContent = fs.readFileSync('src/models/PlayerState.ts', 'utf8');
      if (playerStateContent.includes('currentOutfit')) {
        console.log('✅ PlayerState.ts: Has outfit integration (related to decoration)');
        schemaScore++;
      } else {
        console.log('⚠️  PlayerState.ts: Missing outfit integration');
      }
    } else {
      console.log('❌ PlayerState.ts: Missing');
    }
    
    // 3. Check Services
    console.log('\n🛠️  Services:');
    console.log('=============');
    
    let servicesScore = 0;
    const maxServicesScore = 3;
    
    // Check SpaceService
    if (fs.existsSync('src/services/SpaceService.ts')) {
      const spaceServiceContent = fs.readFileSync('src/services/SpaceService.ts', 'utf8');
      if (spaceServiceContent.includes('placeHomeItem') && spaceServiceContent.includes('removeHomeItem')) {
        console.log('✅ SpaceService.ts: Complete with item placement/removal');
        servicesScore++;
      } else {
        console.log('⚠️  SpaceService.ts: Exists but missing core functionality');
      }
    } else {
      console.log('❌ SpaceService.ts: Missing');
    }
    
    // Check ItemService
    if (fs.existsSync('src/services/ItemService.ts')) {
      const itemServiceContent = fs.readFileSync('src/services/ItemService.ts', 'utf8');
      if (itemServiceContent.includes('getItemDetails') && itemServiceContent.includes('furniture')) {
        console.log('✅ ItemService.ts: Complete with furniture support');
        servicesScore++;
      } else {
        console.log('⚠️  ItemService.ts: Exists but missing furniture support');
      }
    } else {
      console.log('❌ ItemService.ts: Missing');
    }
    
    // Check RoomMatchingService
    if (fs.existsSync('src/services/RoomMatchingService.ts')) {
      console.log('✅ RoomMatchingService.ts: Room management service exists');
      servicesScore++;
    } else {
      console.log('❌ RoomMatchingService.ts: Missing');
    }
    
    // 4. Check Colyseus Rooms
    console.log('\n🏠 Colyseus Rooms:');
    console.log('==================');
    
    let roomsScore = 0;
    const maxRoomsScore = 3;
    
    // Check HomeRoom
    if (fs.existsSync('src/rooms/HomeRoom.ts')) {
      const homeRoomContent = fs.readFileSync('src/rooms/HomeRoom.ts', 'utf8');
      if (homeRoomContent.includes('place_item') && homeRoomContent.includes('remove_item')) {
        console.log('✅ HomeRoom.ts: Complete with item placement handlers');
        roomsScore++;
      } else {
        console.log('⚠️  HomeRoom.ts: Exists but missing item handlers');
      }
    } else {
      console.log('❌ HomeRoom.ts: Missing');
    }
    
    // Check HomeRoomDynamic
    if (fs.existsSync('src/rooms/HomeRoomDynamic.ts')) {
      const homeRoomDynamicContent = fs.readFileSync('src/rooms/HomeRoomDynamic.ts', 'utf8');
      if (homeRoomDynamicContent.includes('place_item') && homeRoomDynamicContent.includes('BasePrivateRoom')) {
        console.log('✅ HomeRoomDynamic.ts: Complete dynamic room implementation');
        roomsScore++;
      } else {
        console.log('⚠️  HomeRoomDynamic.ts: Exists but incomplete');
      }
    } else {
      console.log('❌ HomeRoomDynamic.ts: Missing');
    }
    
    // Check BasePrivateRoom
    if (fs.existsSync('src/rooms/BasePrivateRoom.ts')) {
      console.log('✅ BasePrivateRoom.ts: Base room functionality exists');
      roomsScore++;
    } else {
      console.log('❌ BasePrivateRoom.ts: Missing');
    }
    
    // 5. Check API Routes
    console.log('\n🌐 API Routes:');
    console.log('==============');
    
    let routesScore = 0;
    const maxRoutesScore = 2;
    
    // Check room routes
    if (fs.existsSync('src/routes/roomRoutes.ts')) {
      const roomRoutesContent = fs.readFileSync('src/routes/roomRoutes.ts', 'utf8');
      if (roomRoutesContent.includes('home') && roomRoutesContent.includes('generate-id')) {
        console.log('✅ roomRoutes.ts: Complete room management API');
        routesScore++;
      } else {
        console.log('⚠️  roomRoutes.ts: Exists but incomplete');
      }
    } else {
      console.log('❌ roomRoutes.ts: Missing');
    }
    
    // Check if routes are registered in index.ts
    if (fs.existsSync('src/index.ts')) {
      const indexContent = fs.readFileSync('src/index.ts', 'utf8');
      if (indexContent.includes('roomRoutes') && indexContent.includes('home_dynamic')) {
        console.log('✅ index.ts: Routes and rooms properly registered');
        routesScore++;
      } else {
        console.log('⚠️  index.ts: Missing route/room registration');
      }
    } else {
      console.log('❌ index.ts: Missing');
    }
    
    // 6. Calculate overall completion
    console.log('\n📊 System Completion Analysis:');
    console.log('==============================');
    
    const totalScore = databaseScore + schemaScore + servicesScore + roomsScore + routesScore;
    const maxTotalScore = maxDatabaseScore + maxSchemaScore + maxServicesScore + maxRoomsScore + maxRoutesScore;
    const completionPercentage = Math.round((totalScore / maxTotalScore) * 100);
    
    console.log(`Database Tables: ${databaseScore}/${maxDatabaseScore} (${Math.round(databaseScore/maxDatabaseScore*100)}%)`);
    console.log(`Schema Models: ${schemaScore}/${maxSchemaScore} (${Math.round(schemaScore/maxSchemaScore*100)}%)`);
    console.log(`Services: ${servicesScore}/${maxServicesScore} (${Math.round(servicesScore/maxServicesScore*100)}%)`);
    console.log(`Colyseus Rooms: ${roomsScore}/${maxRoomsScore} (${Math.round(roomsScore/maxRoomsScore*100)}%)`);
    console.log(`API Routes: ${routesScore}/${maxRoutesScore} (${Math.round(routesScore/maxRoutesScore*100)}%)`);
    
    console.log(`\n🎯 Overall Completion: ${completionPercentage}%`);
    
    // 7. Provide completion status
    if (completionPercentage >= 90) {
      console.log('🎉 Room decoration system is fully implemented and ready for use!');
    } else if (completionPercentage >= 75) {
      console.log('🚀 Room decoration system is mostly complete, minor enhancements needed');
    } else if (completionPercentage >= 50) {
      console.log('⚠️  Room decoration system is partially complete, significant work needed');
    } else {
      console.log('❌ Room decoration system needs major development');
    }
    
    // 8. Feature analysis
    console.log('\n🎮 Feature Analysis:');
    console.log('====================');
    
    const features = {
      'Item Placement': databaseScore >= 1 && roomsScore >= 1,
      'Item Rotation/Flipping': schemaScore >= 2,
      'Inventory Integration': servicesScore >= 2,
      'Real-time Synchronization': roomsScore >= 2,
      'Room Access Control': routesScore >= 1,
      'Dynamic Room Creation': roomsScore >= 2,
      'Background Customization': databaseScore >= 2,
      'Multi-user Support': roomsScore >= 1
    };
    
    Object.entries(features).forEach(([feature, implemented]) => {
      console.log(`${implemented ? '✅' : '❌'} ${feature}`);
    });
    
    // 9. Recommendations
    console.log('\n💡 Recommendations:');
    console.log('===================');
    
    if (databaseScore < maxDatabaseScore) {
      console.log('- Set up missing database tables for home decoration system');
    }
    if (schemaScore < maxSchemaScore) {
      console.log('- Complete Colyseus schema models for real-time synchronization');
    }
    if (servicesScore < maxServicesScore) {
      console.log('- Implement missing service layer functionality');
    }
    if (roomsScore < maxRoomsScore) {
      console.log('- Complete Colyseus room implementations');
    }
    if (routesScore < maxRoutesScore) {
      console.log('- Add missing API endpoints for room management');
    }
    
    if (completionPercentage >= 90) {
      console.log('- System is ready! Consider adding advanced features like:');
      console.log('  * Item layering and z-index management');
      console.log('  * Furniture collections and themes');
      console.log('  * Room templates and presets');
      console.log('  * Social features (room visiting, ratings)');
    }
    
  } catch (error) {
    console.error('❌ Error checking room decoration system:', error);
  } finally {
    await connection.end();
  }
}

checkRoomDecorationSystem();
