import { Server } from '@colyseus/core';
import pool from '../utils/db';
import { ResultSetHeader, RowDataPacket } from 'mysql2';

/**
 * Notification types for different events
 */
export type NotificationType =
  | 'friend_request_sent'
  | 'friend_request_received'
  | 'friend_request_accepted'
  | 'friend_request_declined'
  | 'friend_removed'
  | 'user_blocked'
  | 'user_unblocked'
  | 'user_online'
  | 'user_offline'
  | 'room_invitation'
  | 'system_message';

/**
 * Notification data structure
 */
export interface NotificationData {
  id?: string;
  type: NotificationType;
  fromUserId?: string;
  toUserId: string;
  title: string;
  message: string;
  data?: any;
  isRead?: boolean;
  createdAt?: Date;
  expiresAt?: Date;
}

/**
 * Real-time notification payload
 */
export interface RealtimeNotification {
  id: string;
  type: NotificationType;
  fromUserId?: string;
  fromUsername?: string;
  title: string;
  message: string;
  data?: any;
  timestamp: number;
  isRead: boolean;
}

/**
 * User online status
 */
export interface UserOnlineStatus {
  userId: string;
  isOnline: boolean;
  lastSeen: Date;
  currentRoom?: string;
}

/**
 * Global notification service for real-time notifications
 */
class NotificationService {
  private static instance: NotificationService;
  private gameServer?: Server;
  private onlineUsers: Map<string, UserOnlineStatus> = new Map();
  private userSessions: Map<string, Set<string>> = new Map(); // userId -> Set of sessionIds

  private constructor() {}

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Initialize the notification service with Colyseus server
   */
  initialize(gameServer: Server): void {
    this.gameServer = gameServer;
    console.log('NotificationService initialized');
  }

  /**
   * Register user session for notifications
   */
  registerUserSession(userId: string, sessionId: string): void {
    if (!this.userSessions.has(userId)) {
      this.userSessions.set(userId, new Set());
    }
    this.userSessions.get(userId)!.add(sessionId);

    // Update online status
    this.updateUserOnlineStatus(userId, true);

    console.log(`User ${userId} session ${sessionId} registered for notifications`);
  }

  /**
   * Unregister user session
   */
  unregisterUserSession(userId: string, sessionId: string): void {
    const sessions = this.userSessions.get(userId);
    if (sessions) {
      sessions.delete(sessionId);
      if (sessions.size === 0) {
        this.userSessions.delete(userId);
        this.updateUserOnlineStatus(userId, false);
      }
    }

    console.log(`User ${userId} session ${sessionId} unregistered from notifications`);
  }

  /**
   * Update user online status
   */
  private updateUserOnlineStatus(userId: string, isOnline: boolean, currentRoom?: string): void {
    const status: UserOnlineStatus = {
      userId,
      isOnline,
      lastSeen: new Date(),
      currentRoom
    };

    this.onlineUsers.set(userId, status);

    // Notify friends about online status change
    this.notifyFriendsAboutStatusChange(userId, isOnline);
  }

  /**
   * Get user online status
   */
  getUserOnlineStatus(userId: string): UserOnlineStatus | null {
    return this.onlineUsers.get(userId) || null;
  }

  /**
   * Get all online users
   */
  getOnlineUsers(): UserOnlineStatus[] {
    return Array.from(this.onlineUsers.values()).filter(status => status.isOnline);
  }

  /**
   * Check if user is online
   */
  isUserOnline(userId: string): boolean {
    const status = this.onlineUsers.get(userId);
    return status?.isOnline || false;
  }

  /**
   * Send real-time notification to user
   */
  async sendRealtimeNotification(notification: NotificationData): Promise<void> {
    try {
      // Save notification to database
      const savedNotification = await this.saveNotification(notification);

      // Send real-time notification if user is online
      const sessions = this.userSessions.get(notification.toUserId);
      if (sessions && sessions.size > 0) {
        const realtimeNotification: RealtimeNotification = {
          id: savedNotification.id!,
          type: notification.type,
          fromUserId: notification.fromUserId,
          fromUsername: notification.fromUserId ? await this.getUsernameById(notification.fromUserId) : undefined,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          timestamp: Date.now(),
          isRead: false
        };

        // Send to notification room if it exists
        const notificationRoom = (this.gameServer as any)?.rooms?.find((room: any) =>
          room.roomName === 'notification' &&
          room.clients.some((client: any) => client.auth?.uid === notification.toUserId)
        );

        if (notificationRoom) {
          notificationRoom.broadcast('notification', realtimeNotification);
        }

        console.log(`Real-time notification sent to user ${notification.toUserId}: ${notification.type}`);
      } else {
        console.log(`User ${notification.toUserId} is offline, notification saved for later`);
      }
    } catch (error) {
      console.error('Error sending real-time notification:', error);
    }
  }

  /**
   * Send friend request notification
   */
  async sendFriendRequestNotification(fromUserId: string, toUserId: string): Promise<void> {
    const fromUsername = await this.getUsernameById(fromUserId);

    await this.sendRealtimeNotification({
      type: 'friend_request_received',
      fromUserId,
      toUserId,
      title: 'New Friend Request',
      message: `${fromUsername} sent you a friend request`,
      data: { fromUserId, fromUsername }
    });
  }

  /**
   * Send friend request accepted notification
   */
  async sendFriendRequestAcceptedNotification(fromUserId: string, toUserId: string): Promise<void> {
    const fromUsername = await this.getUsernameById(fromUserId);

    await this.sendRealtimeNotification({
      type: 'friend_request_accepted',
      fromUserId,
      toUserId,
      title: 'Friend Request Accepted',
      message: `${fromUsername} accepted your friend request`,
      data: { fromUserId, fromUsername }
    });
  }

  /**
   * Send friend request declined notification
   */
  async sendFriendRequestDeclinedNotification(fromUserId: string, toUserId: string): Promise<void> {
    const fromUsername = await this.getUsernameById(fromUserId);

    await this.sendRealtimeNotification({
      type: 'friend_request_declined',
      fromUserId,
      toUserId,
      title: 'Friend Request Declined',
      message: `${fromUsername} declined your friend request`,
      data: { fromUserId, fromUsername }
    });
  }

  /**
   * Send friend removed notification
   */
  async sendFriendRemovedNotification(fromUserId: string, toUserId: string): Promise<void> {
    const fromUsername = await this.getUsernameById(fromUserId);

    await this.sendRealtimeNotification({
      type: 'friend_removed',
      fromUserId,
      toUserId,
      title: 'Friend Removed',
      message: `${fromUsername} removed you from their friends list`,
      data: { fromUserId, fromUsername }
    });
  }

  /**
   * Send user blocked notification
   */
  async sendUserBlockedNotification(fromUserId: string, toUserId: string): Promise<void> {
    const fromUsername = await this.getUsernameById(fromUserId);

    await this.sendRealtimeNotification({
      type: 'user_blocked',
      fromUserId,
      toUserId,
      title: 'User Blocked',
      message: `You have been blocked by ${fromUsername}`,
      data: { fromUserId, fromUsername }
    });
  }

  /**
   * Notify friends about user online status change
   */
  private async notifyFriendsAboutStatusChange(userId: string, isOnline: boolean): Promise<void> {
    try {
      // Get user's friends
      const friends = await this.getUserFriends(userId);
      const username = await this.getUsernameById(userId);

      for (const friendId of friends) {
        if (this.isUserOnline(friendId)) {
          await this.sendRealtimeNotification({
            type: isOnline ? 'user_online' : 'user_offline',
            fromUserId: userId,
            toUserId: friendId,
            title: 'Friend Status Update',
            message: `${username} is now ${isOnline ? 'online' : 'offline'}`,
            data: { userId, username, isOnline }
          });
        }
      }
    } catch (error) {
      console.error('Error notifying friends about status change:', error);
    }
  }

  /**
   * Save notification to database
   */
  private async saveNotification(notification: NotificationData): Promise<NotificationData> {
    try {
      const id = `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const expiresAt = notification.expiresAt || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days default

      const [result] = await pool.query<ResultSetHeader>(
        `INSERT INTO notifications (id, type, from_user_id, to_user_id, title, message, data, expires_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id,
          notification.type,
          notification.fromUserId || null,
          notification.toUserId,
          notification.title,
          notification.message,
          notification.data ? JSON.stringify(notification.data) : null,
          expiresAt
        ]
      );

      return {
        ...notification,
        id,
        isRead: false,
        createdAt: new Date(),
        expiresAt
      };
    } catch (error) {
      console.error('Error saving notification to database:', error);
      throw error;
    }
  }

  /**
   * Get user notifications from database
   */
  async getUserNotifications(userId: string, limit: number = 50, offset: number = 0): Promise<NotificationData[]> {
    try {
      const [rows] = await pool.query<RowDataPacket[]>(
        `SELECT * FROM notifications
         WHERE to_user_id = ? AND expires_at > NOW()
         ORDER BY created_at DESC
         LIMIT ? OFFSET ?`,
        [userId, limit, offset]
      );

      return rows.map(row => ({
        id: row.id,
        type: row.type,
        fromUserId: row.from_user_id,
        toUserId: row.to_user_id,
        title: row.title,
        message: row.message,
        data: row.data ? JSON.parse(row.data) : null,
        isRead: row.is_read,
        createdAt: row.created_at,
        expiresAt: row.expires_at
      }));
    } catch (error) {
      console.error('Error getting user notifications:', error);
      return [];
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(notificationId: string, userId: string): Promise<boolean> {
    try {
      const [result] = await pool.query<ResultSetHeader>(
        'UPDATE notifications SET is_read = TRUE WHERE id = ? AND to_user_id = ?',
        [notificationId, userId]
      );

      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  }

  /**
   * Mark all notifications as read for user
   */
  async markAllNotificationsAsRead(userId: string): Promise<number> {
    try {
      const [result] = await pool.query<ResultSetHeader>(
        'UPDATE notifications SET is_read = TRUE WHERE to_user_id = ? AND is_read = FALSE',
        [userId]
      );

      return result.affectedRows;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return 0;
    }
  }

  /**
   * Get username by user ID
   */
  private async getUsernameById(userId: string): Promise<string> {
    try {
      const [rows] = await pool.query<RowDataPacket[]>(
        'SELECT username FROM users WHERE id = ?',
        [userId]
      );

      return rows.length > 0 ? rows[0].username : `User_${userId}`;
    } catch (error) {
      console.error('Error getting username:', error);
      return `User_${userId}`;
    }
  }

  /**
   * Get user's friends list
   */
  private async getUserFriends(userId: string): Promise<string[]> {
    try {
      const [rows] = await pool.query<RowDataPacket[]>(
        `SELECT
           CASE
             WHEN user_one_id = ? THEN user_two_id
             ELSE user_one_id
           END as friend_id
         FROM friend_relationships
         WHERE (user_one_id = ? OR user_two_id = ?) AND status = 'accepted'`,
        [userId, userId, userId]
      );

      return rows.map(row => row.friend_id.toString());
    } catch (error) {
      console.error('Error getting user friends:', error);
      return [];
    }
  }

  /**
   * Clean up expired notifications
   */
  async cleanupExpiredNotifications(): Promise<number> {
    try {
      const [result] = await pool.query<ResultSetHeader>(
        'DELETE FROM notifications WHERE expires_at < NOW()'
      );

      return result.affectedRows;
    } catch (error) {
      console.error('Error cleaning up expired notifications:', error);
      return 0;
    }
  }
}

export default NotificationService.getInstance();
