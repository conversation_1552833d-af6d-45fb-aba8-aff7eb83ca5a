# 数据库表结构导出

## 概述

本文档包含了 2D 虚拟世界游戏服务端的完整数据库表结构。数据库采用 MySQL 8.0+，字符集为 utf8mb4，排序规则为 utf8mb4_unicode_ci。

## 表结构总览

### 核心系统表 (6 张)
1. **users** - 用户账户表
2. **user_auth_tokens** - 用户认证令牌表
3. **friend_relationships** - 好友关系表
4. **characters** - 角色信息表
5. **items** - 物品模板表
6. **user_private_spaces** - 用户私人空间设置表

### 家园系统表 (2 张)
7. **home_items** - 家具实例表
8. **garden_plots** - 花园地块表

### 任务系统表 (4 张)
9. **quests** - 任务模板表
10. **quest_steps** - 任务步骤表
11. **quest_step_responses** - NPC 响应表
12. **player_quests** - 玩家任务进度表

### 商店系统表 (9 张)
13. **user_currencies** - 用户货币表
14. **shop_categories** - 商店分类表
15. **shop_items** - 商店商品表
16. **user_inventory** - 用户库存表
17. **purchase_history** - 购买历史表
18. **user_purchase_limits** - 用户购买限制表
19. **promotions** - 促销活动表
20. **user_promotion_usage** - 用户促销使用记录表
21. **wishlist** - 用户心愿单表

**总计：21 张表**

## 详细表结构

### 1. 核心系统表

#### users - 用户账户表
```sql
CREATE TABLE `users` (
  `id` BIGINT AUTO_INCREMENT COMMENT 'Unique User ID',
  `username` VARCHAR(255) NOT NULL COMMENT 'Username',
  `password_hash` VARCHAR(255) NOT NULL COMMENT 'Hashed password',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record Creation Time',
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record Last Update Time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores user accounts';
```

#### user_auth_tokens - 用户认证令牌表
```sql
CREATE TABLE `user_auth_tokens` (
  `uid` BIGINT NOT NULL COMMENT 'Linked User ID from users table',
  `token` VARCHAR(255) NOT NULL COMMENT 'SHA256 Hashed Game Token',
  `expires_at` BIGINT UNSIGNED NOT NULL COMMENT 'Token Expiration Timestamp (Unix milliseconds)',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`uid`, `token`),
  UNIQUE KEY `idx_token` (`token`),
  FOREIGN KEY (`uid`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores active game login tokens';
```

#### friend_relationships - 好友关系表
```sql
CREATE TABLE `friend_relationships` (
  `id` BIGINT AUTO_INCREMENT,
  `user_one_id` BIGINT NOT NULL COMMENT 'Smaller user ID',
  `user_two_id` BIGINT NOT NULL COMMENT 'Larger user ID',
  `status` ENUM('pending', 'accepted', 'declined', 'blocked') NOT NULL COMMENT 'Relationship status',
  `action_user_id` BIGINT NOT NULL COMMENT 'User ID who performed the last action',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_relationship` (`user_one_id`, `user_two_id`),
  INDEX `idx_user_one_status` (`user_one_id`, `status`),
  INDEX `idx_user_two_status` (`user_two_id`, `status`),
  FOREIGN KEY (`user_one_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_two_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`action_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores friendships and block relationships';
```

#### characters - 角色信息表
```sql
CREATE TABLE `characters` (
  `character_id` BIGINT AUTO_INCREMENT COMMENT 'Unique character ID',
  `user_id` BIGINT NOT NULL COMMENT 'User ID owning the character',
  `character_name` VARCHAR(255) NOT NULL COMMENT 'Character name',
  `level` INT NOT NULL DEFAULT 1,
  `experience` BIGINT NOT NULL DEFAULT 0,
  `pos_x` FLOAT NOT NULL DEFAULT 0.0,
  `pos_y` FLOAT NOT NULL DEFAULT 0.0,
  `current_scene_id` VARCHAR(255) NOT NULL,
  `selected_appearance_details` JSON NOT NULL COMMENT 'Character appearance details, JSON format',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`character_id`),
  INDEX `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores player characters';
```

#### items - 物品模板表
```sql
CREATE TABLE `items` (
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Unique Item ID (e.g., "basic_chair", "tomato_seed")',
  `item_name` VARCHAR(255) NOT NULL COMMENT 'Display name of the item',
  `description` TEXT COMMENT 'Description of the item',
  `item_type` ENUM('furniture', 'plant', 'seed', 'tool', 'wearable', 'consumable', 'collectible') NOT NULL COMMENT 'Broad category of the item',
  `category` VARCHAR(255) COMMENT 'Specific category (e.g., "decor", "chair", "vegetable", "hat", "potion")',
  `price` INT UNSIGNED COMMENT 'Price in in-game currency (if applicable)',
  `is_stackable` BOOLEAN DEFAULT FALSE,
  `max_stack_size` INT DEFAULT 1,
  `total_growth_time_ms` INT UNSIGNED COMMENT 'For plants/seeds: total time from planting to harvestable in milliseconds',
  `num_growth_stages` INT UNSIGNED COMMENT 'For plants/seeds: number of distinct visual growth stages',
  `water_interval_ms` INT UNSIGNED COMMENT 'For plants/seeds: how often it needs watering in milliseconds',
  `harvest_details` JSON COMMENT 'For plants/seeds: JSON detailing item(s) and quantities yielded upon harvest',
  `data` JSON COMMENT 'Additional item-specific data (e.g., dimensions for furniture, effect for consumables)',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores item templates and definitions';
```

#### user_private_spaces - 用户私人空间设置表
```sql
CREATE TABLE `user_private_spaces` (
  `owner_user_id` BIGINT NOT NULL COMMENT 'Linked to users table ID',
  `home_background_id` VARCHAR(255) DEFAULT 'default_home_bg',
  `garden_background_id` VARCHAR(255) DEFAULT 'default_garden_bg',
  `home_access_level` ENUM('private', 'friends_only', 'public') DEFAULT 'private',
  `garden_access_level` ENUM('private', 'friends_only', 'public') DEFAULT 'private',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`owner_user_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`home_background_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT,
  FOREIGN KEY (`garden_background_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores settings for user''s private home and garden spaces';
```

### 2. 家园系统表

#### home_items - 家具实例表
```sql
CREATE TABLE `home_items` (
  `item_instance_id` VARCHAR(36) NOT NULL COMMENT 'Unique instance ID for this placed item (UUID)',
  `owner_user_id` BIGINT NOT NULL COMMENT 'User ID who owns this home and item instance',
  `item_template_id` VARCHAR(255) NOT NULL COMMENT 'Template ID from the items table',
  `pos_x` FLOAT NOT NULL,
  `pos_y` FLOAT NOT NULL,
  `rotation` FLOAT DEFAULT 0.0,
  `is_flipped` BOOLEAN DEFAULT FALSE,
  `placed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`item_instance_id`),
  INDEX `idx_owner_user_id` (`owner_user_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`item_template_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores instances of items placed in users'' homes';
```

#### garden_plots - 花园地块表
```sql
CREATE TABLE `garden_plots` (
  `plot_id` VARCHAR(255) NOT NULL COMMENT 'Unique ID for this plot instance (e.g., owner_user_id_plot_index)',
  `owner_user_id` BIGINT NOT NULL COMMENT 'User ID who owns this garden plot instance',
  `plot_template_id` VARCHAR(255) NOT NULL COMMENT 'Template ID for the plot itself (e.g., "small_plot")',
  `seed_id` VARCHAR(255) COMMENT 'Template ID of the seed currently planted',
  `plant_timestamp` BIGINT UNSIGNED COMMENT 'Unix timestamp (ms) when current seed was planted',
  `growth_stage` INT DEFAULT 0 COMMENT 'Current growth stage of the plant',
  `last_watered_timestamp` BIGINT UNSIGNED COMMENT 'Unix timestamp (ms) when plot was last watered',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`plot_id`),
  UNIQUE KEY `idx_owner_plot` (`owner_user_id`, `plot_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`plot_template_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT,
  FOREIGN KEY (`seed_id`) REFERENCES `items`(`item_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores instances of garden plots in users'' gardens';
```

### 3. 任务系统表

#### quests - 任务模板表
```sql
CREATE TABLE `quests` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique quest ID',
  `title` VARCHAR(255) NOT NULL COMMENT 'Quest title',
  `accept_message_0` TEXT COMMENT 'Accept message line 0',
  `accept_message_1` TEXT COMMENT 'Accept message line 1',
  `accept_message_2` TEXT COMMENT 'Accept message line 2',
  `decline_message_0` TEXT COMMENT 'Decline message line 0',
  `reward_message_0_0` TEXT COMMENT 'Reward message line 0_0',
  `reward_message_0_1` TEXT COMMENT 'Reward message line 0_1',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores quest templates and definitions';
```

#### quest_steps - 任务步骤表
```sql
CREATE TABLE `quest_steps` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique step ID',
  `quest_id` INT NOT NULL COMMENT 'Reference to quest',
  `step_order` INT NOT NULL COMMENT 'Order of this step in the quest',
  `goal` TEXT NOT NULL COMMENT 'Step goal description',
  `summary` TEXT COMMENT 'Step summary',
  `intro_0` TEXT COMMENT 'Intro message line 0',
  `intro_1` TEXT COMMENT 'Intro message line 1',
  `intro_2` TEXT COMMENT 'Intro message line 2',
  `intro_3` TEXT COMMENT 'Intro message line 3',
  `intro_4` TEXT COMMENT 'Intro message line 4',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`quest_id`) REFERENCES `quests`(`id`) ON DELETE CASCADE,
  INDEX `idx_quest_step_order` (`quest_id`, `step_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores quest step templates';
```

#### quest_step_responses - NPC 响应表
```sql
CREATE TABLE `quest_step_responses` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique response ID',
  `step_id` INT NOT NULL COMMENT 'Reference to quest step',
  `npc_id` INT NOT NULL COMMENT 'NPC ID that gives this response',
  `response_type` ENUM('finished', 'unfinished') NOT NULL COMMENT 'Type of response',
  `prompt` TEXT COMMENT 'Response prompt',
  `message_0` TEXT COMMENT 'Response message line 0',
  `message_1` TEXT COMMENT 'Response message line 1',
  `message_2` TEXT COMMENT 'Response message line 2',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`step_id`) REFERENCES `quest_steps`(`id`) ON DELETE CASCADE,
  INDEX `idx_step_npc_type` (`step_id`, `npc_id`, `response_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores NPC responses for quest steps';
```

#### player_quests - 玩家任务进度表
```sql
CREATE TABLE `player_quests` (
  `player_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `quest_id` INT NOT NULL COMMENT 'Reference to quest',
  `current_step_order` INT NOT NULL DEFAULT 0 COMMENT 'Current step the player is on',
  `is_completed` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether the quest is completed',
  `step_progress` JSON COMMENT 'Step-specific progress data',
  `started_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When the quest was started',
  `completed_at` TIMESTAMP NULL COMMENT 'When the quest was completed',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`player_id`, `quest_id`),
  FOREIGN KEY (`player_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`quest_id`) REFERENCES `quests`(`id`) ON DELETE CASCADE,
  INDEX `idx_player_completed` (`player_id`, `is_completed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores player quest progress';
```

### 4. 商店系统表

#### user_currencies - 用户货币表
```sql
CREATE TABLE `user_currencies` (
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `gold_coins` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Basic currency - gold coins',
  `diamonds` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Premium currency - diamonds',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores player currency balances';
```

#### shop_categories - 商店分类表
```sql
CREATE TABLE `shop_categories` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique category ID',
  `name` VARCHAR(255) NOT NULL COMMENT 'Category name (e.g., "花之屋", "家园居")',
  `display_name` VARCHAR(255) NOT NULL COMMENT 'Display name for UI',
  `description` TEXT COMMENT 'Category description',
  `icon` VARCHAR(255) COMMENT 'Category icon identifier',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT 'Display order',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether category is active',
  `parent_category_id` INT NULL COMMENT 'Parent category for subcategories',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`parent_category_id`) REFERENCES `shop_categories`(`id`) ON DELETE SET NULL,
  INDEX `idx_parent_sort` (`parent_category_id`, `sort_order`),
  INDEX `idx_active_sort` (`is_active`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores shop category hierarchy';
```

#### shop_items - 商店商品表
```sql
CREATE TABLE `shop_items` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique shop item ID',
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Reference to item template',
  `category_id` INT NOT NULL COMMENT 'Reference to shop category',
  `name` VARCHAR(255) NOT NULL COMMENT 'Shop display name',
  `description` TEXT COMMENT 'Shop item description',
  `price_gold` BIGINT UNSIGNED NULL COMMENT 'Price in gold coins',
  `price_diamonds` BIGINT UNSIGNED NULL COMMENT 'Price in diamonds',
  `original_price_gold` BIGINT UNSIGNED NULL COMMENT 'Original price for discount display',
  `original_price_diamonds` BIGINT UNSIGNED NULL COMMENT 'Original price for discount display',
  `is_available` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether item is available for purchase',
  `is_featured` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether item is featured',
  `is_limited_time` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether item is limited time',
  `available_from` TIMESTAMP NULL COMMENT 'When item becomes available',
  `available_until` TIMESTAMP NULL COMMENT 'When item expires',
  `max_purchases_per_user` INT NULL COMMENT 'Maximum purchases per user (NULL = unlimited)',
  `total_stock` INT NULL COMMENT 'Total stock available (NULL = unlimited)',
  `remaining_stock` INT NULL COMMENT 'Remaining stock',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT 'Display order within category',
  `tags` JSON COMMENT 'Item tags for filtering and search',
  `metadata` JSON COMMENT 'Additional shop-specific metadata',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`item_id`) REFERENCES `items`(`item_id`) ON DELETE CASCADE,
  FOREIGN KEY (`category_id`) REFERENCES `shop_categories`(`id`) ON DELETE CASCADE,
  INDEX `idx_category_sort` (`category_id`, `sort_order`),
  INDEX `idx_available_featured` (`is_available`, `is_featured`),
  INDEX `idx_limited_time` (`is_limited_time`, `available_from`, `available_until`),
  INDEX `idx_item_lookup` (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores shop item listings and pricing';
```

#### user_inventory - 用户库存表
```sql
CREATE TABLE `user_inventory` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique inventory entry ID',
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Reference to item template',
  `quantity` BIGINT UNSIGNED NOT NULL DEFAULT 1 COMMENT 'Quantity owned',
  `acquired_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When item was acquired',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`item_id`) REFERENCES `items`(`item_id`) ON DELETE CASCADE,
  UNIQUE KEY `idx_user_item` (`user_id`, `item_id`),
  INDEX `idx_user_acquired` (`user_id`, `acquired_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores player inventory items';
```

#### purchase_history - 购买历史表
```sql
CREATE TABLE `purchase_history` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique transaction ID',
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `shop_item_id` INT NOT NULL COMMENT 'Reference to shop item',
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Reference to item template',
  `quantity` INT UNSIGNED NOT NULL DEFAULT 1 COMMENT 'Quantity purchased',
  `price_gold` BIGINT UNSIGNED NULL COMMENT 'Gold coins spent',
  `price_diamonds` BIGINT UNSIGNED NULL COMMENT 'Diamonds spent',
  `transaction_type` ENUM('purchase', 'gift_sent', 'gift_received', 'refund') NOT NULL DEFAULT 'purchase',
  `gift_recipient_id` BIGINT NULL COMMENT 'If gift, recipient user ID',
  `gift_sender_id` BIGINT NULL COMMENT 'If gift received, sender user ID',
  `purchase_source` VARCHAR(255) NULL COMMENT 'Source of purchase (shop, event, etc.)',
  `metadata` JSON COMMENT 'Additional transaction metadata',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`shop_item_id`) REFERENCES `shop_items`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`item_id`) REFERENCES `items`(`item_id`) ON DELETE CASCADE,
  FOREIGN KEY (`gift_recipient_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`gift_sender_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  INDEX `idx_user_date` (`user_id`, `created_at`),
  INDEX `idx_transaction_type` (`transaction_type`),
  INDEX `idx_gift_recipient` (`gift_recipient_id`),
  INDEX `idx_gift_sender` (`gift_sender_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores purchase transaction history';
```

#### user_purchase_limits - 用户购买限制表
```sql
CREATE TABLE `user_purchase_limits` (
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `shop_item_id` INT NOT NULL COMMENT 'Reference to shop item',
  `purchase_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of times purchased',
  `last_purchase_at` TIMESTAMP NULL COMMENT 'Last purchase timestamp',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`, `shop_item_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`shop_item_id`) REFERENCES `shop_items`(`id`) ON DELETE CASCADE,
  INDEX `idx_last_purchase` (`last_purchase_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tracks user purchase limits for limited items';
```

#### promotions - 促销活动表
```sql
CREATE TABLE `promotions` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique promotion ID',
  `name` VARCHAR(255) NOT NULL COMMENT 'Promotion name',
  `description` TEXT COMMENT 'Promotion description',
  `type` ENUM('discount_percentage', 'discount_fixed', 'bundle', 'free_gift', 'buy_x_get_y') NOT NULL COMMENT 'Promotion type',
  `discount_percentage` DECIMAL(5,2) NULL COMMENT 'Discount percentage (0-100)',
  `discount_gold` BIGINT UNSIGNED NULL COMMENT 'Fixed gold discount amount',
  `discount_diamonds` BIGINT UNSIGNED NULL COMMENT 'Fixed diamond discount amount',
  `min_purchase_amount` BIGINT UNSIGNED NULL COMMENT 'Minimum purchase amount to qualify',
  `max_discount_amount` BIGINT UNSIGNED NULL COMMENT 'Maximum discount amount',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether promotion is active',
  `starts_at` TIMESTAMP NULL COMMENT 'Promotion start time',
  `ends_at` TIMESTAMP NULL COMMENT 'Promotion end time',
  `usage_limit_total` INT NULL COMMENT 'Total usage limit across all users',
  `usage_limit_per_user` INT NULL COMMENT 'Usage limit per user',
  `current_usage` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Current total usage count',
  `applicable_categories` JSON COMMENT 'Category IDs this promotion applies to',
  `applicable_items` JSON COMMENT 'Item IDs this promotion applies to',
  `metadata` JSON COMMENT 'Additional promotion configuration',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_active_dates` (`is_active`, `starts_at`, `ends_at`),
  INDEX `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores shop promotions and sales events';
```

#### user_promotion_usage - 用户促销使用记录表
```sql
CREATE TABLE `user_promotion_usage` (
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `promotion_id` INT NOT NULL COMMENT 'Reference to promotion',
  `usage_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of times used',
  `last_used_at` TIMESTAMP NULL COMMENT 'Last usage timestamp',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`, `promotion_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`promotion_id`) REFERENCES `promotions`(`id`) ON DELETE CASCADE,
  INDEX `idx_last_used` (`last_used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tracks user promotion usage';
```

#### wishlist - 用户心愿单表
```sql
CREATE TABLE `wishlist` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique wishlist entry ID',
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `shop_item_id` INT NOT NULL COMMENT 'Reference to shop item',
  `added_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When item was added to wishlist',
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`shop_item_id`) REFERENCES `shop_items`(`id`) ON DELETE CASCADE,
  UNIQUE KEY `idx_user_item` (`user_id`, `shop_item_id`),
  INDEX `idx_user_added` (`user_id`, `added_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores user wishlist items';
```

## 表关系图

### 核心用户系统
```
users (1) ←→ (N) user_auth_tokens
users (1) ←→ (N) friend_relationships
users (1) ←→ (N) characters
users (1) ←→ (1) user_private_spaces
```

### 家园系统
```
users (1) ←→ (N) home_items
users (1) ←→ (N) garden_plots
items (1) ←→ (N) home_items
items (1) ←→ (N) garden_plots
```

### 任务系统
```
quests (1) ←→ (N) quest_steps
quest_steps (1) ←→ (N) quest_step_responses
users (1) ←→ (N) player_quests
quests (1) ←→ (N) player_quests
```

### 商店系统
```
users (1) ←→ (1) user_currencies
users (1) ←→ (N) user_inventory
users (1) ←→ (N) purchase_history
users (1) ←→ (N) wishlist

shop_categories (1) ←→ (N) shop_categories (自引用)
shop_categories (1) ←→ (N) shop_items
items (1) ←→ (N) shop_items
shop_items (1) ←→ (N) purchase_history
shop_items (1) ←→ (N) user_purchase_limits
shop_items (1) ←→ (N) wishlist

promotions (1) ←→ (N) user_promotion_usage
```

## 索引策略

### 性能优化索引
- **用户查询**: `users.username` (UNIQUE)
- **好友关系**: `friend_relationships(user_one_id, status)`, `friend_relationships(user_two_id, status)`
- **家园物品**: `home_items.owner_user_id`, `garden_plots.owner_user_id`
- **任务进度**: `player_quests(player_id, is_completed)`
- **商店查询**: `shop_items(category_id, sort_order)`, `shop_items(is_available, is_featured)`
- **库存管理**: `user_inventory(user_id, item_id)` (UNIQUE)
- **购买历史**: `purchase_history(user_id, created_at)`

## 数据完整性

### 外键约束
- 所有用户相关表都有 `ON DELETE CASCADE` 约束
- 物品模板删除时使用 `ON DELETE RESTRICT` 防止数据丢失
- 可选引用使用 `ON DELETE SET NULL`

### 数据验证
- 枚举类型确保状态值的有效性
- JSON 字段存储复杂数据结构
- UNSIGNED 类型防止负数值
- 时间戳字段自动维护创建和更新时间

## 存储估算

### 预估数据量（10万用户）
- **users**: ~10MB
- **characters**: ~50MB
- **user_inventory**: ~500MB
- **purchase_history**: ~1GB
- **shop_items**: ~10MB
- **player_quests**: ~100MB

**总计**: 约 1.7GB（不包括索引）

## 备份策略

### 建议备份方案
1. **全量备份**: 每日凌晨进行完整数据库备份
2. **增量备份**: 每小时备份 binlog
3. **关键表实时备份**: user_currencies, purchase_history
4. **测试恢复**: 定期测试备份恢复流程

## 维护建议

### 定期维护任务
1. **清理过期数据**: 定期清理过期的认证令牌
2. **优化表结构**: 定期分析表碎片并优化
3. **监控性能**: 监控慢查询和索引使用情况
4. **数据归档**: 将历史交易数据归档到历史表
