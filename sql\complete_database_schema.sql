-- =====================================================
-- 2D Virtual World Game Database Schema
-- Complete Database Export
-- Generated: 2024
-- Total Tables: 21
-- =====================================================

-- Set character set and collation
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. CORE SYSTEM TABLES (6 tables)
-- =====================================================

-- Table: users
CREATE TABLE `users` (
  `id` BIGINT AUTO_INCREMENT COMMENT 'Unique User ID',
  `username` VARCHAR(255) NOT NULL COMMENT 'Username',
  `password_hash` VARCHAR(255) NOT NULL COMMENT 'Hashed password',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record Creation Time',
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record Last Update Time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores user accounts';

-- Table: user_auth_tokens
CREATE TABLE `user_auth_tokens` (
  `uid` BIGINT NOT NULL COMMENT 'Linked User ID from users table',
  `token` VARCHAR(255) NOT NULL COMMENT 'SHA256 Hashed Game Token',
  `expires_at` BIGINT UNSIGNED NOT NULL COMMENT 'Token Expiration Timestamp (Unix milliseconds)',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`uid`, `token`),
  UNIQUE KEY `idx_token` (`token`),
  FOREIGN KEY (`uid`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores active game login tokens';

-- Table: friend_relationships
CREATE TABLE `friend_relationships` (
  `id` BIGINT AUTO_INCREMENT,
  `user_one_id` BIGINT NOT NULL COMMENT 'Smaller user ID',
  `user_two_id` BIGINT NOT NULL COMMENT 'Larger user ID',
  `status` ENUM('pending', 'accepted', 'declined', 'blocked') NOT NULL COMMENT 'Relationship status',
  `action_user_id` BIGINT NOT NULL COMMENT 'User ID who performed the last action',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_relationship` (`user_one_id`, `user_two_id`),
  INDEX `idx_user_one_status` (`user_one_id`, `status`),
  INDEX `idx_user_two_status` (`user_two_id`, `status`),
  FOREIGN KEY (`user_one_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_two_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`action_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores friendships and block relationships';

-- Table: characters
CREATE TABLE `characters` (
  `character_id` BIGINT AUTO_INCREMENT COMMENT 'Unique character ID',
  `user_id` BIGINT NOT NULL COMMENT 'User ID owning the character',
  `character_name` VARCHAR(255) NOT NULL COMMENT 'Character name',
  `level` INT NOT NULL DEFAULT 1,
  `experience` BIGINT NOT NULL DEFAULT 0,
  `pos_x` FLOAT NOT NULL DEFAULT 0.0,
  `pos_y` FLOAT NOT NULL DEFAULT 0.0,
  `current_scene_id` VARCHAR(255) NOT NULL,
  `selected_appearance_details` JSON NOT NULL COMMENT 'Character appearance details, JSON format',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`character_id`),
  INDEX `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores player characters';

-- Table: items
CREATE TABLE `items` (
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Unique Item ID (e.g., "basic_chair", "tomato_seed")',
  `item_name` VARCHAR(255) NOT NULL COMMENT 'Display name of the item',
  `description` TEXT COMMENT 'Description of the item',
  `item_type` ENUM('furniture', 'plant', 'seed', 'tool', 'wearable', 'consumable', 'collectible') NOT NULL COMMENT 'Broad category of the item',
  `category` VARCHAR(255) COMMENT 'Specific category (e.g., "decor", "chair", "vegetable", "hat", "potion")',
  `price` INT UNSIGNED COMMENT 'Price in in-game currency (if applicable)',
  `is_stackable` BOOLEAN DEFAULT FALSE,
  `max_stack_size` INT DEFAULT 1,
  `total_growth_time_ms` INT UNSIGNED COMMENT 'For plants/seeds: total time from planting to harvestable in milliseconds',
  `num_growth_stages` INT UNSIGNED COMMENT 'For plants/seeds: number of distinct visual growth stages',
  `water_interval_ms` INT UNSIGNED COMMENT 'For plants/seeds: how often it needs watering in milliseconds',
  `harvest_details` JSON COMMENT 'For plants/seeds: JSON detailing item(s) and quantities yielded upon harvest',
  `data` JSON COMMENT 'Additional item-specific data (e.g., dimensions for furniture, effect for consumables)',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores item templates and definitions';

-- Table: user_private_spaces
CREATE TABLE `user_private_spaces` (
  `owner_user_id` BIGINT NOT NULL COMMENT 'Linked to users table ID',
  `home_background_id` VARCHAR(255) DEFAULT 'default_home_bg',
  `garden_background_id` VARCHAR(255) DEFAULT 'default_garden_bg',
  `home_access_level` ENUM('private', 'friends_only', 'public') DEFAULT 'private',
  `garden_access_level` ENUM('private', 'friends_only', 'public') DEFAULT 'private',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`owner_user_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`home_background_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT,
  FOREIGN KEY (`garden_background_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores settings for user''s private home and garden spaces';

-- =====================================================
-- 2. HOME SYSTEM TABLES (2 tables)
-- =====================================================

-- Table: home_items
CREATE TABLE `home_items` (
  `item_instance_id` VARCHAR(36) NOT NULL COMMENT 'Unique instance ID for this placed item (UUID)',
  `owner_user_id` BIGINT NOT NULL COMMENT 'User ID who owns this home and item instance',
  `item_template_id` VARCHAR(255) NOT NULL COMMENT 'Template ID from the items table',
  `pos_x` FLOAT NOT NULL,
  `pos_y` FLOAT NOT NULL,
  `rotation` FLOAT DEFAULT 0.0,
  `is_flipped` BOOLEAN DEFAULT FALSE,
  `placed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`item_instance_id`),
  INDEX `idx_owner_user_id` (`owner_user_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`item_template_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores instances of items placed in users'' homes';

-- Table: garden_plots
CREATE TABLE `garden_plots` (
  `plot_id` VARCHAR(255) NOT NULL COMMENT 'Unique ID for this plot instance (e.g., owner_user_id_plot_index)',
  `owner_user_id` BIGINT NOT NULL COMMENT 'User ID who owns this garden plot instance',
  `plot_template_id` VARCHAR(255) NOT NULL COMMENT 'Template ID for the plot itself (e.g., "small_plot")',
  `seed_id` VARCHAR(255) COMMENT 'Template ID of the seed currently planted',
  `plant_timestamp` BIGINT UNSIGNED COMMENT 'Unix timestamp (ms) when current seed was planted',
  `growth_stage` INT DEFAULT 0 COMMENT 'Current growth stage of the plant',
  `last_watered_timestamp` BIGINT UNSIGNED COMMENT 'Unix timestamp (ms) when plot was last watered',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`plot_id`),
  UNIQUE KEY `idx_owner_plot` (`owner_user_id`, `plot_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`plot_template_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT,
  FOREIGN KEY (`seed_id`) REFERENCES `items`(`item_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores instances of garden plots in users'' gardens';

-- =====================================================
-- 3. QUEST SYSTEM TABLES (4 tables)
-- =====================================================

-- Table: quests
CREATE TABLE `quests` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique quest ID',
  `title` VARCHAR(255) NOT NULL COMMENT 'Quest title',
  `accept_message_0` TEXT COMMENT 'Accept message line 0',
  `accept_message_1` TEXT COMMENT 'Accept message line 1',
  `accept_message_2` TEXT COMMENT 'Accept message line 2',
  `decline_message_0` TEXT COMMENT 'Decline message line 0',
  `reward_message_0_0` TEXT COMMENT 'Reward message line 0_0',
  `reward_message_0_1` TEXT COMMENT 'Reward message line 0_1',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores quest templates and definitions';

-- Table: quest_steps
CREATE TABLE `quest_steps` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique step ID',
  `quest_id` INT NOT NULL COMMENT 'Reference to quest',
  `step_order` INT NOT NULL COMMENT 'Order of this step in the quest',
  `goal` TEXT NOT NULL COMMENT 'Step goal description',
  `summary` TEXT COMMENT 'Step summary',
  `intro_0` TEXT COMMENT 'Intro message line 0',
  `intro_1` TEXT COMMENT 'Intro message line 1',
  `intro_2` TEXT COMMENT 'Intro message line 2',
  `intro_3` TEXT COMMENT 'Intro message line 3',
  `intro_4` TEXT COMMENT 'Intro message line 4',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`quest_id`) REFERENCES `quests`(`id`) ON DELETE CASCADE,
  INDEX `idx_quest_step_order` (`quest_id`, `step_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores quest step templates';

-- Table: quest_step_responses
CREATE TABLE `quest_step_responses` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique response ID',
  `step_id` INT NOT NULL COMMENT 'Reference to quest step',
  `npc_id` INT NOT NULL COMMENT 'NPC ID that gives this response',
  `response_type` ENUM('finished', 'unfinished') NOT NULL COMMENT 'Type of response',
  `prompt` TEXT COMMENT 'Response prompt',
  `message_0` TEXT COMMENT 'Response message line 0',
  `message_1` TEXT COMMENT 'Response message line 1',
  `message_2` TEXT COMMENT 'Response message line 2',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`step_id`) REFERENCES `quest_steps`(`id`) ON DELETE CASCADE,
  INDEX `idx_step_npc_type` (`step_id`, `npc_id`, `response_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores NPC responses for quest steps';

-- Table: player_quests
CREATE TABLE `player_quests` (
  `player_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `quest_id` INT NOT NULL COMMENT 'Reference to quest',
  `current_step_order` INT NOT NULL DEFAULT 0 COMMENT 'Current step the player is on',
  `is_completed` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether the quest is completed',
  `step_progress` JSON COMMENT 'Step-specific progress data',
  `started_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When the quest was started',
  `completed_at` TIMESTAMP NULL COMMENT 'When the quest was completed',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`player_id`, `quest_id`),
  FOREIGN KEY (`player_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`quest_id`) REFERENCES `quests`(`id`) ON DELETE CASCADE,
  INDEX `idx_player_completed` (`player_id`, `is_completed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores player quest progress';

-- =====================================================
-- 4. SHOP SYSTEM TABLES (9 tables)
-- =====================================================

-- Table: user_currencies
CREATE TABLE `user_currencies` (
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `gold_coins` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Basic currency - gold coins',
  `diamonds` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Premium currency - diamonds',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores player currency balances';

-- Table: shop_categories
CREATE TABLE `shop_categories` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique category ID',
  `name` VARCHAR(255) NOT NULL COMMENT 'Category name (e.g., "花之屋", "家园居")',
  `display_name` VARCHAR(255) NOT NULL COMMENT 'Display name for UI',
  `description` TEXT COMMENT 'Category description',
  `icon` VARCHAR(255) COMMENT 'Category icon identifier',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT 'Display order',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether category is active',
  `parent_category_id` INT NULL COMMENT 'Parent category for subcategories',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`parent_category_id`) REFERENCES `shop_categories`(`id`) ON DELETE SET NULL,
  INDEX `idx_parent_sort` (`parent_category_id`, `sort_order`),
  INDEX `idx_active_sort` (`is_active`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores shop category hierarchy';

-- Table: shop_items
CREATE TABLE `shop_items` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique shop item ID',
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Reference to item template',
  `category_id` INT NOT NULL COMMENT 'Reference to shop category',
  `name` VARCHAR(255) NOT NULL COMMENT 'Shop display name',
  `description` TEXT COMMENT 'Shop item description',
  `price_gold` BIGINT UNSIGNED NULL COMMENT 'Price in gold coins',
  `price_diamonds` BIGINT UNSIGNED NULL COMMENT 'Price in diamonds',
  `original_price_gold` BIGINT UNSIGNED NULL COMMENT 'Original price for discount display',
  `original_price_diamonds` BIGINT UNSIGNED NULL COMMENT 'Original price for discount display',
  `is_available` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether item is available for purchase',
  `is_featured` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether item is featured',
  `is_limited_time` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether item is limited time',
  `available_from` TIMESTAMP NULL COMMENT 'When item becomes available',
  `available_until` TIMESTAMP NULL COMMENT 'When item expires',
  `max_purchases_per_user` INT NULL COMMENT 'Maximum purchases per user (NULL = unlimited)',
  `total_stock` INT NULL COMMENT 'Total stock available (NULL = unlimited)',
  `remaining_stock` INT NULL COMMENT 'Remaining stock',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT 'Display order within category',
  `tags` JSON COMMENT 'Item tags for filtering and search',
  `metadata` JSON COMMENT 'Additional shop-specific metadata',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`item_id`) REFERENCES `items`(`item_id`) ON DELETE CASCADE,
  FOREIGN KEY (`category_id`) REFERENCES `shop_categories`(`id`) ON DELETE CASCADE,
  INDEX `idx_category_sort` (`category_id`, `sort_order`),
  INDEX `idx_available_featured` (`is_available`, `is_featured`),
  INDEX `idx_limited_time` (`is_limited_time`, `available_from`, `available_until`),
  INDEX `idx_item_lookup` (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores shop item listings and pricing';

-- Table: user_inventory
CREATE TABLE `user_inventory` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique inventory entry ID',
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Reference to item template',
  `quantity` BIGINT UNSIGNED NOT NULL DEFAULT 1 COMMENT 'Quantity owned',
  `is_locked` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether item is locked to prevent accidental operations',
  `is_new` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether item has "new" marker',
  `is_equipped` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether item is currently equipped/worn',
  `custom_data` JSON COMMENT 'Custom item data (enchantments, durability, etc.)',
  `acquired_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When item was acquired',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`item_id`) REFERENCES `items`(`item_id`) ON DELETE CASCADE,
  UNIQUE KEY `idx_user_item` (`user_id`, `item_id`),
  INDEX `idx_user_acquired` (`user_id`, `acquired_at`),
  INDEX `idx_user_new` (`user_id`, `is_new`),
  INDEX `idx_user_equipped` (`user_id`, `is_equipped`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores player inventory items';

-- Table: user_inventory_config
CREATE TABLE `user_inventory_config` (
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `max_capacity` INT UNSIGNED NOT NULL DEFAULT 100 COMMENT 'Maximum inventory capacity',
  `default_sort_order` ENUM('acquired_time_desc', 'acquired_time_asc', 'name_asc', 'name_desc', 'quantity_desc', 'quantity_asc', 'rarity_desc') NOT NULL DEFAULT 'acquired_time_desc' COMMENT 'Default sort order',
  `auto_sort_enabled` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether auto-sort is enabled',
  `show_new_items_first` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether to show new items first',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores user inventory configuration and preferences';

-- Table: inventory_operations_log
CREATE TABLE `inventory_operations_log` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique log entry ID',
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Reference to item template',
  `operation_type` ENUM('add', 'remove', 'use', 'sell', 'gift_send', 'gift_receive', 'equip', 'unequip', 'lock', 'unlock', 'destroy') NOT NULL COMMENT 'Type of operation',
  `quantity_change` INT NOT NULL COMMENT 'Quantity change (positive for add, negative for remove)',
  `quantity_before` BIGINT UNSIGNED NOT NULL COMMENT 'Quantity before operation',
  `quantity_after` BIGINT UNSIGNED NOT NULL COMMENT 'Quantity after operation',
  `operation_source` VARCHAR(255) COMMENT 'Source of operation (shop, quest, gift, etc.)',
  `metadata` JSON COMMENT 'Additional operation metadata',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`item_id`) REFERENCES `items`(`item_id`) ON DELETE CASCADE,
  INDEX `idx_user_operation` (`user_id`, `operation_type`, `created_at`),
  INDEX `idx_item_operation` (`item_id`, `operation_type`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Logs all inventory operations for auditing and analytics';

-- Table: purchase_history
CREATE TABLE `purchase_history` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique transaction ID',
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `shop_item_id` INT NOT NULL COMMENT 'Reference to shop item',
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Reference to item template',
  `quantity` INT UNSIGNED NOT NULL DEFAULT 1 COMMENT 'Quantity purchased',
  `price_gold` BIGINT UNSIGNED NULL COMMENT 'Gold coins spent',
  `price_diamonds` BIGINT UNSIGNED NULL COMMENT 'Diamonds spent',
  `transaction_type` ENUM('purchase', 'gift_sent', 'gift_received', 'refund') NOT NULL DEFAULT 'purchase',
  `gift_recipient_id` BIGINT NULL COMMENT 'If gift, recipient user ID',
  `gift_sender_id` BIGINT NULL COMMENT 'If gift received, sender user ID',
  `purchase_source` VARCHAR(255) NULL COMMENT 'Source of purchase (shop, event, etc.)',
  `metadata` JSON COMMENT 'Additional transaction metadata',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`shop_item_id`) REFERENCES `shop_items`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`item_id`) REFERENCES `items`(`item_id`) ON DELETE CASCADE,
  FOREIGN KEY (`gift_recipient_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`gift_sender_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  INDEX `idx_user_date` (`user_id`, `created_at`),
  INDEX `idx_transaction_type` (`transaction_type`),
  INDEX `idx_gift_recipient` (`gift_recipient_id`),
  INDEX `idx_gift_sender` (`gift_sender_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores purchase transaction history';

-- Table: user_purchase_limits
CREATE TABLE `user_purchase_limits` (
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `shop_item_id` INT NOT NULL COMMENT 'Reference to shop item',
  `purchase_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of times purchased',
  `last_purchase_at` TIMESTAMP NULL COMMENT 'Last purchase timestamp',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`, `shop_item_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`shop_item_id`) REFERENCES `shop_items`(`id`) ON DELETE CASCADE,
  INDEX `idx_last_purchase` (`last_purchase_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tracks user purchase limits for limited items';

-- Table: promotions
CREATE TABLE `promotions` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique promotion ID',
  `name` VARCHAR(255) NOT NULL COMMENT 'Promotion name',
  `description` TEXT COMMENT 'Promotion description',
  `type` ENUM('discount_percentage', 'discount_fixed', 'bundle', 'free_gift', 'buy_x_get_y') NOT NULL COMMENT 'Promotion type',
  `discount_percentage` DECIMAL(5,2) NULL COMMENT 'Discount percentage (0-100)',
  `discount_gold` BIGINT UNSIGNED NULL COMMENT 'Fixed gold discount amount',
  `discount_diamonds` BIGINT UNSIGNED NULL COMMENT 'Fixed diamond discount amount',
  `min_purchase_amount` BIGINT UNSIGNED NULL COMMENT 'Minimum purchase amount to qualify',
  `max_discount_amount` BIGINT UNSIGNED NULL COMMENT 'Maximum discount amount',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether promotion is active',
  `starts_at` TIMESTAMP NULL COMMENT 'Promotion start time',
  `ends_at` TIMESTAMP NULL COMMENT 'Promotion end time',
  `usage_limit_total` INT NULL COMMENT 'Total usage limit across all users',
  `usage_limit_per_user` INT NULL COMMENT 'Usage limit per user',
  `current_usage` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Current total usage count',
  `applicable_categories` JSON COMMENT 'Category IDs this promotion applies to',
  `applicable_items` JSON COMMENT 'Item IDs this promotion applies to',
  `metadata` JSON COMMENT 'Additional promotion configuration',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_active_dates` (`is_active`, `starts_at`, `ends_at`),
  INDEX `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores shop promotions and sales events';

-- Table: user_promotion_usage
CREATE TABLE `user_promotion_usage` (
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `promotion_id` INT NOT NULL COMMENT 'Reference to promotion',
  `usage_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of times used',
  `last_used_at` TIMESTAMP NULL COMMENT 'Last usage timestamp',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`, `promotion_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`promotion_id`) REFERENCES `promotions`(`id`) ON DELETE CASCADE,
  INDEX `idx_last_used` (`last_used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tracks user promotion usage';

-- Table: wishlist
CREATE TABLE `wishlist` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Unique wishlist entry ID',
  `user_id` BIGINT NOT NULL COMMENT 'Reference to user ID',
  `shop_item_id` INT NOT NULL COMMENT 'Reference to shop item',
  `added_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When item was added to wishlist',
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`shop_item_id`) REFERENCES `shop_items`(`id`) ON DELETE CASCADE,
  UNIQUE KEY `idx_user_item` (`user_id`, `shop_item_id`),
  INDEX `idx_user_added` (`user_id`, `added_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores user wishlist items';

-- Table: room_registry
CREATE TABLE `room_registry` (
  `room_id` VARCHAR(255) NOT NULL COMMENT 'Dynamic room ID (e.g., home_123, garden_456)',
  `room_type` ENUM('home', 'garden', 'quest') NOT NULL COMMENT 'Type of room',
  `owner_uid` BIGINT NOT NULL COMMENT 'User ID who owns this room',
  `access_level` ENUM('private', 'friends_only', 'public') NOT NULL DEFAULT 'private' COMMENT 'Room access level',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether room is currently active',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When room was first created',
  `last_accessed` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last time room was accessed',
  PRIMARY KEY (`room_id`),
  FOREIGN KEY (`owner_uid`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  INDEX `idx_owner_type` (`owner_uid`, `room_type`),
  INDEX `idx_type_access` (`room_type`, `access_level`),
  INDEX `idx_active_accessed` (`is_active`, `last_accessed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Registry for dynamic room management';

-- Table: notifications
CREATE TABLE `notifications` (
  `id` VARCHAR(255) NOT NULL COMMENT 'Unique notification ID',
  `type` ENUM('friend_request_sent', 'friend_request_received', 'friend_request_accepted', 'friend_request_declined', 'friend_removed', 'user_blocked', 'user_unblocked', 'user_online', 'user_offline', 'room_invitation', 'system_message') NOT NULL COMMENT 'Type of notification',
  `from_user_id` BIGINT NULL COMMENT 'User ID who triggered the notification (can be null for system messages)',
  `to_user_id` BIGINT NOT NULL COMMENT 'User ID who should receive the notification',
  `title` VARCHAR(255) NOT NULL COMMENT 'Notification title',
  `message` TEXT NOT NULL COMMENT 'Notification message content',
  `data` JSON NULL COMMENT 'Additional notification data as JSON',
  `is_read` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether the notification has been read',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When notification was created',
  `expires_at` TIMESTAMP NOT NULL COMMENT 'When notification expires and can be deleted',
  PRIMARY KEY (`id`),
  FOREIGN KEY (`from_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`to_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  INDEX `idx_to_user_created` (`to_user_id`, `created_at`),
  INDEX `idx_to_user_read` (`to_user_id`, `is_read`),
  INDEX `idx_expires_at` (`expires_at`),
  INDEX `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Real-time notifications for users';

-- Table: outfit_templates
CREATE TABLE `outfit_templates` (
  `id` VARCHAR(255) NOT NULL COMMENT 'Unique outfit template ID',
  `name` VARCHAR(255) NOT NULL COMMENT 'Outfit template name',
  `description` TEXT NULL COMMENT 'Outfit description',
  `category` VARCHAR(100) NOT NULL COMMENT 'Outfit category (casual, formal, fantasy, etc.)',
  `rarity` ENUM('common', 'uncommon', 'rare', 'epic', 'legendary') NOT NULL DEFAULT 'common' COMMENT 'Outfit rarity',
  `is_default` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether this is a default outfit',
  `is_purchasable` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether this outfit can be purchased',
  `price_gold` INT NOT NULL DEFAULT 0 COMMENT 'Price in gold coins',
  `price_diamonds` INT NOT NULL DEFAULT 0 COMMENT 'Price in diamonds',
  `unlock_level` INT NOT NULL DEFAULT 1 COMMENT 'Required level to unlock',
  `outfit_data` JSON NOT NULL COMMENT 'Complete outfit data as JSON',
  `preview_image` VARCHAR(500) NULL COMMENT 'URL to preview image',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When template was created',
  PRIMARY KEY (`id`),
  INDEX `idx_category` (`category`),
  INDEX `idx_rarity` (`rarity`),
  INDEX `idx_purchasable` (`is_purchasable`),
  INDEX `idx_unlock_level` (`unlock_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Predefined outfit templates';

-- Table: user_outfits
CREATE TABLE `user_outfits` (
  `id` VARCHAR(255) NOT NULL COMMENT 'Unique outfit save ID',
  `user_id` BIGINT NOT NULL COMMENT 'User ID who owns this outfit',
  `outfit_name` VARCHAR(255) NOT NULL COMMENT 'User-defined outfit name',
  `outfit_data` JSON NOT NULL COMMENT 'Complete outfit data as JSON',
  `is_current` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether this is the current active outfit',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When outfit was saved',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'When outfit was last updated',
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  INDEX `idx_user_current` (`user_id`, `is_current`),
  INDEX `idx_user_updated` (`user_id`, `updated_at`),
  UNIQUE KEY `unique_user_outfit_name` (`user_id`, `outfit_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User saved outfits';



-- =====================================================
-- RESTORE FOREIGN KEY CHECKS
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- DATABASE SCHEMA SUMMARY
-- =====================================================
-- Total Tables: 21
-- Core System: 6 tables
-- Home System: 2 tables
-- Quest System: 4 tables
-- Shop System: 9 tables
-- =====================================================
