"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseTimeoutError = exports.DatabaseConstraintError = exports.DatabaseTransactionError = exports.DatabaseQueryError = exports.DatabaseConnectionError = exports.DatabaseError = void 0;
const BaseError_1 = require("./BaseError");
/**
 * Database related errors
 */
class DatabaseError extends BaseError_1.BaseError {
    constructor(message = 'Database operation failed', details) {
        super('DatabaseError', 500, message, true, details);
    }
}
exports.DatabaseError = DatabaseError;
/**
 * Database connection error
 */
class DatabaseConnectionError extends DatabaseError {
    constructor(message = 'Failed to connect to database') {
        super(message);
        this.name = 'DatabaseConnectionError';
    }
}
exports.DatabaseConnectionError = DatabaseConnectionError;
/**
 * Database query error
 */
class DatabaseQueryError extends DatabaseError {
    constructor(query, originalError) {
        const message = `Database query failed: ${originalError?.message || 'Unknown error'}`;
        super(message, { query, originalError: originalError?.message });
        this.name = 'DatabaseQueryError';
    }
}
exports.DatabaseQueryError = DatabaseQueryError;
/**
 * Database transaction error
 */
class DatabaseTransactionError extends DatabaseError {
    constructor(message = 'Database transaction failed', details) {
        super(message, details);
        this.name = 'DatabaseTransactionError';
    }
}
exports.DatabaseTransactionError = DatabaseTransactionError;
/**
 * Database constraint violation error
 */
class DatabaseConstraintError extends DatabaseError {
    constructor(constraint, table) {
        let message = `Database constraint violation: ${constraint}`;
        if (table) {
            message += ` in table '${table}'`;
        }
        super(message, { constraint, table });
        this.name = 'DatabaseConstraintError';
    }
}
exports.DatabaseConstraintError = DatabaseConstraintError;
/**
 * Database timeout error
 */
class DatabaseTimeoutError extends DatabaseError {
    constructor(timeout) {
        const message = `Database operation timed out after ${timeout}ms`;
        super(message, { timeout });
        this.name = 'DatabaseTimeoutError';
    }
}
exports.DatabaseTimeoutError = DatabaseTimeoutError;
